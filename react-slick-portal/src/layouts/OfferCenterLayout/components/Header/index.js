import React, { useEffect } from 'react';
import router from 'umi/router'
import classnames from 'classnames';
import { connect } from 'dva';
import NavList from './NavList';
import style from './Header.module.less';
import NavSearch from './NavSearch';

const Header = props => {
  const { offerMenuTree, dispatch } = props;

  useEffect(() => {
    dispatch({
      type: 'offer/getOfferMenuTree',
    });
  }, [])

  return (
    <>
      <div className={classnames(style.wrap, props.fixed ? style.fixed : null)} style={{ display: 'none' }}>
        <div className={style.left}>
          <span
            className={style.subTitle}
            onClick={() => {
              router.push('/offerCenter');
              dispatch({
                type: 'offer/setCurrent',
                payload: 0,
              });
              dispatch({
                type: 'offer/setSubCurrentType',
                payload: '1100',
              });
            }}
          >
            产品大厅
          </span>
          <NavList data={offerMenuTree || []} />
        </div>
        <div className={style.right}>
          <NavSearch />
        </div>
      </div>
    </>
  );
};

export default connect(({ offer }) => ({
  offerMenuTree: offer.offerMenuTree,
}))(Header);
