import React from 'react';
import { Popover } from 'antd';
import { connect } from 'dva';
import style from './Header.module.less';
import MulitpleMenu from './NavDropdown/multiple';

const NavList = props => {
  const { offerMenuTree } = props;

  return (
    <ul className={style.navList}>
      <Popover
        overlayClassName={style.popover}
        placement="bottomLeft"
        content={<MulitpleMenu list={offerMenuTree || []} />}
      >
        <li>
          <span>产品列表</span>
        </li>
      </Popover>
    </ul>
  );
};

export default connect(({ offer }) => ({
  offerMenuTree: offer.offerMenuTree,
}))(NavList);
