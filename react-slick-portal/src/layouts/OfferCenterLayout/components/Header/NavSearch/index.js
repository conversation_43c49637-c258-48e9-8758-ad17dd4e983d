import React, { useEffect, useState } from 'react';
import { Input, AutoComplete } from 'antd';
import { router } from 'umi';

import { connect } from 'dva';
import style from './Search.module.less';

const { Search } = Input;

/**
 * 导航顶部搜索组件
 * @param {props} props
 */
const NavSearch = props => {
  const { searchHis, dispatch } = props;
  const [dataSource, setDataSource] = useState([]);

  const unique = arr => {
    return arr.filter((item, index, arr) => {
      return arr.indexOf(item, 0) === index;
    });
  }

  const getDataSource = () => {
    dispatch({
      type: 'offer/getSearchHis',
    });
    const tmpDS = [];
    searchHis.forEach(item => {
      if (item.recentContent) {
        tmpDS.push(item.recentContent);
      }
    })
    setDataSource(unique(tmpDS));
  };

  useEffect(() => {
    dispatch({
      type: 'offer/getSearchHis',
    });
  }, []);

  const onSearch = v => {
    if (v !== '') {
      router.push(`/offerCenter/search?searchkey=${v}`);
      dispatch({
        type: 'offer/saveCurSearchKey',
        payload: v,
      });
    }
  };

  return (
    <div className={style.wrap}>
      <div className={style.search}>
        <AutoComplete
          dataSource={dataSource}
          defaultActiveFirstOption={false}
          onSelect={v => {onSearch(v)}}
          backfill
        >
          <Search
            placeholder="产品搜索"
            style={{ width: 641, cursor: 'pointer' }}
            onSearch={v => {onSearch(v)}}
            onClick={getDataSource}
          />
        </AutoComplete>
      </div>
    </div>
  );
};

export default connect(({ offer }) => ({
  searchHis: offer.searchHis,
}))(NavSearch);
