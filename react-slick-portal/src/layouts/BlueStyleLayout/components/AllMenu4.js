import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'dva';
import { Menu, Icon, Modal, Empty, Popover, Row, Col, Divider } from 'antd';
import Link from 'umi/link';
import memoizeOne from 'memoize-one';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import sortBy from 'lodash/sortBy';
import classNames from 'classnames';
import RGL, { WidthProvider } from 'react-grid-layout';
// import useComponentSize from '@rehooks/component-size';
import findIndex from 'lodash/findIndex';
import find from 'lodash/find';
import mapping from '@/services/menu';
import request from '@/utils/request';
import {
  getFirstLevelMenu,
  getSecondLevelMenuInfo,
  getThirdLevelMenuInfo,
  getAllMenuByLetter,
  quickSearch,
} from './utils';
import { isFloat, getItem, getFinalUrl } from '@/utils/utils';
import styles from '../less/header.less';
import styles2 from '../less/allMenu4.less';

const { MENU_TYPE_LEAF, MENU_TYPE_DIR } = mapping;
const { sessionId, userInfo } = getItem('user');

const ReactGridLayout = WidthProvider(RGL);

// const _quickSearch = memoizeOne(quickSearch, isEqual);
const _getFirstLevelMenu = memoizeOne(getFirstLevelMenu, isEqual);
const _getAllMenuByLetter = memoizeOne(getAllMenuByLetter, isEqual);
const _getSecondLevelMenuInfo = memoizeOne(getSecondLevelMenuInfo, isEqual);
const _getThirdLevelMenuInfo = memoizeOne(getThirdLevelMenuInfo, isEqual);
// 整数不变，浮点数保留小数点2位
function format(val) {
  return isFloat(val) ? parseFloat(parseFloat(val).toFixed(2)) : parseFloat(val);
}

function AllMenu4({ hideAllMenu, menu: { all, appSysCode, taskSysCode, allByLetter } }) {
  // const ref = useRef(null);
  // const size = useComponentSize(ref);
  // const { width, height } = size;
  const [visible, setVisible] = useState(false);
  const [modalUrl, setModalUrl] = useState('');

  const [activeMenuId2, setActiveMenuId2] = useState();
  const [menuLel3, setMenuLel3] = useState([]);
  const [menuLetter, setMenuLetter] = useState([]);

  function serialize(all, appSysCode, taskSysCode) {
    const raw = cloneDeep(all);
    const result = [];
    const menuLel1 = _getFirstLevelMenu(raw, appSysCode, taskSysCode);
    menuLel1.forEach(item1 => {
      if (item1.menuType === `${MENU_TYPE_DIR}`) {
        let children = _getSecondLevelMenuInfo(raw, item1.menuId);

        if (Array.isArray(children) && children.length > 0) {
          children = children.map(item2 => {
            const grandson = _getThirdLevelMenuInfo(raw, item2.menuId);
            if (Array.isArray(grandson) && grandson.length > 0) {
              item2.children = grandson;
            }
            return item2;
          });

          item1.children = children;
        }
      }
      result.push(item1);
    });
    return result;
  }

  const menuArr = serialize(all, appSysCode, taskSysCode);

  // 获取
  const setFindMenuIdByType = (menuLvl4, type) => {
    if (menuLvl4 && menuLvl4.length > 0) {
      for (let i = 0; i < menuLvl4.length; i++) {
        if (parseInt(menuLvl4[i].menuType, 10) == type) {
          setActiveMenuId2(menuLvl4[i].menuId);
          // break;
          return menuLvl4[i].menuId;
        }
      }
    }
  };

  const multiColumnLayout4 = menuLvl4 => (
    <div className="topbar-dropdown-box4">
      <div className="y-row">
        {menuLvl4.length > 0 ? (
          <div className="y-col">
            <div className="y-col-body-menu4">
              <ul className="y-list">
                {menuLvl4.map(menu => (
                  <li
                    className={classNames({ active: menu.menuId === activeMenuId2 })}
                    // className={classNames({
                    //   active: menu.menuId === findMenuIdByType(menuLvl4, 1000),
                    // })}
                    key={JSON.stringify(menu)}
                    onMouseEnter={e => {
                      if (parseInt(menu.menuType, 10) == 1000) {
                        handleThird(e, menu);
                      }
                    }}
                  >
                    {renderMenu(menu, hideAllMenu)}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ) : null}

        <div className="letter-panel">
          <div className="letter-category clearfix">
            <ul className="y-list">
              {menuLetter.length > 0
                ? menuLetter.map(group => (
                  <Col span={8} key={JSON.stringify(group)}>
                    {group.menus.length === 0 ? null : (
                      <dl key={group.title}>
                        <dt className="letter-title">
                          <span>{group.title}</span>
                        </dt>
                        <dd>
                          <ul>
                            {group.menus.map(member => (
                              <li className="ng-item" key={member.menuId}>
                                {renderMenu(member, hideAllMenu)}
                              </li>
                            ))}
                          </ul>
                        </dd>
                      </dl>
                    )}
                  </Col>
                ))
                : null}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  function handleThird(e, menu) {
    e.preventDefault();
    setActiveMenuId2(parseInt(menu.menuId, 10));
    setMenuLel3(_getThirdLevelMenuInfo(all, menu.menuId));
    // 1100 表示叶子节点
    if (parseInt(menu.menuType, 10) != 1100) {
      groupMenuByLetter(menu.menuId);
    }
  }

  function handleThirdById(menuId) {
    setMenuLel3(_getThirdLevelMenuInfo(all, menuId));
    groupMenuByLetter(menuId);
  }

  function groupMenuByLetter(menuId) {
    const menuList = _getThirdLevelMenuInfo(all, menuId);
    // 创建首字母分组
    const letterReg = /^[A-Z]$/;
    const list = new Array();

    menuList.map(menuInfo => {
      list['#'] = new Array();
      let letter = menuInfo.firstLetter.substr(0, 1).toUpperCase();
      // 是否 大写 英文 字母
      if (!letterReg.test(letter)) {
        letter = '#';
      }

      // 创建 字母 分组
      if (!(letter in list)) {
        list[letter] = new Array();
      }

      // 字母 分组 添加 数据
      list[letter].push(menuInfo);
    });

    // 转换 格式 进行 排序；
    const resault = new Array();
    for (const key in list) {
      resault.push({
        letter: key,
        list: list[key],
      });
    }
    resault.sort((x, y) => x.letter.charCodeAt(0) - y.letter.charCodeAt(0));

    // # 号分组 放最后
    const lastArr = resault[0];
    resault.splice(0, 1);
    resault.push(lastArr);

    // 转换 数据 格式
    const jsonSortMap = [];
    for (let i = 0; i < resault.length; i++) {
      const jsonSort = {
        title: resault[i].letter,
        menus: resault[i].list,
      };
      jsonSortMap.push(jsonSort);
    }

    setMenuLetter(jsonSortMap);
  }

  // 负责渲染级联菜单
  function renderMenu(menu, hideAllMenu) {
    // 1100 表示叶子节点
    if (parseInt(menu.menuType, 10) === 1100) {
      if (menu.menuOpenMode !== '1') {
        return (
          <a
            onClick={() => openMenu(menu)}
            className={classNames('text-ellipsis')}
            key={menu.menuId}
          >
            {menu.menuName}
          </a>
        );
      }

      return (
        <Link
          to={getFinalUrl(menu)}
          onClick={() => hideAllMenu(false)}
          title={menu.menuName}
          className="text-ellipsis"
        >
          {menu.menuName}
        </Link>
      );
    }
    return (
      <a title={menu.menuName} href="#" className="text-ellipsis">
        {menu.menuName}
        <Icon type="right" />
      </a>
    );
  }

  /**
   * 单列布局，适用于没有3级菜单
   * @param {*} childrens
   */
  const singleColumnLayout = menuLel2 => (
    <div>
      <Menu className={styles.menu}>
        {menuLel2.map(child => (
          <Menu.Item key={child.menuId}>
            {child.menuOpenMode !== '1' ? (
              <a
                onClick={() => openMenu(child)}
                className={classNames(styles2.item, 'text-ellipsis')}
              >
                {child.menuName}
              </a>
            ) : (
              <Link
                className={classNames('text-ellipsis')}
                to={getFinalUrl(child)}
                onClick={() => hideAllMenu(false)}
              >
                {child.menuName}
              </Link>
            )}
          </Menu.Item>
        ))}
      </Menu>
    </div>
  );

  function handleWidth(menu1) {
    if (!find(menu1.children, 'children')) {
      return 220;
    }
    // if (menu1.children.length === 2) {
    //   return 220 * 2;
    // }
    return 220 * 3;
  }

  async function openMenu(menu) {
    // var MENU_OPEN_MODE_WORK_BENCH_LABEL = "1"; //工作台标签页打开
    // var MENU_OPEN_MODE_POPUP = "2"; //弹出框打开
    // var MENU_OPEN_MODE_NEW_BROWSER = "3"; //新开浏览器打开
    // var MENU_OPEN_MODE_BROWSER_LABEL = "4"; //浏览器新标签打开
    let menuUrl = menu.urlAddr.replace('[iframe]', '');

    if (menuUrl.indexOf('?') === -1) {
      menuUrl += `?bss3SessionId=${sessionId}`;
    } else {
      menuUrl += `&bss3SessionId=${sessionId}`;
    }

    if (menuUrl.indexOf('{SYS_USER_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_USER_ID}', userInfo.userCode);
    }
    if (menuUrl.indexOf('{SYS_ORG_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_ORG_ID}', userInfo.userOrgId);
    }

    // 采用SSO单点校验方式进行菜单打开
    if (menuUrl.indexOf('ssoType=1') !== -1) {
      const urlParams = parseUrlParams(menuUrl);

      // 菜单地址SSO加密，拼接返回的signString和singTimestamp
      const response = await request(
        `/orgauth/SystemInfoController/calcSign.do?systemInfoId=${menu.systemCode}`,
        {
          method: 'POST',
          data: urlParams.params,
        }
      );

      if (response && response.resultCode === '0') {
        menuUrl = `${menuUrl}&signString=${response.signString}&signTimestamp=${response.signTimestamp}`;
      }
    }

    if (menu.menuOpenMode === '4') {
      window.open(menuUrl);
    } else if (menu.menuOpenMode === '3') {
      window.open(menuUrl, menu.menuName, 'width=800,height=600,location=yew,toolbar=yes');
    } else {
      setVisible(true);
      setModalUrl(menuUrl);
      hideAllMenu(false);
    }
  }

  /**
   * 解析url参数，返回{
   *    url:不含?号的url地址,
   *    oriUrl:原url,
   *    params: ?号后面的参数
   * }
   * @param url
   * @return {{}}
   */
  function parseUrlParams(url) {
    const urlArr = url.split('?');
    const resp = {};
    resp.oriUrl = url;
    const options = {};
    if (urlArr.length > 1) {
      resp.url = urlArr[0];
      const parArr = urlArr[1].split('&');
      for (let i = 0; i < parArr.length; i++) {
        const par = parArr[i].split('=');
        if (par[1]) {
          options[par[0]] = decodeURIComponent(par[1]);
        } else {
          options[par[0]] = '';
        }
      }
    } else {
      resp.url = url;
    }
    resp.params = options;
    return resp;
  }

  useEffect(() => {
    // console.log(width);
  });

  return (
    <div className={styles2.navMenu}>
      {menuArr.length > 0 &&
        menuArr.map(menu1 => (
          <Popover
            key={menu1.menuId}
            destroyPopupOnHide
            placement={!find(menu1.children, 'children') ? 'bottom' : 'bottomLeft'}
            overlayStyle={{
              width: handleWidth(menu1),
            }}
            content={
              !find(menu1.children, 'children')
                ? singleColumnLayout(menu1.children, hideAllMenu)
                : multiColumnLayout4(menu1.children, hideAllMenu)
            }
            trigger="hover"
            getPopupContainer={trigger => trigger.parentNode}
          >
            <span
              className={classNames(styles.action)}
              onMouseEnter={_ => {
                if (find(menu1.children, 'children')) {
                  const menuId = setFindMenuIdByType(menu1.children, 1000);
                  handleThirdById(menuId);
                }
              }}
            >
              {menu1.menuName} <Icon type="down" className={styles.arrow} />
            </span>
          </Popover>
        ))}

      <Modal visible={visible} onCancel={() => setVisible(false)} footer={null} width="800px">
        <iframe src={modalUrl} height="600px" width="752px" />
      </Modal>
    </div>
  );
}

export default connect(({ menu }) => ({
  menu,
}))(AllMenu4);
