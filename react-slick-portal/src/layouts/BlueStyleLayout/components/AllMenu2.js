import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'dva';
import { Menu, Icon, Modal, Empty, Popover, Row, Col, Divider } from 'antd';
import Link from 'umi/link';
import memoizeOne from 'memoize-one';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import sortBy from 'lodash/sortBy';
import classNames from 'classnames';
import RGL, { WidthProvider } from 'react-grid-layout';
// import useComponentSize from '@rehooks/component-size';
import findIndex from 'lodash/findIndex';
import find from 'lodash/find';
import mapping from '@/services/menu';
import {
  getFirstLevelMenu,
  getSecondLevelMenuInfo,
  getThirdLevelMenuInfo,
  getAllMenuByLetter,
  quickSearch,
} from './utils';
import { isFloat, getItem, getFinalUrl } from '@/utils/utils';
import styles from '../less/header.less';
import styles2 from '../less/allMenu2.less';

const { MENU_TYPE_LEAF, MENU_TYPE_DIR } = mapping;
const { sessionId, userInfo } = getItem('user');

const ReactGridLayout = WidthProvider(RGL);

// const _quickSearch = memoizeOne(quickSearch, isEqual);
const _getFirstLevelMenu = memoizeOne(getFirstLevelMenu, isEqual);
// const _getAllMenuByLetter = memoizeOne(getAllMenuByLetter, isEqual);
const _getSecondLevelMenuInfo = memoizeOne(getSecondLevelMenuInfo, isEqual);
const _getThirdLevelMenuInfo = memoizeOne(getThirdLevelMenuInfo, isEqual);
// 整数不变，浮点数保留小数点2位
function format(val) {
  return isFloat(val) ? parseFloat(parseFloat(val).toFixed(2)) : parseFloat(val);
}

function AllMenu2({ hideAllMenu, menu: { all, appSysCode, taskSysCode } }) {
  // const ref = useRef(null);
  // const size = useComponentSize(ref);
  // const { width, height } = size;
  const [visible, setVisible] = useState(false);
  const [modalUrl, setModalUrl] = useState('');

  function serialize(all, appSysCode, taskSysCode) {
    const raw = cloneDeep(all);
    const result = [];
    const menuLel1 = _getFirstLevelMenu(raw, appSysCode, taskSysCode);
    menuLel1.forEach(item1 => {
      if (item1.menuType === `${MENU_TYPE_DIR}`) {
        let children = sortBy(_getSecondLevelMenuInfo(raw, item1.menuId), 'children');

        if (Array.isArray(children) && children.length > 0) {
          children = children.map(item2 => {
            const grandson = sortBy(_getThirdLevelMenuInfo(raw, item2.menuId), 'children');
            if (Array.isArray(grandson) && grandson.length > 0) {
              item2.children = grandson;
            }
            return item2;
          });

          item1.children = children;
        }
      }
      result.push(item1);
    });
    return result;
  }

  const menuArr = serialize(all, appSysCode, taskSysCode);
  // console.log(menu);

  /**
   * 多列布局(2或3列)，适用于有3级菜单的
   * @param {object[]} menuLvl2
   */
  const multiColumnLayout = menuLvl2 => {
    const cols = menuLvl2.length <= 3 ? menuLvl2.length : 3;
    const w = 1;
    const generateLayout = (cols, w) => menuLvl2.map((item, i) => {
        const y = item.children ? (item.children.length * 32 + 27) / 10 : 32 / 10;
        return {
          x: i % cols,
          y: Infinity,
          w,
          h: format(y),
          i: i.toString(),
        };
      });
    // if (findIndex(menuLvl2, { menuId: 2780365 }) !== -1) {
    //   console.log(generateLayout(cols, w));
    // }

    return (
      <ReactGridLayout
        layout={generateLayout(cols, w)}
        useCSSTransforms={false}
        isDraggable={false}
        isResizable={false}
        margin={[0, 0]}
        // compactType="vertical"
        cols={cols}
        rowHeight={10}
      >
        {menuLvl2.map((child, m) => (
          <div className={styles2.group} key={m}>
            {child.children ? (
              <>
                <div className={styles2.title}>{child.menuName}</div>
              </>
              ) : null}
            {/* <Divider className={styles2.split} /> */}
            {Array.isArray(child.children) ? (
                child.children.map(menu3 => {
                  // var MENU_OPEN_MODE_WORK_BENCH_LABEL = "1"; //工作台标签页打开
                  // var MENU_OPEN_MODE_POPUP = "2"; //弹出框打开
                  // var MENU_OPEN_MODE_NEW_BROWSER = "3"; //新开浏览器打开
                  // var MENU_OPEN_MODE_BROWSER_LABEL = "4"; //浏览器新标签打开

                  if (menu3.menuOpenMode !== '1') {
                    return (
                      <a
                        onClick={() => openMenu(menu3)}
                        className={classNames(styles2.item, 'text-ellipsis')}
                        key={menu3.menuId}
                      >
                        {menu3.menuName}
                      </a>
                    );
                  }
                    return (
                      <Link
                        key={menu3.menuId}
                        className={classNames(styles2.item, 'text-ellipsis')}
                        to={getFinalUrl(menu3)}
                        onClick={() => hideAllMenu(false)}
                      >
                        {menu3.menuName}
                      </Link>
                    );
                })
              ) : child.menuOpenMode !== '1' ? (
                <a
                  onClick={() => openMenu(child)}
                  className={classNames(styles2.item, 'text-ellipsis')}
                  key={child.menuId}
                >
                  {child.menuName}
                </a>
              ) : (
                <Link
                  key={child.menuId}
                  className={classNames(styles2.item, 'text-ellipsis')}
                  to={getFinalUrl(child)}
                  onClick={() => hideAllMenu(false)}
                >
                  {child.menuName}
                </Link>
              )}
          </div>
          ))}
      </ReactGridLayout>
    );
  };

  /**
   * 单列布局，适用于没有3级菜单
   * @param {*} childrens
   */
  const singleColumnLayout = menuLel2 => (
    <div>
      <Menu className={styles.menu}>
        {menuLel2.map(child => (
          <Menu.Item key={child.menuId}>
            {child.menuOpenMode !== '1' ? (
              <a
                onClick={() => openMenu(child)}
                className={classNames(styles2.item, 'text-ellipsis')}
              >
                {child.menuName}
              </a>
                ) : (
                  <Link
                    className={classNames('text-ellipsis')}
                    to={getFinalUrl(child)}
                    onClick={() => hideAllMenu(false)}
                  >
                    {child.menuName}
                  </Link>
                )}
          </Menu.Item>
            ))}
      </Menu>
    </div>
    );

  function handleWidth(menu1) {
    if (!find(menu1.children, 'children')) {
      return 184;
    }
    if (menu1.children.length === 2) {
      return 184 * 2;
    }
    return 184 * 3;
  }

  async function openMenu(menu) {
    // var MENU_OPEN_MODE_WORK_BENCH_LABEL = "1"; //工作台标签页打开
    // var MENU_OPEN_MODE_POPUP = "2"; //弹出框打开
    // var MENU_OPEN_MODE_NEW_BROWSER = "3"; //新开浏览器打开
    // var MENU_OPEN_MODE_BROWSER_LABEL = "4"; //浏览器新标签打开
    let menuUrl = menu.urlAddr.replace('[iframe]', '');
    if (menuUrl.indexOf('?') === -1) {
      menuUrl += `?bss3SessionId=${sessionId}`;
    } else {
      menuUrl += `&bss3SessionId=${sessionId}`;
    }

    if (menuUrl.indexOf('{SYS_USER_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_USER_ID}', userInfo.userCode);
    }
    if (menuUrl.indexOf('{SYS_ORG_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_ORG_ID}', userInfo.userOrgId);
    }

    // 采用SSO单点校验方式进行菜单打开
    if (menuUrl.indexOf('ssoType=1') !== -1) {
      // let urlParams = parseUrlParams(menuUrl);
      const urlParams = {
        bssSessionId: sessionId,
      };
      // 菜单地址SSO加密，拼接返回的signString和singTimestamp
      const response = await request(
        `orgauth/SystemInfoController/calcSign.do?systemInfoId=${menu.systemCode}`,
        {
          method: 'POST',
          data: urlParams,
        }
      );

      if (response && response.resultCode === '0') {
        menuUrl = `${menuUrl}&signString=${response.signString}&signTimestamp=${response.signTimestamp}`;
      }
    }

    if (menu.menuOpenMode === '4') {
      window.open(menuUrl);
    } else if (menu.menuOpenMode === '3') {
      window.open(menuUrl, menu.menuName, 'width=800,height=600,location=yew,toolbar=yes');
    } else {
      setVisible(true);
      setModalUrl(menuUrl);
      hideAllMenu(false);
    }
  }

  /**
   * 解析url参数，返回{
   *    url:不含?号的url地址,
   *    oriUrl:原url,
   *    params: ?号后面的参数
   * }
   * @param url
   * @return {{}}
   */
  function parseUrlParams(url) {
    const urlArr = url.split('?');
    const resp = {};
    resp.oriUrl = url;
    const options = {};
    if (urlArr.length > 1) {
      resp.url = urlArr[0];
      const parArr = urlArr[1].split('&');
      for (let i = 0; i < parArr.length; i++) {
        const par = parArr[i].split('=');
        if (par[1]) {
          options[par[0]] = decodeURIComponent(par[1]);
        } else {
          options[par[0]] = '';
        }
      }
    } else {
      resp.url = url;
    }
    resp.params = options;
    return resp;
  }

  useEffect(() => {
    // console.log(width);
  });

  return (
    <div className={styles2.navMenu}>
      {menuArr.length > 0 &&
        menuArr.map(menu1 => (
          <Popover
            key={menu1.menuId}
            destroyPopupOnHide
            placement={!find(menu1.children, 'children') ? 'bottom' : 'bottomLeft'}
            overlayStyle={{
                width: handleWidth(menu1),
              }}
            content={
                !find(menu1.children, 'children')
                  ? singleColumnLayout(sortBy(menu1.children, 'children'), hideAllMenu)
                  : multiColumnLayout(sortBy(menu1.children, 'children'), hideAllMenu)
              }
            trigger="hover"
            getPopupContainer={trigger => trigger.parentNode}
          >
            <span className={classNames(styles.action)}>
              {menu1.menuName} <Icon type="down" className={styles.arrow} />
            </span>
          </Popover>
          ))}

      <Modal visible={visible} onCancel={() => setVisible(false)} footer={null} width="800px">
        <iframe src={modalUrl} height="600px" width="752px" />
      </Modal>
    </div>
  );
}

export default connect(({ menu }) => ({
  menu,
}))(AllMenu2);
