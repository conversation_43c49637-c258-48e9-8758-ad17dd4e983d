import React from 'react';
import { connect } from 'dva';
import img1 from '../img/menusvg/list.svg';
import img2 from '../img/menusvg/users.svg';
import img3 from '../img/menusvg/computer.svg';
import img4 from '../img/menusvg/control.svg';
import img5 from '../img/menusvg/shoppingcar.svg';
import img6 from '../img/menusvg/partition.svg';
import img7 from '../img/menusvg/briefcase.svg';
import img8 from '../img/menusvg/line.svg';
import img9 from '../img/menusvg/info.svg';
import img10 from '../img/menusvg/card.svg';
import defaultimg from '../img/menusvg/default.svg';
import leavesimg from '../img/menusvg/leavesimg.svg';

function MenuImg(props) {
  const { iconUrl,menuType } = props;
  const optImg = () => {
    // console.log('iconUrl', iconUrl);
    switch (iconUrl) {
      case 'list':
        return img1;
      case 'users':
        return img2;
      case 'computer':
        return img3;
      case 'control':
        return img4;
      case 'shoppingcar':
        return img5;
      case 'partition':
        return img6;
      case 'briefcase':
        return img7;
      case 'line':
        return img8;
      case 'info':
        return img9;
      case 'card':
        return img10;
      default:
        if(parseInt(menuType, 10) === 1100) return leavesimg;
        return defaultimg;
    }
  };

  return (
    <>
      <img
        alt=""
        src={optImg()}
        height="17"
        width="16"
        style={{ marginRight: 13, marginBottom: 4 }}
      />
    </>
  );
}

export default connect(({ login }) => ({
  user: login.user,
}))(MenuImg);
