/* eslint-disable react/no-array-index-key */
/* eslint-disable react/jsx-indent */
/* eslint-disable camelcase */
import React, { useState, useEffect, useRef } from 'react';
import { useDebounceFn } from '@umijs/hooks';
import { Input, Button, Row, Col, Icon, Radio, Empty, Modal } from 'antd';
import memoizeOne from 'memoize-one';
import isEqual from 'lodash/isEqual';
import classNames from 'classnames';
import { connect } from 'dva';
import Link from 'umi/link';
import {
  getFirstLevelMenu,
  getSecondLevelMenuInfo,
  getThirdLevelMenuInfo,
  getAllMenuByLetter,
  quickSearch,
} from './utils';
import request from '@/utils/request';
import { getItem, getFinalUrl } from '@/utils/utils';
import styles from '../less/header.less';
import MenuImg from './MenuImg';

const _quickSearch = memoizeOne(quickSearch, isEqual);
const _getFirstLevelMenu = memoizeOne(getFirstLevelMenu, isEqual);
const _getAllMenuByLetter = memoizeOne(getAllMenuByLetter, isEqual);
const _getSecondLevelMenuInfo = memoizeOne(getSecondLevelMenuInfo, isEqual);
const _getThirdLevelMenuInfo = memoizeOne(getThirdLevelMenuInfo, isEqual);

function AllMenu({
  dispatch,
  menu: { all, allByLetter, recent, appSysCode, taskSysCode },
  refreshLoading,
  hideAllMenu,
}) {
  // 动态调整左侧菜单高度
  const bodytest = document.body.clientHeight - 57;
  // 抽屉效果ref
  const sortAndnavref = useRef();
  const menu3ref = useRef();
  // 可选值 'category' 和 'letter'
  const [viewMode, setViewMode] = useState('letter');
  // 级联菜单 每级激活菜单的menuId
  const [activeMenuId1, setActiveMenuId1] = useState();
  const [activeMenuId2, setActiveMenuId2] = useState();
  const [activeMenuId3, setActiveMenuId3] = useState();

  // 级联菜单数据
  const [menuLel1, setMenuLel1] = useState(() => _getFirstLevelMenu(all, appSysCode, taskSysCode));
  const [menuLel2, setMenuLel2] = useState([]);
  const [menuLel3, setMenuLel3] = useState([]);
  // 分类与导航菜单显示控制
  const [sortAndnav, setsortAndnav] = useState('none');

  // 弹出框打开
  const [visible, setVisible] = useState(false);
  const [modalUrl, setModalUrl] = useState('');
  const { sessionId, userInfo } = getItem('user');

  // 按字母排序的所有菜单
  const _allByLetter = _getAllMenuByLetter(allByLetter, appSysCode);
  // 按类别排序的所有菜单
  const [allByCategory, setAllByCategory] = useState(
    _quickSearch(all, '', appSysCode, taskSysCode)
  ); // 搜索结果
  const { run } = useDebounceFn((all, value, appSysCode, taskSysCode, viewSort) => {
    if (viewSort) {
      setViewMode('letter');
      setAllByCategory(_quickSearch(all, value, appSysCode, taskSysCode));
    } else {
      setViewMode('category');
      setAllByCategory(_quickSearch(all, value, appSysCode, taskSysCode));
    }
  }, 300);

  function handleSecond(e, menuId) {
    e.preventDefault();
    setActiveMenuId2();
    setMenuLel3([]);
    menu3ref.current.style.width = '0px';
    setActiveMenuId1(parseInt(menuId, 10));
    if (menuId === '000') {
      setMenuLel2(_getSecondLevelMenuInfo(all, menuId));
      setsortAndnav('block');
      // 抽屉效果-宽度处理
      setTimeout(() => {
        sortAndnavref.current.style.width = '720px';
      }, 100);
    } else {
      setMenuLel2(_getSecondLevelMenuInfo(all, menuId));
      setsortAndnav('none');
    }
  }
  function handleSecondLeave(e) {
    e.preventDefault();
    setActiveMenuId1();
    setMenuLel2([]);
    setActiveMenuId2();
    setMenuLel3([]);
    setsortAndnav('none');
    // 抽屉效果-宽度恢复
    sortAndnavref.current.style.width = '0px';
    menu3ref.current.style.width = '0px';
  }

  function handleThird(e, menuId) {
    e.preventDefault();
    setActiveMenuId2(parseInt(menuId, 10));
    setMenuLel3(_getThirdLevelMenuInfo(all, menuId));
    if (_getThirdLevelMenuInfo(all, menuId).length > 0) {
      setTimeout(() => {
        menu3ref.current.style.width = '240px';
      }, 100);
    } else {
      menu3ref.current.style.width = '0px';
    }
  }

  function handlesortAndnav(e) {
    e.preventDefault();
    run(all, '', appSysCode, taskSysCode, true);
  }

  // 负责渲染级联菜单
  function renderMenu(menu, hideAllMenu) {
    // 1100 表示叶子节点
    if (parseInt(menu.menuType, 10) === 1100) {
      if (menu.menuOpenMode !== '1') {
        return (
          <>
            <a
              onClick={() => openMenu(menu)}
              className={classNames('text-ellipsis')}
              key={menu.menuId}
            >
              {/* {menu.menuLevel === 0 ? <MenuImg iconUrl={menu.iconUrl} /> : <></>} */}
              <MenuImg iconUrl={menu.iconUrl} menuType={menu.menuType} />
              <span>{menu.menuName}</span>
            </a>
          </>
        );
      }
      return (
        <Link
          to={getFinalUrl(menu)}
          onClick={() => hideAllMenu(false)}
          title={menu.menuName}
          className="text-ellipsis"
        >
          <MenuImg iconUrl={menu.iconUrl} menuType={menu.menuType} />
          {/* {menu.menuLevel === 0 ? <MenuImg imgType="1" iconUrl={menu.iconUrl} /> : <></>} */}
          <span>{menu.menuName}</span>
        </Link>
      );
    }
    return (
      <a title={menu.menuName} href="#" className="text-ellipsis">
        <MenuImg iconUrl={menu.iconUrl} menuType={menu.menuType} />
        {/* {menu.menuLevel === 0 ? <MenuImg imgType="1" iconUrl={menu.iconUrl} /> : <></>} */}
        <span>{menu.menuName}</span>
        <Icon type="right" />
      </a>
    );
  }

  function renderAllByLetter(allByLetter, hideAllMenu) {
    return allByLetter.length === 0
      ? null
      : allByLetter.map(group => (
          <Col span={8} key={JSON.stringify(group)}>
            {group.length === 0
              ? null
              : group.map(member =>
                  member.menus.length === 0 ? null : (
                    <dl key={member.title}>
                      <dt className="letter-title">
                        <span>{member.title}</span>
                      </dt>
                      <dd>
                        <ul>
                          {member.menus.map(menu => (
                            <li className="ng-item" key={menu.menuId}>
                              {renderMenu(menu, hideAllMenu)}
                            </li>
                          ))}
                        </ul>
                      </dd>
                    </dl>
                  )
                )}
          </Col>
        ));
  }

  /**
   *
   * @param {object[]} result 格式如下
   * [
   *   {
   *     title: '组织管理',
   *     menus: [
   *       {
   *         firstLetter: 'YGGL',
   *         iconUrl: 'icon-gene-perm-identity',
   *         menuCode: 'TYMH_MENU_019',
   *         menuDesc: '员工管理',
   *         menuId: 276111,
   *         menuIndex: 7,
   *         menuLevel: 2,
   *         menuName: '员工管理',
   *         menuOpenMode: '3',
   *         menuType: '1100',
   *         menuTypeName: '叶子菜单',
   *         parMenuId: 276110,
   *         paramEncryptType: '1000',
   *         privId: 88352,
   *         statusCd: '1000',
   *         systemCode: '727001',
   *         urlAddr: 'orgauth/modules/staff/views/StaffManageView',
   *       },
   *     ],
   *   },
   * ];
   */
  function renderAllByCategory(all, hideAllMenu) {
    return all.length === 0 ? (
      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ margin: '150px 0 32px' }} />
    ) : (
      all.map((group, n) => (
        <Col span={8} key={`${group.title}_${n}`}>
          {/**
           * TODO: 测试环境配置了一样的一级菜单导致key重复，暂时使用索引区分
           */}
          <dl>
            <dt className="letter-title">
              <span>{group.title}</span>
            </dt>
            <dd>
              <ul>
                {group.menus.map((menu, i) => (
                  <li className="ng-item" key={`${menu.menuId}_${i}`}>
                    {renderMenu(menu, hideAllMenu)}
                  </li>
                ))}
              </ul>
            </dd>
          </dl>
        </Col>
      ))
    );
  }

  // 菜单打开方式
  async function openMenu(menu) {
    // var MENU_OPEN_MODE_WORK_BENCH_LABEL = "1"; //工作台标签页打开
    // var MENU_OPEN_MODE_POPUP = "2"; //弹出框打开
    // var MENU_OPEN_MODE_NEW_BROWSER = "3"; //新开浏览器打开
    // var MENU_OPEN_MODE_BROWSER_LABEL = "4"; //浏览器新标签打开
    let menuUrl = menu.urlAddr.replace('[iframe]', '');
    if (menuUrl.indexOf('?') === -1) {
      menuUrl += `?bss3SessionId=${sessionId}`;
    } else {
      menuUrl += `&bss3SessionId=${sessionId}`;
    }

    if (menuUrl.indexOf('{SYS_USER_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_USER_ID}', userInfo.userCode);
    }
    if (menuUrl.indexOf('{SYS_ORG_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_ORG_ID}', userInfo.userOrgId);
    }

    // 采用SSO单点校验方式进行菜单打开
    if (menuUrl.indexOf('ssoType=1') !== -1) {
      // let urlParams = parseUrlParams(menuUrl);
      const urlParams = {
        bssSessionId: sessionId,
      };

      // 菜单地址SSO加密，拼接返回的signString和singTimestamp
      const response = await request(
        `orgauth/SystemInfoController/calcSign.do?systemInfoId=${menu.systemCode}`,
        {
          method: 'POST',
          data: urlParams,
        }
      );

      if (response && response.resultCode === '0') {
        menuUrl = `${menuUrl}&signString=${response.signString}&signTimestamp=${response.signTimestamp}`;
      }
    }

    if (menu.menuOpenMode === '4') {
      window.open(menuUrl);
    } else if (menu.menuOpenMode === '3') {
      window.open(menuUrl, menu.menuName, 'width=800,height=600,location=yew,toolbar=yes');
    } else {
      setVisible(true);
      setModalUrl(menuUrl);
      hideAllMenu(false);
    }
  }

  /**
   * 解析url参数，返回{
   *    url:不含?号的url地址,
   *    oriUrl:原url,
   *    params: ?号后面的参数
   * }
   * @param url
   * @return {{}}
   */
  function parseUrlParams(url) {
    const urlArr = url.split('?');
    const resp = {};
    resp.oriUrl = url;
    const options = {};
    if (urlArr.length > 1) {
      resp.url = urlArr[0];
      const parArr = urlArr[1].split('&');
      for (let i = 0; i < parArr.length; i++) {
        const par = parArr[i].split('=');
        if (par[1]) {
          options[par[0]] = decodeURIComponent(par[1]);
        } else {
          options[par[0]] = '';
        }
      }
    } else {
      resp.url = url;
    }
    resp.params = options;
    return resp;
  }

  useEffect(() => {
    setMenuLel1(_getFirstLevelMenu(all, appSysCode, taskSysCode));
    setMenuLel2(_getSecondLevelMenuInfo(all, activeMenuId1));
    setMenuLel3(_getThirdLevelMenuInfo(all, activeMenuId2));
  }, [setMenuLel2, setMenuLel3, all, appSysCode, taskSysCode, activeMenuId1, activeMenuId2]);

  return (
    <div className="topbar-dropdown-box-light" style={{ height: bodytest }}>
      <div className="y-row" onMouseLeave={e => handleSecondLeave(e)}>
        <div className="y-col">
          <div className="y-col-body">
            {menuLel1.length > 0 ? (
              <ul className="y-list">
                <li
                  className={classNames({ active: activeMenuId1 === '000' })}
                  key="000"
                  onMouseEnter={e => handleSecond(e, '000')}
                  onClick={e => handleSecond(e, '000')}
                >
                  <a className={classNames('text-ellipsis')} key="000" title="全部分类与导航">
                    <MenuImg imgType="defaultimg" menuType={1000} />
                    <span className={styles.menutitle}>全部分类与导航</span>
                  </a>
                </li>
                {menuLel1.map(menu => (
                  <li
                    className={classNames({ active: menu.menuId === activeMenuId1 })}
                    key={menu.menuId}
                    onMouseEnter={e => handleSecond(e, menu.menuId)}
                    onClick={e => handleSecond(e, menu.menuId)}
                  >
                    {renderMenu(menu, hideAllMenu)}
                  </li>
                ))}
              </ul>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </div>
        </div>

        {/* {sortAndnav ? ( */}
        <div
          className="letter-panel"
          style={{ display: sortAndnav }}
          onMouseLeave={e => handlesortAndnav(e)}
          ref={sortAndnavref}
        >
          <div className="letter-search">
            <div style={{ width: 200 }} className="inline-block">
              <Input.Search
                allowClear
                placeholder="搜索关键字"
                onSearch={value => run(all, value, appSysCode, taskSysCode)}
              />
            </div>
            <Button
              type="primary"
              className="inline-block margin-left"
              loading={refreshLoading}
              onClick={() => {
                dispatch({
                  type: 'menu/refreshMenu',
                });
              }}
            >
              刷新
            </Button>
            <Radio.Group
              value={viewMode}
              onChange={e => setViewMode(e.target.value)}
              className="pull-right"
            >
              <Radio.Button value="category">按类别</Radio.Button>
              <Radio.Button value="letter">按字母</Radio.Button>
            </Radio.Group>
          </div>
          <div className="letter-category clearfix">
            <Row gutter={16}>
              {viewMode === 'letter'
                ? renderAllByLetter(_allByLetter, hideAllMenu)
                : renderAllByCategory(allByCategory, hideAllMenu)}
            </Row>
          </div>
        </div>
        {/* ) : null} */}

        {menuLel2.length > 0 ? (
          <div className="y-col">
            <div className="y-col-body">
              <ul className="y-list">
                {menuLel2.map(menu => (
                  <li
                    className={classNames({ active: menu.menuId === activeMenuId2 })}
                    onMouseEnter={e => handleThird(e, menu.menuId)}
                    key={menu.menuId}
                  >
                    {renderMenu(menu, hideAllMenu)}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ) : null}

        <div className="y-col" ref={menu3ref} style={{ width: 0 }}>
          <div className="y-col-body">
            <ul className="y-list">
              {menuLel3.map(menu => (
                <li
                  className={classNames({ active: menu.menuId === activeMenuId3 })}
                  onMouseEnter={() => setActiveMenuId3(menu.menuId)}
                  key={menu.menuId}
                >
                  {renderMenu(menu, hideAllMenu)}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
      <Modal visible={visible} onCancel={() => setVisible(false)} footer={null} width="800px">
        <iframe src={modalUrl} height="600px" width="752px" />
      </Modal>
    </div>
  );
}

export default connect(({ menu, loading }) => ({
  menu,
  refreshLoading: loading.effects['menu/refreshMenu'],
}))(AllMenu);
