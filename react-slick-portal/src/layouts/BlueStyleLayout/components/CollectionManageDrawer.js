import React, { useState, useEffect } from 'react';
import { Input, Checkbox, Button, Drawer, Empty, message } from 'antd';
import { useDebounceFn } from '@umijs/hooks';
import { connect } from 'dva';
import memoizeOne from 'memoize-one';
import isEqual from 'lodash/isEqual';
import mapping from '@/services/menu';
import { quickSearch } from './utils';
import styles from '../less/menuDrawer.less';
import request from '@/utils/request';

const _quickSearch = memoizeOne(quickSearch, isEqual);

const { COLLECT_MENU_SIZE_DEFAULT } = mapping;

// 创建可选择的菜单列表，并给每个menu追加contentId
function buildMenuList(all, value = '', appSysCode, taskSysCode) {
  return _quickSearch(all, value, appSysCode, taskSysCode).map(item => {
    if (Array.isArray(item.menus)) {
      item.menus.forEach(menu => {
        menu.contentId = menu.menuId;
      });
    }
    return item;
  });
}

const CollectionManageDrawer = ({ loading, visible, close, dispatch, menu: { all, collection, appSysCode, taskSysCode } }) => {
  const [menuSize, setMenuSize] = useState(10);
  const [selectedList, setSelectedList] = useState(collection || []);
  // 所有菜单
  const [menuList, setMenuList] = useState(buildMenuList(all, '', appSysCode, taskSysCode)); // 所有菜单

  const isChecked = menu => selectedList.some(item => item.contentId === menu.contentId);

  // 负责渲染级联菜单
  const renderMenus = (menus = []) =>
    menus.map(menu => (
      <div className={styles.checkBox}>
        <Checkbox
          value={menu}
          checked={isChecked(menu)}
          onChange={e => {
            if (e.target.checked) {
              if (selectedList.length >= menuSize) {
                message.warn(`最多允许收藏${menuSize}个菜单`);
                return;
              }
              setSelectedList([...selectedList, e.target.value]);
            } else {
              setSelectedList(selectedList.filter(item => item.contentId !== menu.contentId));
            }
          }}
        >
          {menu.menuName}
        </Checkbox>
      </div>
    ));

  function handleSubmit(e) {
    e.preventDefault();
    dispatch({
      type: 'menu/updateCollectionMenu',
      payload: {
        close,
        collection: selectedList.map((item, index) => ({ ...item, collectSort: index + 1 })),
      },
    });
  }

  const { run } = useDebounceFn(value => {
    setMenuList(buildMenuList(all, value, appSysCode, taskSysCode));
  }, 300);

  const menuSizeData = async () => {
    request('portal/DataDictController/getValidValueByCode.do', {
      data: { groupCode: 'COLLECT_GROUP_CODE', paramCode: 'COLLECT_MENU_SIZE', defValue: '' },
      method: 'GET',
    }).then(result => {
      if (result) {
        setMenuSize(result);
      } else {
        setMenuSize(COLLECT_MENU_SIZE_DEFAULT);
      }
    });
  };

  useEffect(() => {
    menuSizeData();
  }, []);

  return (
    <Drawer placement="right" closable={false} onClose={close} visible={visible} destroyOnClose width={850}>
      <div className={styles.collectionDrawer}>
        <div className={styles['letter-search']}>
          <div style={{ width: 200 }} className="inline-block">
            <Input.Search allowClear placeholder="搜索关键字" onSearch={run} />
          </div>
        </div>
        <div className={styles.tip}>
          <span>固定至快捷操作（ </span>
          <span className={styles.highlight}>{`${selectedList.length} / ${menuSize}`}</span>
          <span> ）</span>
        </div>
        {menuList.length === 0 ? (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ margin: '150px 0 32px' }} />
        ) : (
          <div className={styles.scrollWrap} style={{ height: document.documentElement.clientHeight - 155 }}>
            <div className={styles['letter-category']}>
              {menuList.map((group, n) => (
                <div className={styles['letter-content']} key={`${group.title}_${n}`}>
                  <dl>
                    <dt className={styles['letter-title']}>
                      <span>{group.title}</span>
                    </dt>
                    <dd>
                      <div>{renderMenus(group?.menus)}</div>
                    </dd>
                  </dl>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      <div className={styles.drawerFooter}>
        <Button className="margin-right" onClick={close}>
          取消
        </Button>
        <Button type="primary" loading={loading} onClick={handleSubmit}>
          确定
        </Button>
      </div>
    </Drawer>
  );
};

export default connect(({ menu, loading }) => ({
  menu,
  loading: loading.effects['menu/updateCollectionMenu'],
}))(CollectionManageDrawer);
