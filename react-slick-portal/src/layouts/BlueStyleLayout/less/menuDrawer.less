@import './variables.less';

:global {
  .menu-drawer {
    width: 0;
    background-color: #fff;
    border-right: 0 solid #e8e8e8;
    .reset {
      margin-bottom: 8px;
      .ant-tabs-nav-animated,
      .ant-tabs-nav-container {
        width: @menu-drawer-width - 16;
        transform: none !important;
      }
      .ant-tabs-nav-container {
        margin-left: 8px;
      }
      .ant-tabs-nav-container-scrolling {
        padding: 0 !important;
      }
      .ant-tabs-tab-prev,
      .ant-tabs-tab-next {
        display: none !important;
        width: 0 !important;
      }
      .ant-tabs-tab {
        width: 50%;
        margin: 0;
        padding: 8px 0;
        color: @text-color-secondary;
        text-align: center;
        &.ant-tabs-tab-active {
          color: @blue-6;
        }
      }
      .ant-tabs-ink-bar-animated {
        // transform: none !important;
        width: @menu-drawer-width / 2 !important;
      }
    }

    .ant-menu-inline {
      border-right: none;
      .ant-menu-submenu {
        border-bottom: 1px solid #e8e8e8;
      }

      .ant-menu-sub {
        background-color: #f9f9fd;
        > li:last-child {
          border-bottom: none;
        }
      }
    }

    // 禁用选中高亮样式
    .ant-menu-item-selected {
      background-color: #fff !important;
      > a {
        color: @text-color;
      }
      &::after {
        border-right-color: transparent;
      }
    }
  }
}

.collectionDrawer {
  margin-right: -24px;
  padding-left: 60px;

  .tip {
    margin-bottom: 16px;
    color: @text-color-secondary;
    font-size: 14px;

    .highlight {
      color: @blue-6;
    }
  }
  .letter-search {
    height: 36px;
    margin-bottom: 8px;
  }
  .scrollWrap {
    height: 100%;
    overflow: auto;
  }
  .letter-category {
    column-gap: 24px;
    column-width: 220px;

    .letter-content {
      break-inside: avoid;
      margin-bottom: 8px;

      .letter-title {
        padding: 8px;
        padding-left: 0;
        color: @blue-6;
        font-weight: bold;
        font-size: 14px;
        line-height: 20px;
      }

      .checkBox {
        margin-bottom: 8px;
      }
    }
  }
}

.drawerFooter {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 50px;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  border-top: 1px solid #e9e9e9;
}
