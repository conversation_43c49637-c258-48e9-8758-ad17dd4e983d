@import '~antd/lib/style/themes/default.less';
@import './variables.less';

.navMenu {
  height: @header-height;
  overflow: hidden;
}

.group {
  // margin-top: 16px;
  // margin-bottom: 16px;
  // background: rgba(250, 250, 250, 1);
  // border-radius: 2px;
}

.split {
  margin: 12px 0 8px;
}

.title {
  margin-bottom: 8px;
  color: @text-color;
  font-weight: bold;
}
.item {
  display: block;
  padding: 6px 16px 6px 0;
  color: @menu-color;
  line-height: 20px;
  // &:hover {
  //   background-color: @primary-1;
  // }
}
