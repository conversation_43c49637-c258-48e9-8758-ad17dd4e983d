@import '~antd/lib/style/themes/default.less';
@import './variables.less';

.navMenu {
  height: @header-height;
  overflow: hidden;
}

.group {
  // margin-top: 16px;
  // margin-bottom: 16px;
  // background: rgba(250, 250, 250, 1);
  // border-radius: 2px;
}

.split {
  margin: 12px 0 8px;
}

.title {
  margin-bottom: 8px;
  color: @text-color;
  font-weight: bold;
}
.item {
  display: block;
  padding: 6px 16px 6px 0;
  color: @menu-color;
  line-height: 20px;
  // &:hover {
  //   background-color: @primary-1;
  // }
}

:global {
  /* 系统菜单展开开始 */
  .topbar-dropdown-box4 {
    // position: fixed;
    // z-index: 1000;
    // width: 100%;
    // height: 60%;
    overflow: hidden;
    font-size: @font-size-base;
    background: #fff;
    // border: 1px solid #d4d5d6;
    // box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    ul {
      margin: 0;
      padding: 0;
      list-style: none;
    }
    .inline-block {
      display: inline-block;
    }
    .y-list {
      margin: 0;
      // margin-top: @slick-space-base;
      padding: 0;
      list-style: none;
      li {
        position: relative;
        i {
          position: absolute;
          top: 8px;
          right: 8px;
          display: none;
          margin-top: 5px;
          font-size: 12px;
        }
        a {
          display: block;
          padding: 8px 16px 8px 8px;
          color: @menu-color;
          line-height: 20px;
        }
        &:hover {
          background: #f3f3f3;
          i {
            display: block;
          }
        }
        &.active {
          background-color: @primary-1;
          i {
            display: block;
          }
          a {
            color: @blue-6;
          }
        }
      }
    }

    .y-col {
      position: relative;
      float: left;
      width: 180px;
      max-height: 360px;
      overflow-x: hidden;
      overflow-y: auto;
      line-height: 20px;
      background-color: #fafafa;
      // border: 1px solid #eee;
      .y-col-header {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        height: 36px;
        padding: 8px;
        color: @text-color-secondary;
      }
      .y-col-body {
        position: absolute;
        top: 36px;
        right: 0;
        bottom: 0;
        left: 0;
      }
      .y-col-body_menu4 {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }
    }
    .bg-grey {
      background-color: #fafafa;
    }
    .y-row {
      position: relative;
      width: 100%;
      height: 100%;
      &::after {
        display: block;
        clear: both;
        height: 0;
        font-size: 0;
        visibility: hidden;
        content: ' ';
      }
    }

    .letter-panel {
      position: relative;
      max-height: 360px;
      overflow-y: auto;
      .letter-search {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        height: 36px;
        padding: 6px 16px;
      }
      .letter-category {
        // position: absolute;
        // top: 36px;
        // right: 0;
        // bottom: 0;
        // left: 0;
        padding: 0 16px 8px;
        overflow-x: hidden;
        overflow-y: auto;
        .letter-title {
          padding: 8px;
          color: @blue-6;
          font-weight: bold;
          font-size: 14px;
          line-height: 20px;
        }
        ul {
          margin: 0;
          padding: 0;
          list-style: none;
          li {
            &.ng-item {
              a {
                display: block;
                padding: 8px;
                color: @menu-color;
                line-height: 20px;
                &:hover {
                  color: @menu-color;
                  background-color: #f0f0f0;
                }
              }
            }
          }
          &::after {
            display: block;
            clear: both;
            height: 0;
            font-size: 0;
            visibility: hidden;
            content: ' ';
          }
        }
      }
    }
  }
}
