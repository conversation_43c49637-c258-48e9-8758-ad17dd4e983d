import React from 'react';
import classNames from 'classnames';
import { Helmet } from 'react-helmet';
import { formatMessage } from 'umi-plugin-react/locale';
import SelectLang from '@/components/SelectLang';
import { enableLanguage } from '@/defaultSettings';
import logo from './img/yidong-logo.png';
import computer from './img/computer.png';
import styles from './less/index.less';

const { location } = window;

class UserLayout extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      show: !location.port, // 如果不是 dva 2.0 请删除
    };
  }

  componentDidMount() {
    // dva 2.0 样式在组件渲染之后动态加载，导致滚动组件不生效；线上不影响；
    /* 如果不是 dva 2.0 请删除 start */
    if (location.port) {
      // 样式 build 时间在 200-300ms 之间;
      setTimeout(() => {
        this.setState({
          show: true,
        });
      }, 500);
    }
    /* 如果不是 dva 2.0 请删除 end */
  }

  render() {
    const { children } = this.props;
    return (
      <>
        <Helmet>
          <title>{`${formatMessage({ id: 'app.title' })}`}</title>
        </Helmet>
        <div
          className={classNames(styles.userlayout)}
          ref={d => {
            this.dom = d;
          }}
        >
          {this.state.show && (
            <>
              <div className={styles.logo}>
                <img src={logo} alt="logo" />
              </div>
              <div className={styles.computer}>
                <img src={computer} alt="computer" />
              </div>
              {enableLanguage ? (
                <div className={styles.language}>
                  <SelectLang />
                </div>
              ) : null}

              {children}
            </>
          )}
        </div>
      </>
    );
  }
}

export default UserLayout;
