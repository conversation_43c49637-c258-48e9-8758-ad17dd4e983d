.minImg{
  position: fixed;
  right: 0px;
  bottom: 90px;
  transform: rotate(-50deg);
  z-index: 1000;
  cursor: pointer;
  height: 22px;
}
.floatingInput{
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  z-index: 999;
  .inputBox{
    text-align: center;
    display: flex;
    align-items: center;
    // box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 8px;
    box-shadow: inset 30px 0px 34px 0px rgba(35, 160, 222, 0.2);
    border: 1px solid transparent;
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    background-image: linear-gradient(to right, #fff, #fff), linear-gradient(180deg, #0169ff, #3f9fff, #4e95ff, #9946ff);
    border-radius: 8px !important;
  }

  .closeIcon{
    margin-top: -10px;
    font-size: 20px;
    color: #333;
  }

  :global{
    .ant-input{
      border: none;
      background-color: rgba(255, 0, 0, 0); /* 红色，半透明 */
    }

    .ant-input:focus {
      border-color: rgba(0, 133, 208, 0);
      border: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
}
