@import './variables.less';

:global {
  .view-normal {
    padding-top: @header-height;
  }
  .view-inner {
    #layout-body {
      padding-top: 0 !important;
      padding-left: 0 !important;
    }
  }
  #root,
  #layout-container,
  #layout-body {
    height: 100%;
  }

  #layout-container,
  #layout-body,
  #layout-header,
  .layout-tabs-nav,
  .setting-drawer,
  .menu-drawer-basic {
    transition: all 0.3s ease;
  }

  #layout-container {
    position: relative;
    left: 0;
    background-color: #D8E4EB;
    flex: 1 1;

    // overflow-x: hidden;
    .layout-body-main {
      // background-color: #E9EEF1;
      background: url('../img/bg.png');
      background-size: 100% 100%;
    }
  }
  // OPEN SETTING DRAWER
  .setting-drawer {
    position: fixed;
    top: 0;
    right: -@setting-drawer-width;
    bottom: 0;
    z-index: 999;
    width: @setting-drawer-width;
    // transform: translate(@setting-drawer-width, 0);
  }

  .setting-drawer-active {
    left: -@setting-drawer-width !important;
    #layout-body {
      position: relative;
    }
    #layout-header,
    .layout-tabs-nav,
    .menu-drawer-basic {
      right: @setting-drawer-width !important;
      left: -@setting-drawer-width !important;
    }
    .setting-drawer {
      right: 0;
    }
    .setting-drawer-mask {
      right: @setting-drawer-width !important;
      left: 0;
      transition: all 0.000001s ease 0.3s;
    }
  }

  .setting-drawer-mask {
    position: fixed;
    top: 0;
    right: -100%;
    bottom: 0;
    left: 100%;
    z-index: 998;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    filter: alpha(opacity=50);
  }

  // OPEN MENU DRAWER
  .menu-drawer-basic {
    position: fixed;
    top: @header-height;
    bottom: 0;
    left: 0;
    width: 0;
  }

  @base-menu-drawer-width: 50px;
  @second-menu-drawer-width: 192px;

  .base-menu-drawer-active {
    #layout-body {
      padding-left: @base-menu-drawer-width;
    }
    .menu-drawer-basic {
      width: @base-menu-drawer-width;
    }
    &.tabs-nav-fixed {
      .layout-tabs-nav {
        left: @base-menu-drawer-width;
      }
    }
  }

  .second-menu-drawer-active {
    #layout-body {
      padding-left: @second-menu-drawer-width + @base-menu-drawer-width;
    }
    .menu-drawer-basic {
      width: @second-menu-drawer-width + @base-menu-drawer-width;
    }
    &.tabs-nav-fixed {
      .layout-tabs-nav {
        left: @second-menu-drawer-width + @base-menu-drawer-width;
      }
    }
  }

  // LAYOUT TABS NAV
  .layout-tabs-nav {
    height: 36px; // 务必设置一个固定高度，否则global.size.height计算失效
    margin-bottom: 0;
    padding: 6px 8px;
    // padding-left: @slick-space-base;
    // background: rgba(235,244,249,0.7);
    background: #FFF;
    // box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.04);
    .ant-tabs-ink-bar {
      display: none !important;
    }
    .ant-tabs-nav-wrap {
      margin-bottom: 0;
    }
    .ant-tabs-tab {
      margin-right: 5px;
      padding: 3px 8px;
      color: #666666;
      font-size: 12px;
      background: rgba(0,0,0,0.04);
      // background: #FFFFFF;
      border-radius: 4px;
      &.ant-tabs-tab-active {
        color: #fff;
        background: linear-gradient( 228deg, #0085D0 0%, #30AEF6 100%);
        font-weight: 400;
        /*border: 1px solid rgba(0, 133, 208, 1);*/
      }
    }
    .anticon-close {
      margin-right: 0 !important;
      margin-left: 8px;
      font-size: 12px;
    }
  }

  .layout-tabs-time{
    font-weight: 400;
    font-size: 12px;
    color: #0085D0;
    line-height: 24px !important;
    margin: 0 8px;
  }

  .tabs-nav-fixed {
    .layout-tabs-nav {
      position: fixed;
      top: @header-height !important;
      right: 0;
      left: 0;
      z-index: 100;
    }
    #layout-body {
      padding-top: 36px;
    }
  }
  .base-online-mask{
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    height: 100%;
    background-color: rgba(0,0,0,.45);
  }
  .selected_word_popup{
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    background-color: #fff;
    border-radius: 5px;
    z-index: 9999;
    max-width: 400px;
    display: flex;
    align-items: center;
    .selected_word_content{
      line-height: 28px;
      padding: 0 10px;
      color: #424242;
      cursor: pointer;
      word-break: break-all;
      font-weight: 700;
    }
  }

  .yx-chat-window.pop{
    top: 48px !important;
  }
}
