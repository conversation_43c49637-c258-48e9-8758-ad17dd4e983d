@import './variables.less';
.minContainer {
  position: fixed;
  top: 75px;
  right: 10px;
  background-color: rgb(255, 255, 255);
  border-radius: 2px;
  box-shadow: rgba(0, 0, 0, 0.15) 0 4px 12px;
  .minContent{
    display: flex;
    justify-content: space-between;
    width: 200px;
    padding: 16px 24px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 22px;
    .title{
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
    }
  }
}
