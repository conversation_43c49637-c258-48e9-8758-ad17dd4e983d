@import '~antd/lib/style/themes/default.less';
@import './variables.less';

.header {
  position: relative;
  height: 64px;
  padding: 16px;
  color: #fff;
  font-size: 16px;
  line-height: 32px;
  background: @primary-color;
}

:global {
  .setting-drawer {
    position: relative;
    width: @setting-drawer-width;
    // overflow-y: auto;
    background-color: #fff;
    border-left: none;

    .clearfixAfter {
      display: block;
      clear: both;
      height: 0;
      visibility: hidden;
      content: '.';
    }
    .clearfix {
      display: block;
      zoom: 1;
    }

    .ant-list-item {
      padding: 8px 0;
      color: rgba(0, 0, 0, 0.8);
      font-size: 14px;
      span {
        flex: 1;
      }
    }
  }
  .setting-drawer-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    display: block;
    width: 56px;
    height: 64px;
    padding: 0;
    color: #fff;
    font-weight: 700;
    font-size: 16px;
    font-style: normal;
    line-height: 64px;
    text-align: center;
    text-transform: none;
    text-decoration: none;
    background: transparent;
    border: 0;
    outline: 0;
    cursor: pointer;
    transition: color 0.3s;
    text-rendering: auto;
    &:hover {
      color: rgba(0, 0, 0, 0.75);
    }
  }
}

.content {
  position: relative;
  height: calc(100% - 64px);
  padding: @slick-space-base * 2;
  overflow-y: auto;
}

.productionHint {
  margin-bottom: 24px;
  :global(.ant-alert-message) {
    color: @text-color-secondary;
  }
}

.circle {
  font-size: 16px !important;
  line-height: 22px;
  border-color: transparent;
}

.roleContainer{
  padding: 8px 12px;
  background-color: #fff;
  .roleItem{
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #333;
    font-size: 12px;
    cursor: default;
    .rolesName{
      margin: 0 6px;
      white-space: nowrap;
    }
    .rolesCode{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.collapseDiv {
  margin-top: 8px;
  :global {
    .ant-collapse{
      background-color: initial;
    }
    .ant-collapse-borderless{
      background-color:initial;
    }
    .ant-collapse-item{
      border: none;
    }
    .ant-collapse-header{
      margin-bottom: 8px;
      padding: 5px 12px !important;
      color: #000;
      font-weight: 400;
      font-size: 14px;
      background: #F9F9F9;
    }
    .ant-collapse-content-box{
      padding: 0 !important;
    }
  }
}

