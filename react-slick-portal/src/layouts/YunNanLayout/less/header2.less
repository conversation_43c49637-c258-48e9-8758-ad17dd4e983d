@import './variables.less';

.refreshButton{
  position: absolute;
  top: 7px;
  right: 20px;
}

.row {
  position: relative;
  width: 100%;
  height: 100%;
  &::after {
    display: block;
    clear: both;
    height: 0;
    font-size: 0;
    visibility: hidden;
    content: ' ';
  }
}

.col {
  position: relative;
  float: left;
  width: 180px;
  height: 280px;
  line-height: 20px;
  background-color: #fff;
  border-right: 1px solid rgba(237, 237, 238, 1);
  overflow-y: auto;
  overflow-x: hidden;
}

.body {
  overflow-y: auto;
  overflow-x: hidden;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}
.list {
  margin: 0;
  // margin-top: @slick-space-base;
  padding: 0;
  list-style: none;
  overflow: hidden;
  li {
    position: relative;
    i {
      position: absolute;
      top: 8px;
      right: 8px;
      display: none;
      margin-top: 5px;
      font-size: 12px;
    }
    a {
      display: block;
      padding: 8px 16px 8px 8px;
      color: rgba(85, 88, 100, 1);
      line-height: 20px;
      &:hover {
        color: @blue-6;
      }
    }
    &:hover {
      // background: #f3f3f3;
      color: @blue-6;
      i {
        display: block;
        color: @blue-6!important;
      }
    }
    &.active {
      // background-color: @primary-1;
      i {
        display: block;
        color: @blue-6!important;
      }
      a {
        color: @blue-6;
      }
    }
  }
}

.fix {
  padding: 16px;
}

.panel {
  padding: 0 16px;
  position: relative;
  overflow-y: auto;
  height: 280px;

  .title {
    width: 160px;
    color: rgba(143, 145, 152, 1);
    margin: 16px 8px 0;
    padding-bottom: 4px;
    line-height: 28px;
    border-bottom: 1px solid #ededee;
    &:first-child{
      margin-top: 0;
    }
  }

  .list li {
    width: 180px;
    float: left;
  }
}
