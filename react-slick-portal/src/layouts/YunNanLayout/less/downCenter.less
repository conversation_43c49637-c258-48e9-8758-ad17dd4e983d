// .downCenter {
//   :global(.ant-modal-body) {
//     padding: 0px !important;
//   }
// }

.dropdown {
  width: auto;
  padding: 20px 30px;
  background: #fff;
  border: 1px solid #d4d5d6;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}
.item {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.8);
  padding: 6px;
  // display: inline-block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  // height: 34px;
  display: flex;
  align-items: center;

  .gray {
    color: rgba(0, 0, 0, 0.4);
  }
  &.bordered {
    border-radius: 2px;
    border: 1px solid rgba(201, 201, 201, 1);
    &:hover {
      border-color: rgba(0, 133, 208, 1);
      color: rgba(0, 133, 208, 1);
    }
    &.actived {
      border-color: rgba(0, 133, 208, 1);
      color: rgba(0, 133, 208, 1);
      position: relative;
      &::before {
        content: '';
        position: absolute;
        right: 0;
        bottom: 0;
        border: 9px solid rgba(0, 133, 208, 1);
        border-top-color: transparent;
        border-left-color: transparent;
      }

      &::after {
        content: '';
        width: 5px;
        height: 10px;
        position: absolute;
        right: 1px;
        bottom: 2px;
        border: 2px solid #fff;
        border-top-color: transparent;
        border-left-color: transparent;
        transform: rotate(45deg);
      }
    }
  }
}

.card {
  :global {
    .ant-card-body {
      overflow-y: auto;
      height: 132px;
      // padding-top: 16px !important;
      padding-bottom:0 !important ;
    }
  }
}
