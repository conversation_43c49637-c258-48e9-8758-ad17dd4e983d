@import './variables.less';

.wrapper {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 900;
  height: @header-height;
  padding: 0;
  color: @header-default-text;
  line-height: @header-height;
  background-color: @header-default-bg;
  border-bottom: 1px solid @header-border-default-color;
  // border-bottom: 1px solid #eee;
}
.action {
  display: inline-block;
  height: 100%;
  padding: 0 12px;
  color: inherit !important;
  vertical-align: top;
  cursor: pointer;

  i {
    color: #fff !important;
    font-size: 16px;
    vertical-align: middle;
    &.arrow {
      position: relative;
      top: 1px;
      font-size: 12px;
      transition: all 0.3s;
    }
  }

  &:not(.logo):not(.title):hover,
  &.active,
  &:global(.ant-popover-open) {
    color: inherit;
    background-color: @header-action-active-default-bg;
  }

  &:global(.ant-popover-open) {
    .arrow {
      transform: rotate(180deg);
      transform-origin: 50% 50%;
    }
  }
}

.logo {
  width: 120px;
  height: @header-height;
  padding: 0;
  background: url(../img/header-logo-default.png) no-repeat center center;
  cursor: default;
}

.title {
  position: relative;
  padding: 0 8px 0 0;
  font-size: 13px;
  cursor: default;
  // &::before {
  //   position: absolute;
  //   top: 50%;
  //   left: 0;
  //   width: 1px;
  //   height: 14px;
  //   margin-top: -6px;
  //   background: #eee;
  //   content: '';
  // }
}

.allMenu {
  font-size: 20px;
  // text-align: center;
  cursor: pointer;
  i {
    vertical-align: 1px;
  }
}

.marquee{
  // max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  box-sizing: border-box;
  p {
    margin-bottom: 0;
    /*margin-top: 2px;*/
  }

  .scrollMarquee{
    animation: scroll 10s linear infinite;
    &:hover{
      animation-play-state: paused; /* 鼠标悬停时暂停动画 */
    }
  }
}

@keyframes scroll {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(-100%);
  }
}

.allMenuDropdown {
  position: fixed;
  top: @header-height;
  right: 0;
  left: 0;
}

.allMenuDropdown2 {
  position: fixed;
  top: @header-height;
  :global {
    .ant-tabs-bar {
      border: none;
    }
    .ant-tabs-nav {
      float: left;
    }
    .ant-tabs-tab {
      font-size: 14px;
      line-height: 22px;
    }
  }
}

.actionWrapper {
  position: relative;
  height: 100%;
}

.searchWrapper {
  position: absolute;
  top: 50%;
  left: 0;
  display: none !important;
  width: 320px;
  margin-top: -12px;
  transition: all 150ms;
  &.active {
    left: -320px;
    display: block !important;
  }
  :global {
    .ant-select-selection {
      &:hover,
      &:active,
      &:focus {
        border-color: #d9d9d9;
      }
    }
    .ant-select-auto-complete.ant-select {
      .ant-input {
        &:hover,
        &:focus {
          border-color: #d9d9d9;
        }
      }
    }
  }
  // :global(.ant-select-selection):hover {
  // }
}

.hide {
  display: none;
}

.billContent{
  :global {
    .ant-menu-inline, .ant-menu-vertical{
      width: 100px;
      border: none;
    }
  }
}

.menu {
  :global(.anticon) {
    margin-right: 8px;
  }
  :global(.ant-dropdown-menu-item) {
    min-width: 160px;
  }
  &:global(.ant-menu) {
    border-right: none;
  }
  :global(.ant-menu-item) {
    &:hover {
      background-color: @blue-1;
    }
    // 禁用选中高亮
    &:global(.ant-menu-item-selected) {
      &:hover {
        background-color: @blue-1;
        > a {
          color: @blue-6;
        }
      }

      background-color: #fff;
      > a {
        color: @text-weak-color;
      }
    }
  }
}
.avatar {
  img {
    border: 2px solid transparent;
    border-radius: 50%;
  }
  i {
    margin: 0 5px;
    font-size: 12px;
  }
}

// .notice {
// }

// .logout {
// }

:global(.theme-blue) {
  .wrapper {
    color: @header-blue-text;
    background: @header-blue-bg;
    border-bottom-color: @header-border-blue-color;
  }

  .avatar {
    img {
      border: 2px solid @blue-2;
    }
  }

  .title {
    &::before {
      background: #fff;
    }
  }

  .logo {
    font-size: 0;
    background-image: url(../img/js-header-logo.png);
    background-size: 96px;
  }

  .action {
    &:not(.logo):not(.title):hover,
    &:global(.ant-popover-open),
    &.active {
      background-color: @header-action-active-blue-bg !important;
    }
  }
}

:global {
  /* 系统菜单展开开始 */
  .topbar-dropdown-box {
    position: fixed;
    z-index: 1000;
    left: 80px;
    right: 80px;
    padding: 0 16px 16px;
    max-height: 80%;
    overflow: hidden;
    font-size: @font-size-base;
    background: #fff;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.16);
    ul {
      margin: 0;
      padding: 0;
      list-style: none;
    }
    .ant-tabs-bar{
      margin: 0 15px 16px 0;
    }
    .inline-block {
      display: inline-block;
    }
    .y-list {
      margin: 0;
      // margin-top: @slick-space-base;
      padding: 0;
      list-style: none;
      li {
        position: relative;
        i {
          position: absolute;
          top: 8px;
          right: 8px;
          display: none;
          margin-top: 5px;
          font-size: 12px;
        }
        a {
          display: block;
          padding: 8px 16px 8px 8px;
          color: @menu-color;
          line-height: 20px;
        }
        &:hover {
          background: #f3f3f3;
          i {
            display: block;
          }
        }
        &.active {
          background-color: @primary-1;
          i {
            display: block;
          }
          a {
            color: @blue-6;
          }
        }
      }
    }

    .y-col {
      position: relative;
      float: left;
      width: 180px;
      height: 100%;
      line-height: 20px;
      background-color: #fff;
      border: 1px solid #eee;
      .y-col-header {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        height: 36px;
        padding: 8px;
        color: @text-color-secondary;
      }
      .y-col-body {
        position: absolute;
        top: 36px;
        right: 0;
        bottom: 0;
        left: 0;
        overflow-x: hidden;
        overflow-y: auto;
      }
      .y-col-body-menu4 {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        overflow-x: hidden;
        overflow-y: auto;
      }
    }
    .bg-grey {
      background-color: #fafafa;
    }
    .y-row {
      position: relative;
      width: 100%;
      height: 100%;
      &::after {
        display: block;
        clear: both;
        height: 0;
        font-size: 0;
        visibility: hidden;
        content: ' ';
      }
    }

    .letter-panel {
      position: relative;
      height: 100%;
      overflow: hidden;
      .letter-search {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        height: 36px;
        padding: 6px 16px;
      }
      .letter-category {
        position: absolute;
        top: 36px;
        right: 0;
        bottom: 0;
        left: 0;
        padding: 0 16px 8px;
        overflow-x: hidden;
        overflow-y: auto;
        .letter-title {
          padding: 8px;
          color: @blue-6;
          font-weight: bold;
          font-size: 14px;
          line-height: 20px;
        }
        ul {
          margin: 0;
          padding: 0;
          list-style: none;
          li {
            &.ng-item {
              a {
                display: block;
                padding: 8px;
                color: @menu-color;
                line-height: 20px;
                &:hover {
                  color: @menu-color;
                  background-color: #f0f0f0;
                }
              }
            }
          }
          &::after {
            display: block;
            clear: both;
            height: 0;
            font-size: 0;
            visibility: hidden;
            content: ' ';
          }
        }
      }
    }
  }

  // 用户信息面板
  .slick-dropdown {
    width: auto;
    padding: 20px 30px;
    background: #fff;
    box-shadow: 0px 12px 48px 16px rgba(0, 28, 56, 0.03), 0px 9px 28px 0px rgba(0, 28, 56, 0.05), 0px 6px 16px -8px rgba(0, 28, 56, 0.08);
    ul {
      margin: 0;
      padding: 0;
      list-style: none;
    }
    .dropdown-memu-row {
      position: relative;
      height: 100%;
      margin: 0;
      padding: 0;
      .panel {
        border: 1px solid #d4d5d6;
        border-radius: 2px;
        .panel-heading {
          position: relative;
          padding: 5px 10px;
          background-color: #f9f9f9;
          border-color: #d4d5d6;
          border-bottom: 1px solid #d4d5d6;
          border-radius: 8px 8px 0 0;
          &::before {
            display: inline-block;
            width: 6px;
            height: 16px;
            margin-top: -4px;
            margin-right: @slick-space-base;
            vertical-align: middle;
            background: @blue-6;
            border-radius: 2px;
            content: '';
          }
        }
        .panel-body {
          height: 130px;
          padding-top: 7px;
          // height: 120px;
          // padding-top: 22px;
        }
        .panel-footer {
          background: none;
          border: none;
        }
      }
      .dropdown-memu-col {
        //padding: 0;
        position: relative;
        height: 100%;
        overflow-x: hidden;
        //border-right: 1px solid @border-color;
        //padding: 20px 10px;
        overflow-y: auto;
        &:nth-child(2) {
          padding: 0 10px;
        }
        &:last-child {
          border-right: none;
        }
        .cv-role-info {
          padding-top: 22px;
          ul {
            list-style: none;
          }
          li {
            float: left;
            width: 50%;
            padding: 5px 0;
            text-align: center;
            list-style: none;
            &.active {
              a {
                color: #fff;
                background-color: @blue-5;
                &:hover {
                  color: #fff;
                }
              }
            }
            a {
              display: inline-block;
              width: 50%;
              padding: 5px 0;
              color: #000;
              white-space: nowrap;
              background-color: #f9f9f9;
              border-radius: 2px;
              &:hover {
                color: @blue-5;
              }
            }
          }
        }
      }
      .dropdown-group-main {
        position: absolute;
        top: 0;
        right: 0;
        bottom: (@font-size-base* @line-height-base+ @padding-base-vertical*2) + @padding-base-vertical*2;
        left: 0;
        z-index: 1;
        overflow-x: hidden;
        overflow-y: auto;
      }
      .dropdown-group-control {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: (@font-size-base* @line-height-base+ @padding-base-vertical*2) + @padding-base-vertical*2;
        padding: @padding-base-vertical @padding-base-horizontal;
        border-top: 1px solid @divide-color;
        .group-control-tips {
          display: inline-block;
          height: @input-height-base;
          line-height: @input-height-base;
        }
      }
      .dropdown-memu-col.user-info-col {
        .panel-body {
          background: #f9f9f9;
          border-radius: 0 0 8px 8px;
        }
        //background: #e7ecf3;
        .user-info-dt {
          color: @menu-color;
        }
        .user-info-dd {
          color: @text-weak-color;
        }
        .user-info-dl,
        .user-info-dt,
        .user-info-dd {
          margin: 0;
          padding: 0;
        }
        .user-info-dl {
          margin-bottom: @padding-small-horizontal;
        }
        .user-info-cont,
        .user-info-address {
          position: relative;
          padding-left: 40px;
          .user-info-icon {
            position: absolute;
            top: 0;
            left: 0;
            font-size: 16px;
          }
        }
        .user-info-cont {
          margin-bottom: @padding-large-horizontal;
          padding-top: 22px;
          .user-info-dt,
          .user-info-dd {
            display: inline;
          }
          .user-info-icon {
            color: @brand-success;
          }
          i.fa {
            width: 30px;
            text-align: center;
            //color: @portal-tabs-active-bg;
            &.fa-id-card-o {
              color: @portal-tabs-active-bg;
            }
            &.fa-window-maximize {
              color: #72d1f0;
            }
            &.fa-desktop {
              color: #40fb99;
            }
          }
        }
        .user-info-address {
          .user-info-dt,
          .user-info-dd {
            display: block;
          }
          .user-info-dt {
            color: @text-weak-color;
            font-weight: normal;
          }
          .user-info-dd {
            color: @menu-color;
            font-weight: bold;
          }
          .user-info-icon {
            color: @primary-5;
          }
        }
      }
    }
    .list {
      margin: 0 8px;
      padding: 0;
      overflow: hidden;
      > li {
        float: left;
        width: 33.333333%;
        list-style: none;
      }
      .entrance-tit {
        display: block;
        margin: @padding-small-vertical;
        padding: @padding-small-vertical @padding-base-horizontal;
        overflow: hidden;
        color: @text-weak-color;
        white-space: nowrap;
        text-align: center;
        text-overflow: ellipsis;
        background: #f9f9f9;
        border-radius: 2px;
        cursor: pointer;
        transition: none;
        &:hover,
        &.active {
          color: #fff;
          background: @blue-5;
        }
      }
    }
  }
}

.searchRecommend {
  display: inline-block;
  width: 380px;
  height: 340px;
  overflow-y: auto;
  background-color: #f5f9ff;

  .unit {
    padding: 16px 10px 16px 16px;
    line-height: 20px;

    img {
      vertical-align: -0.05em;
      line-height: 0;
    }

    .title {
      display: inline-block;
      margin-bottom: 16px;
      color: #000;
      font-weight: 400;
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
    }

    .label {
      display: inline-block;
      margin: 0 4px 8px 0;
      padding: 0 6px;
      color: #000;
      font-size: 12px;
      line-height: 22px;
      border: 1px solid #e6e6e6;
      background-color: #fafafa;
      border-radius: 2px;
      cursor: pointer;

      &.yellow {
        color: #faaf0c;
        border-color: #ffe58f;
        background-color: #fffbe6;
      }
    }
  }
}

.searchDropdownBox {
  // position: fixed;
  // left: 77px;
  // z-index: 1000;
  width: 1114px;
  height: 340px;
  overflow: hidden;
  font-size: @font-size-base;
  background: #fff;
  border: 1px solid #d4d5d6;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  .inline-block {
    display: inline-block;
  }
  .y-list {
    margin: 0;
    // margin-top: @slick-space-base;
    padding: 0;
    list-style: none;
    li {
      position: relative;
      i {
        position: absolute;
        top: 8px;
        right: 8px;
        display: none;
        margin-top: 5px;
        font-size: 12px;
      }
      a {
        display: block;
        padding: 8px 16px 8px 8px;
        color: @menu-color;
        line-height: 20px;
      }
      &:hover {
        background: #f3f3f3;
        i {
          display: block;
        }
      }
      &.active {
        background-color: @primary-1;
        i {
          display: block;
        }
        a {
          color: @blue-6;
        }
      }
    }
  }

  .y-col {
    position: relative;
    float: left;
    width: 180px;
    height: 100%;
    line-height: 20px;
    background-color: #fff;
    border: 1px solid #eee;
    .y-col-header {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 36px;
      padding: 8px;
      color: @text-color-secondary;
    }
    .y-col-body {
      position: absolute;
      top: 36px;
      right: 0;
      bottom: 0;
      left: 0;
      overflow-x: hidden;
      overflow-y: auto;
    }
    .y-col-body-menu4 {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
  .bg-grey {
    background-color: #fafafa;
  }
  .row {
    position: relative;
    width: 1114px;
    height: 100%;
    &::after {
      display: block;
      clear: both;
      height: 0;
      font-size: 0;
      visibility: hidden;
      content: ' ';
    }
  }

  .letter-panel {
    position: relative;
    display: inline-block;
    width: 730px;
    height: 100%;
    overflow: hidden;
    .letter-search {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 36px;
      padding: 6px 16px;
    }
    .letter-category {
      position: absolute;
      top: 10px;
      right: 0;
      bottom: 0;
      left: 0;
      padding: 0 16px 8px;
      overflow-x: hidden;
      overflow-y: auto;
      .letter-title {
        padding: 6px 8px;
        color: @blue-6;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
      }
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        li {
          &.ng-item {
            a {
              display: block;
              padding: 6px 8px;
              color: #666;
              line-height: 20px;
            }
          }
        }
        &::after {
          display: block;
          clear: both;
          height: 0;
          font-size: 0;
          visibility: hidden;
          content: ' ';
        }
      }
    }
  }
}

.search {
  display: inline-block;

  .searchInput {
    width: 220px;

    :global {
      .ant-input {
        border-radius: 18px;
      }
      .anticon-search {
        color: #0085d0;
      }
    }
  }
}

.headerSearchMenuWrap {
  // position: relative;
  // display: inline-block;
  top: 64px !important;
  right: 144px !important;
  left: auto !important;
  :global {
    .ant-popover-content {
      .ant-popover-inner-content {
        padding: 0;
      }
    }

    .ant-popover {
      top: 64px !important;
    }
  }

  .letter-title {
    color: #999999 !important;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      width: 80px;
      height: 1px;
      background-color: #e6e6e6;
      bottom: 2px;
      left: 8px;
    }
  }
  .ng-item {
    color: #666 !important;
  }

  .clearAll {
    color: #999;
    font-size: 12px;
    cursor: pointer;

    &:hover {
      color: #666;
    }
  }
}

.onlineCard{
  width: 180px;
  color: #333;
  font-size: 18px;
  background: #FFF;
  .onlineBtn{
    display: flex;
    justify-content: space-around;
    margin-top: 12px;
  }
  :global{
    .ant-btn{
      height: 28px;
      padding: 0 24px;
      font-size: 14px;
      line-height: 26px;
    }
  }
}
:global(.ant-popover-placement-bottom) {
  z-index: 100 !important;
}
