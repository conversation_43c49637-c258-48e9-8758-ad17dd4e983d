@import './variables.less';

:global {
  .menu-drawer-basic {
    width: 0;
    background-color: #fff;
    border-right: 1px solid #e8e8e8;
    .reset {
      margin-bottom: 8px;
      .ant-tabs-nav-animated,
      .ant-tabs-nav-container {
        width: @menu-drawer-width - 16;
        transform: none !important;
      }
      .ant-tabs-nav-container {
        margin-left: 8px;
      }
      .ant-tabs-nav-container-scrolling {
        padding: 0 !important;
      }
      .ant-tabs-tab-prev,
      .ant-tabs-tab-next {
        display: none !important;
        width: 0 !important;
      }
      .ant-tabs-tab {
        width: 50%;
        margin: 0;
        padding: 8px 0;
        color: @text-color-secondary;
        text-align: center;
        &.ant-tabs-tab-active {
          color: @blue-6;
        }
      }
      .ant-tabs-ink-bar-animated {
        // transform: none !important;
        width: @menu-drawer-width / 2 !important;
      }
    }

    .ant-menu-inline {
      border-right: none;
      // .ant-menu-submenu {
      //   border-bottom: 1px solid #e8e8e8;
      // }
    }
    //   .ant-menu-sub {
    //     background-color: #f9f9fd;
    //     > li:last-child {
    //       border-bottom: none;
    //     }
    //   }
    // }

    // 禁用选中右侧的高亮样式
    .ant-menu-item-selected {
      // background-color: #fff !important;
      // > a {
      //   color: @text-color;
      // }
      &::after {
        border-right-color: transparent;
        transform: none;
        opacity: 0;
      }
    }
  }
}

.wrap {
  display: flex;
  overflow: hidden;

  .first {
    flex-shrink: 0;
    width: 50px;
    border-right: 1px solid #e8e8e8;

    .unit {
      width: 49px;
      height: 40px;
      margin-top: 15px;
      padding-top: 9px;
      cursor: pointer;
    }

    .active {
      border-left: 3px solid @blue-6;
    }

    .notActive {
      border-left: 3px solid #FFF;
    }

    .icon {
      width: 49px;
      padding-left: 11px;

      img {
        cursor: pointer;
      }

    }

    .textWrap {
      margin-top: 6px;
      text-align: center;

      .text {
        display: inline-block;
        width: 32px;
        font-size: 12px;
        line-height: 16px;
        cursor: pointer;
      }

      .blue {
        color: @blue-6;
      }
    }

    .waitBadge {
      position: relative;
      display: flex;
      justify-content: center;
      // width: 49px;
      // margin-left: -14px;
      :global {
        .ant-badge {
          position: absolute;
          top: -8px;
          // right: 9px;
        }
        .ant-badge-multiple-words{
          padding: 0 4px;
        }
        // .ant-badge-count {
        //   top: -9px;
        //   right: 12px;
        // }
      }
    }
  }

  .second {
    flex-shrink: 0;
    width: 192px;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 191px;
      height: 34px;
      padding: 5px 14px 5px 20px;
      color: @blue-6;
      font-weight: 500;
      border-bottom: 1px solid #e8e8e8;
    }

    .list {
      width: 100%;
      overflow-y: auto;

      :global {
        .ant-menu-inline {
          width: calc(100% - 1px);
        }
      }
    }
  }
}

.taskMenuList {

  .menuText {
    display: inline-block;
    max-width: 101px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
  :global {
    .ant-menu-submenu-title {
      padding-left: 11px !important;
    }

    .ant-menu-submenu-arrow {
      display: none;
    }

    .ant-menu-item {
      padding-left: 43px !important;

      .text-ellipsis {
        overflow: visible;
      }
    }
  }
}

.operation_manual {
  .operation_manual_search {
    padding: 16px 16px 0 16px;
  }
  .operation_manual_tree {
    margin: 0 16px;
    .system_menu_tree_edit {
      // display: none;
      padding-left: 8px;
      visibility: hidden;
      .icon {
        padding-right: 8px;
      }
      .icon:hover {
        color: #1890ff;
      }
    }
  }
  :global {
    .ant-tree li .ant-tree-node-content-wrapper:hover .ant-tree-title > span > span:nth-child(2) {
      // display: inline-block;
      visibility: visible;
    }
  }
  a{
    color: rgba(0, 0, 0, 0.65);
    &:active,&:hover,&:visited{
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

.navRightMenu {
  position: absolute;
  z-index: 999;
  background-color: white;
  border: 1px solid #e8e8e8;

  :global {
    .ant-menu-item {
      height: 21px;
      line-height: 21px;
    }
  }
}
