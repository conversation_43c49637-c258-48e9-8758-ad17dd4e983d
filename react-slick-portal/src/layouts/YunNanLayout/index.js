/* eslint-disable no-restricted-syntax */
/* eslint-disable no-console */
/* eslint-disable indent */
import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Tabs, message, Modal, Icon, Menu, Dropdown } from 'antd';
import { connect } from 'dva';
import router from 'umi/router';
import { Helmet } from 'react-helmet';
import classNames from 'classnames';
import { formatMessage } from 'umi-plugin-react/locale';
import { find, findIndex, isObject, delay } from 'lodash';
import PubSub from 'pubsub-js';
import moment from 'moment';
import { useDebounceFn, useBoolean } from '@umijs/hooks';
// import Guide from 'byte-guide';
import Header from './components/Header';
import MenuDrawer from './components/MenuDrawer';
import SettingDrawer from './components/SettingDrawer';
import Exception from '@/components/Exception';
import Authorized from '@/utils/Authorized';
import traceLogAction from '@/services/traceLogAction';
import request from '@/utils/request';
import { IEVersion, mouseWheel, getViewPort, isJSON, openMenu, getDataDictsByList } from '@/utils/utils';
import { openWebSocket } from '@/utils/websocket';
// import { wsCreate } from '@/utils/yywebsocket';
// import { getDataDictByCode } from '@/services/common';
import Dashboard from '@/pages/Dashboard';
import Apply from '@/pages/Treasury/Apply';
import IEModal from '@/pages/User/components/IEModal';
// import AddExperience from '@/pages/Librarys/InstantMessage/AddExperience';
// import AddServicelist from '@/pages/Librarys/InstantMessage/AddServicelist';
// import InstantMessage from '@/pages/InstantMessage';
import FloatingBall from './components/FloatingBall';
import FloatingInput from './components/FloatingInput';
import AIAssistantModal from './components/AIAssistantModal';
import UnreadModal from '@/pages/Bulletin/UnreadModal';
import HomeUserGuide from './components/HomeUserGuide';
// import MenuImg from './components/MenuImg';
// import AIBadge from './img/AIBadge.png';
import './less/index.less';

const wheel = mouseWheel();
const { TabPane } = Tabs;
const TITLE = '工作台';

// 把对象的key序列化成小写
function serialize(data) {
  const result = {};
  Object.keys(data).forEach(val => {
    result[`${val.trim().toLocaleLowerCase()}`] = data[`${val}`];
  });
  return result;
}

function triggerResizeEvent() {
  const event = document.createEvent('HTMLEvents');
  event.initEvent('resize', true, false);
  window.dispatchEvent(event);
}

// function hasScrollbar() {
//   return document.body.scrollHeight - 45 > (window.innerHeight || document.documentElement.clientHeight);
// }

// function getScrollTop() {
//   return document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
// }

function BasicLayout(props) {
  const {
    dispatch,
    currMenu,
    location: { pathname },
    isOSSPage,
    all,
    setting: { theme, settingDrawerActive, showSecondMenu, viewMode, size, showLeftMenu, gutter, currentOpenLeftMenu, refreshKey },
    children,
    openMenuModal,
    // librarys = {},
    showUnreadBulletin,
    setting: { YXPluginReady },
  } = props;


  // const loginSuccess = getItem('YXPlugin_loginSuccess');

  const home = {
    key: '/',
    url: '/',
    closable: false,
    title: TITLE,
    content: <Dashboard />,
    bupMenuName: '',
    bupMenuNo: '',
  };

  const [activeKey, setActiveKey] = useState('');
  const [title, setTitle] = useState('');
  const [tabPanes, setTabPanes] = useState([home]);
  // const [wheelDirection, setWheelDirection] = useState(null);
  const [microApp, setMicroApp] = useState();
  const [jinkuFlag, setJinkuFlag] = useState(false);
  const [jkPostMessageInfo, setJkPostMessageInfo] = useState('');
  const [showMask, setShowMask] = useState(false);
  const [showAIBall, setShowAIBall] = useState(false); // 是否展示智能助手悬浮球
  const [showAISearch, setShowAISearch] = useState(false); // 是否展示悬浮搜索框

  // IE浏览器提示
  const { state: visibleIE, setTrue: showIEModal, setFalse: hideIEModal } = useBoolean(false);

  // 鼠标选中文本
  // const [isVisible, setIsVisible] = useState(false);
  // const [popupStyle, setPopupStyle] = useState({});
  // const [selectedWord, setSelectedWord] = useState('');

  const [currentTime, setCurrentTime] = useState('');

  // 防抖，计算当前画布大小
  const { run: setSize } = useDebounceFn(() => {
    const { width, height } = getViewPort(false);
    const $header = document.getElementById('layout-header');
    const $tabsNav = document.querySelector('.layout-tabs-nav');
    // const $pane = document.querySelector('.ant-tabs-tabpane-active');
    const $menuDrawer = document.getElementById('menu-drawer');
    let padding = 0;
    if (viewMode === 'inner' || pathname.includes('/iframe/')) {
      padding = 0;
    } else {
      padding = parseInt(gutter, 10) * 2;
    }
    const contentHeight =
      height -
      ($header === null ? 0 : $header.offsetHeight) -
      ($tabsNav === null ? 0 : $tabsNav.offsetHeight) -
      // ($pane === null
      //   ? 0
      //   : parseInt(getComputedStyle($pane).paddingTop, 10) +
      //     parseInt(getComputedStyle($pane).paddingBottom, 10)) -
      padding;
    const contentWidth = width - ($menuDrawer === null ? 0 : $menuDrawer.offsetWidth);
    dispatch({
      type: 'setting/saveSize',
      payload: { width: contentWidth, height: contentHeight, fullHeight: height, fullWidth: width },
    });
  }, 50);

  /**
   * 改变tab时的回调,实时更新url
   */
  function onTabChange(key) {
    // eslint-disable-next-line no-unused-vars
    for (const item of tabPanes) {
      if (item.key === `${key}`) {
        if (item.openUrl) {
          request(item.openUrl, {
            method: 'GET',
            showError: false,
          });
        }
      }
    }

    setActiveKey(key);

    /**
     * IMPROVE: 比如下面场景：
     * 首先，用Portal.open('/iframe/xxx?abc=123')打开一个iframe页面('?abc=123'部分表示往iframe页面传参)
     * 接着，在未关闭该iframe页面时，又Portal.open('/iframe/xxx?abc=456')  参数部分变了
     * 结果，iframe中的props.location.search未更新，导致传参错误
     * 处理方式：当同一个页面，search变化时，需要更新tabPanes中对应项的url以及children，更新children才能保证他上面挂载的props.location.search被更新
     */
    router.push(find(tabPanes, { key }).url);
  }

  const getMicroName = str => str.replace('/', '');

  // 摧毁keep-alive状态的页签
  const destoryMicroKeepAliveByKey = targetKey => {
    if (microApp && targetKey.indexOf('Micro/') !== -1) {
      const name = getMicroName(targetKey);
      microApp.unmountApp(name, { clearAliveState: true });
    }
  };

  // 摧毁keep-alive状态的页签
  const destoryAllMicroKeepAlive = () => {
    if (microApp) {
      microApp.unmountAllApps({ clearAliveState: true });
    }
  };

  const getShowMask = status => {
    setShowMask(status);
  };

  const closeMenuModal = () => {
    dispatch({
      type: 'menu/saveInitData',
      payload: {
        openMenuModal: {
          visible: false,
          url: '',
        },
      },
    });
  };

  const getShowValue = async () => {
    const result = await getDataDictsByList([
      {
        groupCode: 'SYSTEM_VAR',
        paramCode: 'AI_SHOW',
      },
      {
        groupCode: 'SYSTEM_VAR',
        paramCode: 'AI_SEARCH_SHOW',
      },
    ]);
    if (result?.length > 0) {
      setShowAIBall(`${result[0]?.paramValue}` === '1');
      setShowAISearch(`${result[1]?.paramValue}` === '1');
    }
  };

  const getCurrentTime = () => {
    const date = moment().format('YYYY年MM月DD日 dddd HH:mm:ss');
    setCurrentTime(date);
  };

  const initYanXiPlugin = async () => {
    try {
      const result = await request('portal/BoteController/config.do', {
        method: 'get',
      });

      if (result?.resultObject && result?.resultObject?.enable && window.YanXiPlugin) {
        const { tenantId, csrfToken, signature, apiPrefix, systemCode } = result.resultObject;
        window.YXPlugin = new window.YanXiPlugin({
          position: 'right',
          mode: 'pop',
          width: 480,
          height: 'calc(100% - 48px)',
          tenantId,
          csrfToken,
          signature,
          prefix: apiPrefix,
          systemCode,
          container: document.getElementById('root'),
          chatMode: 'plat',
          opened: () => {
            console.log('智能助手打开');
            dispatch({
              type: 'setting/save',
              payload: { YXPluginReady: true },
            });
          },
        });
        console.log('智能助手初始化');
        window.YXPlugin.openChatWindon(true);
      }
    } catch (error) {
      console.log('智能助手初始化error：', error);
    }
  };

  useEffect(() => {
    getShowValue();
    // 只有在非ie浏览器时，才引入microApp
    if (IEVersion() === -1) {
      import('@micro-zoe/micro-app').then(_microApp => {
        setMicroApp(_microApp);
      });
    } else {
      showIEModal();
    }

    // 智能助手插件初始化
    if (!window.location.href.toLowerCase().includes('yxplugin=0')) {
      initYanXiPlugin();
    }

    const timer = setInterval(() => {
      getCurrentTime(moment());
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  useEffect(() => {
    // let timer = null;
    if (window.YXPlugin && all && dispatch) {
      console.log('副驾openBupMenu方法注册');
      // 注册打开页面的方法
      window.YXPlugin.registerPageFunction('openBupMenu', e => {
        console.log('副驾中打开页面', e);
        const { params } = e;
        const { rowData } = params || {};
        const _delUrl = rowData?.delURL || rowData?.detailUrl;
        if (_delUrl) {
          const [menuCode, urlParams = ''] = _delUrl.split('?');
          let styleParams = {};
          if (menuCode === 'TYMH_MENU_XTGL_QFXQ') {
            styleParams = {
              width: 1000,
              height: 280,
              top: 150,
            };
          }
          openMenu(menuCode, all, dispatch, urlParams ? `?${urlParams}&yxplugin=0` : '', styleParams);
        }
      });

      // wsCreate();
      // // 定时盯轮询
      // timer = setInterval(async () => {
      //   const result = await request('/bia-intellect/notifications/controller/timingDing');
      //   console.log('定时盯任务轮询result：', result);
      //   if (result?.resultObject && Array.isArray(result?.resultObject)) {
      //     window.YXPlugin?.trigger({
      //       pointCode: 'zhengqi#WorkstationAssistant#AIHelpCheck',
      //       params: {
      //         watchType: 'notice',
      //         notification: {
      //           callBackType: '4000',
      //           notifications: result.resultObject,
      //         },
      //       },
      //     });
      //     console.log('收到定时盯关注合同列表数据，发送AIHelpCheck指令');
      //   }
      // }, 30000);
    }
    // if (window.YXPlugin && loginSuccess) {
    //   // 发送指令，查询关键信息
    //   window.YXPlugin.trigger({
    //     pointCode: 'zhengqi#WorkstationAssistant#getKeyMsg',
    //     params: {},
    //   });
    //   console.log('发送查询关键信息指令');

    //   // 清空登录状态（用于副驾）
    //   removeItem('YXPlugin_loginSuccess');
    // }
    // return () => {
    //   if (timer) {
    //     clearInterval(timer);
    //   }
    // };
  }, [YXPluginReady]);

  useEffect(() => {
    if (IEVersion() !== -1) {
      showIEModal();
    }
  }, [currMenu, tabPanes]);

  /**
   * 关闭tab时的回调
   */
  // eslint-disable-next-line no-shadow
  function onTabRemove(targetKey, currentTabKey, tabPanes) {
    // 如果关闭的是当前tab, 要激活哪个tab?
    // 首先尝试激活左边的, 再尝试激活右边的
    let nextTabKey = currentTabKey;
    if (nextTabKey === targetKey) {
      let currentTabIndex = -1;
      tabPanes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          currentTabIndex = i;
        }
      });

      // 如果当前tab左边还有tab, 就激活左边的
      if (currentTabIndex > 0) {
        nextTabKey = tabPanes[currentTabIndex - 1].key;
      } else if (currentTabIndex === 0 && tabPanes.length > 1) {
        // 否则就激活右边的tab
        nextTabKey = tabPanes[currentTabIndex + 1].key;
      }
    }

    setActiveKey(nextTabKey);
    // 关闭时候实时更新url。只有在当前页面与目标新页面路径不同时才更新。
    const nextTabUrl = find(tabPanes, { key: nextTabKey }).url;
    if (pathname !== nextTabUrl) {
      router.replace(nextTabUrl);
    }

    // 过滤panes
    const newTabPanes = tabPanes.filter(pane => pane.key !== targetKey);
    setTabPanes(newTabPanes);

    destoryMicroKeepAliveByKey(targetKey);
  }

  const closeTab = ({ key }) => {
    // const _title = rightMenuInfo?.title;
    const pane = find(tabPanes, { key });
    if (!pane) {
      return;
    }
    onTabRemove(pane.key, activeKey, tabPanes);
  };

  const closeOtherTabs = ({ key }) => {
    // const _title = rightMenuInfo?.title;
    const pane = find(tabPanes, { key });
    if (!pane) {
      return;
    }
    const newTabPanes = tabPanes.filter(item => item.key === '/' || item.key === pane.key);
    setTabPanes(newTabPanes);

    const removedTabs = tabPanes.filter(item => item.key !== '/' && item.key !== pane.key);
    removedTabs.forEach(item => {
      destoryMicroKeepAliveByKey(item.key);
    });

    if (activeKey === '/') {
      router.replace(activeKey);
      return;
    }
    router.replace(pane.url);
  };

  const closeRightSideTabs = ({ key }) => {
    // const _title = rightMenuInfo?.title;
    const index = findIndex(tabPanes, { key });
    const pane = find(tabPanes, { key });
    if (!pane) {
      return;
    }
    const newTabPanes = tabPanes.slice(0, index + 1);
    setTabPanes(newTabPanes);

    const removedTabs = tabPanes.slice(index + 1);
    removedTabs.forEach(item => {
      destoryMicroKeepAliveByKey(item.key);
    });

    if (activeKey === '/') {
      router.replace(activeKey);
      return;
    }
    router.replace(pane.url);
  };

  const closeAllTabs = () => {
    const newTabPanes = tabPanes.filter(item => item.key === '/');
    setTabPanes(newTabPanes);

    destoryAllMicroKeepAlive();
    router.replace('/');
  };

  const renderContextMenu = ({ key }) => (
    <Menu>
      {key !== '/' && (
        <Menu.Item>
          <a onClick={() => closeTab({ key })}>关闭</a>
        </Menu.Item>
      )}
      <Menu.Item>
        <a onClick={() => closeOtherTabs({ key })}>关闭其他菜单</a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={closeAllTabs}>关闭所有菜单</a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={() => closeRightSideTabs({ key })}>关闭右侧菜单</a>
      </Menu.Item>
    </Menu>
  );

  /**
   *
   * @param {string} pathname  当前页面路径
   * @param {object[]} tabPanes
   * @param {object} currMenu  当前菜单信息，必须包含3个字段：{menuId,urlAddr,menuName,menuCode}
   * @param {string} viewMode 视图模式,可选值 'normal | inner'
   */
  function renderBody() {
    return (
      <Authorized
        authority={currMenu === undefined || currMenu === null ? undefined : currMenu.authority}
        noMatch={<Exception type="403" />}
      >
        <div id="layout-body">
          {viewMode === 'inner' ? (
            tabPanes[findIndex(tabPanes, { key: activeKey })] &&
            tabPanes[findIndex(tabPanes, { key: activeKey })].content
          ) : (
            <>
              <Tabs
                activeKey={activeKey}
                renderTabBar={(prop, DefaultTabBar) => (
                  <DefaultTabBar {...prop} className="layout-tabs-nav" />
                )}
                type="editable-card"
                onEdit={targetKey => onTabRemove(targetKey, activeKey, tabPanes)}
                onChange={key => onTabChange(key)}
                hideAdd
                animated={IEVersion() !== 9}
                className="layout-body-main"
                style={{ height: pathname.includes('/iframe/') && 'calc(100% - 5px)' }}
                tabBarExtraContent={<div className="layout-tabs-time">{currentTime}</div>}
              >
                {/**
                 * 100% 是size.height的初始值，表示setSize未计算结束
                 * 必须保证加载children前size计算结束，因为children依赖size的
                 * 必须返回undefined。返回null 会出现warning
                 */}
                {size.height === '100%'
                  ? undefined
                  : tabPanes.map(pane => (
                      // eslint-disable-next-line react/jsx-indent
                      <TabPane
                        tab={
                          pane.key === '/' ? (
                            <Dropdown overlay={() => renderContextMenu({ key: '/' })} trigger={['contextMenu']}>
                              <span>
                                <Icon type="home" className="margin-right-sm" />
                                {TITLE}
                              </span>
                            </Dropdown>
                          ) : (
                            <Dropdown overlay={() => renderContextMenu({ key: pane.key })} trigger={['contextMenu']}>
                              <span>{pane.bupMenuName ? pane.bupMenuName : pane.title}{pane.bupMenuNo ? `#${pane.bupMenuNo}` : ''}</span>
                            </Dropdown>
                          )
                        }
                        key={pane.key}
                        closable={pane.key !== '/'}
                        style={{ padding: !pathname.includes('/iframe/') && '8px' }}
                      >
                        <div>
                          {refreshKey !== pane.key && pane.content}
                        </div>
                      </TabPane>
                    ))}
              </Tabs>
              {showLeftMenu && <MenuDrawer currMenu={currMenu} pathname={pathname} history={props?.history} />}

              {/* <MenuImg
                style={{ position: 'fixed', bottom: '20px', left: '10px', cursor: 'pointer' }}
                width={32}
                height={32}
                type={currentOpenLeftMenu ? 'expanded1' : 'expand1'}
                onClick={() => {
                  dispatch({
                    type: 'setting/saveCurrentOpenLeftMenu',
                    payload: !currentOpenLeftMenu,
                  });
                  triggerResizeEvent();
                }}
              /> */}
            </>
          )}
        </div>
      </Authorized>
    );
  }

  /**
   * 监听postMessage指令
   * 指令格式：{to:"portal",action:"close|open",pathname:"/notice"}
   */
  useEffect(() => {
    // 监听来自低代码平台的提示消息
    function handleLowCode(event) {
      if (!event.data || !event.data.u__$Data) {
        return;
      }
      const data = event.data.u__$Data;
      const { to, action, type, content } = serialize(data);
      if (to !== 'portal' || action !== 'tip') {
        return;
      }
      if (type === 'success') {
        message.success(content);
      } else if (type === 'error') {
        message.error(content);
      } else {
        message.warning(content);
      }
    }

    function handleActions(event) {
      let data;
      // 判断是否是来自低代码平台的消息（u__$Data）
      if (event.data && event.data.u__$Data) {
        data = event.data.u__$Data;
      } else {
        if (!isJSON(event.data)) {
          return;
        }
        data = JSON.parse(event.data);
        if (!isObject(data)) {
          return;
        }

        if (data.action === 'bup#treasuryInfo') {
          // 拉起金库弹窗
          console.log('receive jkPostMessage， data：', data);
          if (data.dataInfo) {
            setJinkuFlag(true);
            setJkPostMessageInfo(data.dataInfo);
          }
          return;
        }

        if (data.action === 'bup#opneInstantMessage') {
          // 拉起即时通讯弹窗
          console.log('receive instantMessage  PostMessage， data：', data);

          if (!isOSSPage) {
            const { userCode } = data.data;
            if (userCode) {
              request('orgauth/SystemUserController/getUserByUserCode.do', { method: 'post', data: userCode }).then(res => {
                if (res.resultObject) {
                  const userObj = {
                    userId: res.resultObject.sysUserId,
                    userName: res.resultObject.userName,
                  };
                  openWebSocket(userObj);
                }
              });
            }
          } else {
            const { type } = data.data;
            if (!type) {
              return;
            }
            openWebSocket(data.data);
          }
          return;
        }

        if (data.action === 'bup#openAIModal') {
          // 打开智能助手
          const { keyword } = data.data || {};
          PubSub.publish('systemOpt_openAIModal', {
            keyword,
            isCenter: false,
          });
          return;
        }

        if (data.action === 'bup#closeAIModal') {
          // 关闭智能助手
          const { keyword } = data.data || {};
          PubSub.publish('systemOpt_closeAIModal', { keyword });
          return;
        }

        if (data.action === 'bup#openYXJ') {
          // 打开云小集
          const { keyword } = data.data || {};
          PubSub.publish('systemOpt_openYXJ', { keyword });
          return;
        }

        if (data.action === 'bup#closeYXJ') {
          // 关闭云小集
          const { keyword } = data.data || {};
          PubSub.publish('systemOpt_closeYXJ', { keyword });
          return;
        }
      }

      // 把对象中的key全部变小写且去头尾空格
      // eslint-disable-next-line no-shadow
      const { to, action, pathname, targetmenu, directopen } = serialize(data);

      // 子系统主动发送校验登录
      if (to?.toLocaleLowerCase() === 'portal' && action?.toLocaleLowerCase() === 'check') {
        dispatch({
          type: 'login/logout',
        });
        return;
      }

      // 不是有效指令，
      if (!to || !action || !pathname || to.trim().toLocaleLowerCase() !== 'portal' || !['close', 'open'].includes(action.toLocaleLowerCase())) {
        // message.error('无效指令，正确格式：{to:"portal",action:"close|open",pathname:""}');
        return;
      }
      // 格式化pathname,防止不以斜杆开头，或以斜杆结尾
      let formatPathname = pathname;
      if (!/^\/\w+/i.test(pathname)) {
        formatPathname = `/${pathname}`;
      }
      if (pathname.endsWith('/')) {
        formatPathname = formatPathname.slice(0, formatPathname.length - 1);
      }
      if (action.toLocaleLowerCase() === 'close') {
        destoryMicroKeepAliveByKey(formatPathname);
        const newTabPanes = [...tabPanes.filter(item => item.key !== formatPathname)];
        router.replace(targetmenu ? decodeURIComponent(targetmenu) : newTabPanes[newTabPanes.length - 1].key);
        setTabPanes(newTabPanes);
      }

      if (action.toLocaleLowerCase() === 'open') {
        // directopen表示直接打开，不需要通过openMenu去判断（防止循环引用）
        if (directopen === 'true' || directopen === true) {
          router.replace(formatPathname);
          return;
        }

        // 用于去除url从?开始的参数内容
        const endIndex = formatPathname.indexOf('?') === -1 ? formatPathname.length : formatPathname.indexOf('?');
        const urlParams = formatPathname.slice(endIndex);

        // 如果是打开/iframe/xxx，则用menucode去打开，xxx就是menucode
        if (formatPathname.startsWith('/iframe')) {
          openMenu(formatPathname.slice(8, endIndex), all, dispatch, urlParams);
          return;
        }

        // 如果是打开工作台页面，则通过url去搜索对应的menu
        const urlAddr = formatPathname.slice(0, endIndex);
        const menu = all.find(item => item.urlAddr === urlAddr);

        openMenu(menu, all, dispatch, urlParams);
      }
    }

    // closeAll 关闭所有标签

    function closeAll(event) {
      if (!isJSON(event.data)) {
        return;
      }
      const data = JSON.parse(event.data);
      if (!isObject(data)) {
        return;
      }
      // 把对象中的key全部变小写且去头尾空格
      const { action } = serialize(data);

      if (action?.toLocaleLowerCase() === 'closeall') {
        destoryAllMicroKeepAlive();
        const newTabPanes = [...tabPanes.filter(item => item.key === '/')];
        setTabPanes(newTabPanes);
      }
    }

    // window.self === window.top 返回false 说明页面嵌套在iframe中 不需监听当前的postMessage 主系统已经监听
    if (window.self === window.top) {
      window.addEventListener('message', handleActions);
      window.addEventListener('message', handleLowCode);
    }

    window.addEventListener('message', closeAll);

    return () => {
      window.removeEventListener('message', handleActions);
      window.removeEventListener('message', handleLowCode);
    };
  }, [tabPanes]);

  // 监听tab变化切换
  useEffect(() => {
    setSize();
  }, [activeKey]);
  // 监听画布size
  useLayoutEffect(() => {
    setSize();
    window.addEventListener('resize', setSize);
    return () => {
      window.removeEventListener('resize', setSize);
    };
  }, [setSize]);

  // 修复'react-grid-layout'组件在宽度变化时不能适配的bug
  useLayoutEffect(() => {
    if (pathname === '/' || pathname === '/customize/user' || pathname === '/customize/role') {
      delay(triggerResizeEvent, 100);
    }
  }, [pathname]);

  const renderPath = params => {
    if (params) {
      if (params.startsWith('?')) {
        return params;
      }
      return `?${params}`;
    }
    return '';
  };

  // 负责更新TabPanes
  // 根据pathname和all判断当前页面是否存在于菜单中
  useEffect(() => {
    if (pathname.toLocaleLowerCase() !== '/') {
      const newMenu = currMenu;
      if (newMenu !== undefined) {
        setTitle(newMenu.menuName);
        const { bupMenuName, bupMenuNo } = props.location.query || {};
        const urlTemp = pathname + renderPath(props.location.search);

        // 是否记录菜单操作
        let menuOpt = false;

        // pathname + query 都相同直接跳转，如果pathname相同但query不相同，有2种方案：1、同标签打开刷新；2、打开多个标签，标签名为详情名
        // menuOpenMode === '1' ---> 常规方式，同页面不管路径和参数，都用同一个标签打开
        // menuOpenMode === '5' ---> 同页面,相同路径,不同参数时：打开多个标签【多标签】
        // menuOpenMode === '6' ---> 同页面,相同路径,不同参数时：刷新该标签页【重载】
        if (newMenu.menuOpenMode === '5') {
          // 如果key（pathname + query）不存在就要新增一个tabPane
          if (find(tabPanes, { key: urlTemp }) === undefined) {
            setTabPanes([
              ...tabPanes,
              {
                key: urlTemp,
                url: urlTemp,
                title: newMenu.menuName,
                content: children,
                openUrl: currMenu.ifAsync && currMenu.ifAsync === '1000' ? currMenu.openUrl : null,
                menuOpenMode: newMenu.menuOpenMode,
                hasRightListen: false, // 是否监听右击事件
                bupMenuName: bupMenuName || '',
                bupMenuNo: bupMenuNo || '',
              },
            ]);
            setActiveKey(urlTemp);
            menuOpt = true;
          } else {
            const existTabPane = find(tabPanes, { key: urlTemp });
            if (existTabPane.url !== urlTemp) {
              setTabPanes([
                ...tabPanes,
                {
                  key: urlTemp,
                  url: urlTemp,
                  title: newMenu.menuName,
                  content: children,
                  openUrl: currMenu.ifAsync && currMenu.ifAsync === '1000' ? currMenu.openUrl : null,
                  menuOpenMode: newMenu.menuOpenMode,
                  hasRightListen: false, // 是否监听右击事件
                  bupMenuName: bupMenuName || '',
                  bupMenuNo: bupMenuNo || '',
                },
              ]);
            }
            setActiveKey(urlTemp);
          }
        } else if (newMenu.menuOpenMode === '6') {
          // 如果（pathname）不存在就要新增一个tabPane
          if (!tabPanes.some(tabPane => tabPane.key && tabPane.key.includes(pathname))) {
            setTabPanes([
              ...tabPanes,
              {
                key: urlTemp,
                url: urlTemp,
                title: newMenu.menuName,
                content: children,
                openUrl: currMenu.ifAsync && currMenu.ifAsync === '1000' ? currMenu.openUrl : null,
                menuOpenMode: newMenu.menuOpenMode,
                hasRightListen: false, // 是否监听右击事件
                bupMenuName: bupMenuName || '',
                bupMenuNo: bupMenuNo || '',
              },
            ]);
            setActiveKey(urlTemp);
            menuOpt = true;
          } else {
            const existTabPane = find(tabPanes, { key: urlTemp });
            // pathname存在，即已打开标签，但参数不同时，进行标签刷新
            if (!existTabPane) {
              const updatedTabPanes = tabPanes.map(tabPane => {
                if (tabPane.key && tabPane.key.includes(pathname)) {
                  return {
                    key: urlTemp,
                    url: urlTemp,
                    title: newMenu.menuName,
                    content: children,
                    openUrl: currMenu.ifAsync && currMenu.ifAsync === '1000' ? currMenu.openUrl : null,
                    menuOpenMode: newMenu.menuOpenMode,
                    hasRightListen: false, // 是否监听右击事件
                    bupMenuName: bupMenuName || '',
                    bupMenuNo: bupMenuNo || '',
                  };
                }
                return tabPane;
              });
              setTabPanes(updatedTabPanes);
            }
            setActiveKey(urlTemp);
          }
        } else {
          // 常规标签打开方式
          // 如果key不存在就要新增一个tabPane
          const newPathname = pathname.includes('/iframe/') ? pathname : `${pathname}${props.location.search}`;
          const existTabPane = find(tabPanes, { key: newPathname });
          if (!existTabPane) {
            setTabPanes([
              ...tabPanes,
              {
                key: newPathname,
                url: urlTemp,
                title: newMenu.menuName,
                content: children,
                openUrl: currMenu.ifAsync && currMenu.ifAsync === '1000' ? currMenu.openUrl : null,
                menuOpenMode: newMenu.menuOpenMode,
                hasRightListen: false, // 是否监听右击事件
                bupMenuName: bupMenuName || '',
                bupMenuNo: bupMenuNo || '',
              },
            ]);
            menuOpt = true;
          } else {
            const existIndex = findIndex(tabPanes, { key: newPathname });
            if (existTabPane.url !== urlTemp) {
              tabPanes.splice(existIndex, 1, {
                key: newPathname,
                url: urlTemp,
                title: newMenu.menuName,
                content: children,
                openUrl: currMenu.ifAsync && currMenu.ifAsync === '1000' ? currMenu.openUrl : null,
                menuOpenMode: newMenu.menuOpenMode,
                hasRightListen: false, // 是否监听右击事件
                bupMenuName: bupMenuName || '',
                bupMenuNo: bupMenuNo || '',
              });
              setTabPanes(tabPanes);
            }
          }
          setActiveKey(newPathname);
        }

        if (menuOpt) {
          // 记录打开菜单
          traceLogAction.traceOpenMenuLog({
            menuId: currMenu.menuId,
            menuName: currMenu.menuName,
          });

          // 新增打开标签发起请求
          if (currMenu.ifAsync && currMenu.ifAsync === '1000') {
            // request()
            if (currMenu.openUrl) {
              request(currMenu.openUrl, {
                method: 'GET',
                showError: false,
              });
            }
          }

          // 在菜单有展示而非隐藏时
          if (currMenu.showFlag === '1000') {
            // 更新历史记录菜单
            dispatch({
              type: 'menu/updateRecentMenu',
              payload: currMenu.menuId,
            });
          }
        }
      }
    } else {
      setTitle(TITLE);
      setActiveKey('/');
    }
  }, [pathname, all, currMenu, props.location.search, children, dispatch, home.title]);

  // const handleMouseUp = () => {
  //   const isIframe = pathname.includes('/iframe/');
  //   let selection = null;
  //   if (isIframe) {
  //     const menuCode = pathname.split('/iframe/')[1];
  //     selection = document.getElementById(menuCode)?.contentWindow?.document;
  //   } else {
  //     selection = window.getSelection();
  //   }
  //   if (selection.rangeCount > 0 && selection.toString().trim() !== '') {
  //     const range = selection.getRangeAt(0);
  //     const { bottom, top, left, right, width } = range.getBoundingClientRect();
  //     const { clientWidth, clientHeight } = document.body;

  //     let styleObj = {
  //       position: 'absolute',
  //     };

  //     // 计算弹窗显示横向位置，正常情况下显示在选中文本右侧，当选中文本右侧宽度 < 弹窗最大宽度400时，弹窗显示在文本左边
  //     if (clientWidth - right < 400) {
  //       styleObj = {
  //         ...styleObj,
  //         right: `${clientWidth - left - window.scrollX}px`,
  //       };
  //     } else {
  //       styleObj = {
  //         ...styleObj,
  //         left: `${left + window.scrollX + width}px`,
  //       };
  //     }

  //     // 计算弹窗显示纵向位置，正常情况下显示在选中文本下方，当选中文本下方高度 < 弹窗最小高度50时，弹窗显示在文本上方
  //     if (clientHeight - bottom < 50) {
  //       styleObj = {
  //         ...styleObj,
  //         top: `${top + window.scrollY - 50}px`,
  //       };
  //     } else {
  //       styleObj = {
  //         ...styleObj,
  //         top: `${bottom + window.scrollY + 2}px`,
  //       };
  //     }

  //     console.log('选中内容：', selection.toString().trim());

  //     setSelectedWord(selection.toString().trim());
  //     setPopupStyle(styleObj);
  //     setIsVisible(true);
  //   } else {
  //     setIsVisible(false);
  //   }
  // };

  // const clickSelectedWord = () => {
  //   // 打开智能助手
  //   PubSub.publish('systemOpt_openAIModal', {
  //     keyword: selectedWord,
  //     isCenter: true,
  //   });
  //   setIsVisible(false);
  // };

  return (
    <>
      <Helmet>
        <title>{`${title} - ${formatMessage({ id: 'app.title' })}`}</title>
      </Helmet>
      <div className={showMask && 'base-online-mask'} />
      <div
        id="layout-container"
        {...wheel.events}
        className={classNames(`view-${viewMode}`, `theme-${theme}`, {
          'base-menu-drawer-active': showLeftMenu && currentOpenLeftMenu && !showSecondMenu,
          'second-menu-drawer-active': showLeftMenu && currentOpenLeftMenu && showSecondMenu,
          'setting-drawer-active': settingDrawerActive,
          'tabs-nav-fixed': true,
        })}
        // onMouseUp={handleMouseUp}
      >
        {viewMode === 'normal' ? <Header pathname={currMenu.menuOpenMode === '5' || currMenu.menuOpenMode === '6' ? pathname + renderPath(props.location.search) : pathname} showMask={showMask} getShowMask={getShowMask} /> : null}
        {renderBody()}
        {viewMode === 'normal' ? (
          <>
            <SettingDrawer />
            <div
              className={classNames('setting-drawer-mask')}
              onClick={() => {
                dispatch({
                  type: 'setting/toggleSettingDrawer',
                  payload: false,
                });
                // 修复'react-grid-layout'组件在宽度变化时不能适配的bug
                delay(triggerResizeEvent, 100);
              }}
            />

            {/* AI悬浮球 */}
            {showAIBall ? <FloatingBall /> : null }

            {/* 悬浮搜索框 */}
            {showAISearch ? <FloatingInput /> : null }
          </>
        ) : null}

        {/* {status === mapping.LOCKED && <Locked />} */}
        {jinkuFlag && jkPostMessageInfo && (
          <Apply setJinkuFlag={setJinkuFlag} jkPostMessageInfo={jkPostMessageInfo} setJkPostMessageInfo={setJkPostMessageInfo} />
        )}
        {/* 操作指引 */}
        <HomeUserGuide />
        <Modal visible={openMenuModal.visible} onCancel={closeMenuModal} footer={null} width="800px">
          <iframe title={openMenuModal.url} src={openMenuModal.url} height="600px" width="100%" border="0" frameBorder="no" />
        </Modal>

        {/* IE浏览器提示框 */}
        {visibleIE ? <IEModal visible={visibleIE} close={hideIEModal} hideUser={hideIEModal} /> : null}

        {/* 即时通讯会话窗口 */}
        {/* <InstantMessage /> */}

        {/* 即时通讯新增经验库 */}
        {/* {librarys?.instantAddVisible && (
          librarys?.packChatData?.modalType === 'casePack' ? (
            <AddExperience />
          ) : (
            <AddServicelist />
          )
        )} */}

        {showUnreadBulletin ? <UnreadModal /> : null}

        {/* AI智能助手弹窗 */}
        <AIAssistantModal />
      </div>

      {/* {isVisible ? (
        <div className="selected_word_popup" style={popupStyle}>
          <img src={AIBadge} alt="" width={30} height={30} />
          <div className="selected_word_content" onClick={clickSelectedWord}>
            搜一搜：{selectedWord}
          </div>
        </div>
      ) : null} */}
    </>
  );
}

export default connect(({ setting, menu, login, librarys, bulletin }) => ({
  setting,
  all: menu.all,
  openMenuModal: menu.openMenuModal,
  status: login.status,
  isOSSPage: login.isOSSPage,
  librarys,
  showUnreadBulletin: bulletin.showUnreadBulletin,
}))(BasicLayout);
