import React, { useState, useEffect, useRef } from 'react';
import { Modal, Spin } from 'antd';
import PubSub from 'pubsub-js';
import { getItem, getValueByCode } from '@/utils/utils';
import styles from '../less/assistantModal.less';

const AIAssistantModal = () => {
  const { sessionId } = getItem('user');

  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [iframeUrl, setIframeUrl] = useState(null);
  const [keyword, setKeyword] = useState('');
  const [configParams, setConfigParams] = useState({
    isCenter: false,
  });

  const robotIframe = useRef(false);

  const getIframeUrl = async () => {
    setLoading(true);
    const result = await getValueByCode({
      groupCode: 'SYSTEM_VAR',
      paramCode: 'AI_APPID',
    });
    if (result) {
      const originalString = `/chat/share?appId=${result}&placement=right&keyword=${keyword || ''}&autoSend=true`;
      const encodedString = encodeURIComponent(originalString);

      // 替换原有的未编码部分
      const zntSrc = `/knowledge/single?bss3SessionId=${sessionId}&redirect=${encodedString}`;
      setIframeUrl(encodeURI(zntSrc));
      setLoading(false);
    }
  };

  useEffect(() => {
    getIframeUrl();
    PubSub.subscribe('systemOpt_openAIModal', (eventName, data) => {
      if (!robotIframe.current) {
        const { keyword: word, isCenter } = data;
        // if (word !== keyword) {
        //   setIframeUrl('');
        // }
        setKeyword(word);
        setConfigParams({ ...configParams, isCenter });
        setVisible(true);
        robotIframe.current = true;
      }
    });
    PubSub.subscribe('systemOpt_closeAIModal', () => {
      robotIframe.current = false;
      setVisible(false);
      // setKeyword('');
    });
    return () => {
      PubSub.unsubscribe('systemOpt_openAIModal');
      PubSub.unsubscribe('systemOpt_closeAIModal');
    };
  }, []);

  useEffect(() => {
    getIframeUrl();
  }, [keyword]);

  return (
    <Modal
      visible={visible}
      forceRender
      footer={null}
      onCancel={() => {
        setVisible(false);
        robotIframe.current = false;
        // setKeyword('');
      }}
      center={configParams.isCenter}
      bodyStyle={{ padding: '0', height: configParams.isCenter ? window.innerHeight * 0.6 : window.innerHeight * 0.75 }}
      width={configParams.isCenter ? window.innerWidth * 0.5 : window.innerWidth * 0.25}
      style={!configParams.isCenter ? {
        position: 'absolute',
        right: 80,
        bottom: 20,
        top: 'auto',
        padding: 0,
      } : {}}
      wrapClassName={styles.containerModal}
    >
      {
        loading ? <Spin /> : (
          <iframe
            title="智能助手"
            frameBorder="no"
            border="0"
            width="100%"
            height="100%"
            src={iframeUrl}
            style={{ borderRadius: '6px' }}
          />
        )
      }

    </Modal>
  );
};
export default AIAssistantModal;
