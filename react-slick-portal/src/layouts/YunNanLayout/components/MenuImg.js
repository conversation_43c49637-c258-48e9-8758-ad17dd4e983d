import React from 'react';
import Menu from '../img/yunnan/Menu.svg';
import Logo from '../img/yunnan/logo.svg';
import ip from '../img/yunnan/ip.svg';
import area from '../img/yunnan/area.svg';
import download from '../img/yunnan/download.svg';
import notice from '../img/yunnan/notice.svg';
import collection from '../img/yunnan/collection.svg';
import layout from '../img/yunnan/layout.svg';
import password from '../img/yunnan/password.svg';
import User from '../img/yunnan/User.svg';
import sound from '../img/yunnan/Sound.svg';
import email from '../img/yunnan/email.svg';
import time from '../img/yunnan/time.svg';
import mobile from '../img/yunnan/mobile.svg';
import expand1 from '../img/yunnan/expand1.svg';
import expanded1 from '../img/yunnan/expanded1.svg';
import expand2 from '../img/yunnan/expand2.svg';
import expanded2 from '../img/yunnan/expanded2.svg';
import add from '../img/yunnan/add.svg';
import telephone from '../img/yunnan/telephone.svg';
import card from '../img/yunnan/card.svg';
import card1 from '../img/yunnan/card1.svg';
import addressBook from '../img/yunnan/addressBook.svg';
import robot from '../img/yunnan/robot.svg';

function MenuImg({ type, width, height, ...rest }) {
  const optImg = () => {
    switch (type) {
      case 'menu':
        return Menu;
      case 'logo':
        return Logo;
      case 'area':
        return area;
      case 'ip':
        return ip;
      case 'download':
        return download;
      case 'notice':
        return notice;
      case 'password':
        return password;
      case 'collection':
        return collection;
      case 'layout':
        return layout;
      case 'user':
        return User;
      case 'sound':
        return sound;
      case 'time':
        return time;
      case 'email':
        return email;
      case 'mobile':
        return mobile;
      case 'expand1':
        return expand1;
      case 'expand2':
        return expand2;
      case 'expanded1':
        return expanded1;
      case 'expanded2':
        return expanded2;
      case 'add':
        return add;
      case 'card':
        return card;
      case 'card1':
        return card1;
      case 'telephone':
        return telephone;
      case 'addressBook':
        return addressBook;
      case 'robot':
        return robot;
      default:
        return email;
    }
  };

  return <img alt={type} src={optImg()} height={height || 17} width={width || 16} {...rest} />;
}

export default MenuImg;
