/* eslint-disable no-shadow */
/* eslint-disable no-unused-vars */
/* eslint-disable indent */
import React, { useEffect, useState } from 'react';
import { Modal, Row, Col, Icon, message, Card, Button } from 'antd';
import classNames from 'classnames';
import { connect } from 'dva';
import router from 'umi/router';
import { useBoolean } from '@umijs/hooks';
import delay from 'lodash/delay';
import find from 'lodash/find';
import ChangePassword from '@/pages/User/components/ChangePassword';
import CollectionManageDrawer from '@/layouts/BlueStyleLayout/components/CollectionManageDrawer';
import AvatarComponent from '@/components/AvatarComponent';
import DownCenter from './DownCenter';
import request from '@/utils/request';
import MenuImg from './MenuImg';
import '../less/menuDrawer.less';
import styles from '../less/downCenter.less';
import Apply from '@/pages/Treasury/Apply';

function UserInfo({ dispatch, user: { userInfo, loginIp, orgInfo, portalRoles, staffInfo, tenantList }, showUserInfo, all }) {
  const { userCode, roleId, tenantId, postRegionName, postRegionId, externalUserInfos } = userInfo;
  const { pathName = '' } = orgInfo || {};

  const { state: visible, setTrue: show, setFalse: hide } = useBoolean(false);
  const { state: collectionVisible, setTrue: showCollection, setFalse: hideCollection } = useBoolean(false);
  const { state: avatarComponentVisible, setTrue: showAvatarComponent, setFalse: hideAvatarComponent } = useBoolean(false);
  const [downVisible, setDownVisible] = useState(false);
  const [jinkuFlag, setJinkuFlag] = useState(false);
  const [jinkuResult, setJinkuResult] = useState(0);
  const [crmInfo, setCrmInfo] = useState({});

  const getCRMUser = async () => {
    const crm = find(externalUserInfos, item => item.systemNbr === 'CRM' && item.defaultFlag === '1000');
    if (crm) {
      const result = await request('portal/OrganizationOutExtController/queryOrgOutExt.do', {
        method: 'GET',
        data: {
          expId: crm.outOrgId,
        },
      });
      if (result?.resultObject) {
        crm.outOrgName = result.resultObject.outOrgName;
      }
      setCrmInfo(crm);
    }
  };

  useEffect(() => {
    getCRMUser();
  }, [JSON.stringify(externalUserInfos)]);

  function changeRole(e, roleId) {
    e.preventDefault();
    if (roleId) {
      showUserInfo(false);
      const modal = Modal.confirm({
        title: '切换角色会重新加载页面',
        onOk() {
          // 手动更新ok按钮loading状态
          // onOk返回promise对象时，只有在resolve()之后才会关闭
          modal.update({ okButtonProps: { loading: true } });
          return new Promise(resolve => {
            dispatch({
              type: 'login/changeRole',
              payload: roleId,
            }).then(() => {
              modal.update({ okButtonProps: { loading: true } });
              resolve();
            });
          });
        },
      });
    }
  }

  function changeTenant(e, clickTenantId) {
    e.preventDefault();
    if (tenantId === clickTenantId) {
      return;
    }
    if (clickTenantId) {
      showUserInfo(false);
      const modal = Modal.confirm({
        title: '切换租户会重新加载页面',
        onOk() {
          // 手动更新ok按钮loading状态
          // onOk返回promise对象时，只有在resolve()之后才会关闭
          modal.update({ okButtonProps: { loading: true } });
          return new Promise(resolve => {
            dispatch({
              type: 'login/changeTenant',
              payload: clickTenantId,
            }).then(() => {
              modal.update({ okButtonProps: { loading: true } });
              resolve();
            });
          });
        },
      });
    }
  }

  const getImageInfo = file => {
    const params = {
      userId: userInfo.userId,
      base64Strs: file.base64,
    };
    request('portal/FileStoreController/uploadUserPhoto.do', {
      data: params,
    }).then(result => {
      if (result.resultCode === '0') {
        dispatch({
          type: 'login/updateAvatar',
          payload: {
            avatar: result.resultObject,
          },
        });
      } else {
        message.error(result.resultMsg);
      }
    });
  };

  const handleCancel = () => {
    setDownVisible(false);
  };

  const handleChangeAccountBtnClick = () => {
    showUserInfo(false);
    dispatch({ type: 'fourAAccount/openForEdit' });
  };

  const handleChangeOrgBtnClick = () => {
    showUserInfo(false);
    dispatch({ type: 'orgMgmt/openForEdit' });
  };

  const hasTenant = Array.isArray(tenantList);
  // const colspan = hasTenant ? 6 : 8;
  const colspan = 6;

  return (
    <div className="slick-dropdown" style={{ paddingTop: 0 }}>
      <Row gutter={32}>
        <Col xs={colspan * 2}>
          <Card
            title={
              (
                <div>
                  <span className="bold">我的信息</span>
                  <span className="margin-left">
                    <span style={{ color: '#0085d0' }}>&#123;</span>
                    <Button style={{ padding: 0 }} type="link" size="small" onClick={handleChangeAccountBtnClick}>
                      账号切换
                    </Button>
                    <span style={{ color: '#0085d0', paddingLeft: '1px', paddingRight: '1px' }}>|</span>
                    <Button style={{ padding: 0 }} type="link" size="small" onClick={handleChangeOrgBtnClick}>
                      CRM岗位切换
                    </Button>
                    <span style={{ color: '#0085d0' }}>&#125;</span>
                  </span>
                </div>
              )
            }
            bordered={false}
            className={styles.card}
          >
            <Row gutter={[16, 8]}>
              <Col span={12}>
                <div className={styles.item} title={userCode}>
                  <Icon type="credit-card" style={{ marginRight: 8, color: 'rgba(0,0,0,0.8)' }} />
                  账号：<span className={styles.gray}>{userCode}</span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.item} title={userInfo?.mobilePhone || '无'}>
                  <MenuImg type="mobile" style={{ marginRight: 8, color: 'rgba(0,0,0,0.8)' }} />
                  手机号码：<span className={styles.gray}>{userInfo?.mobilePhone || '无'}</span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.item} title={loginIp}>
                  <MenuImg type="ip" style={{ marginRight: 8, color: 'rgba(0,0,0,0.8)' }} />
                  IP地址：<span className={styles.gray}>{loginIp}</span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.item} title={staffInfo.email}>
                  <MenuImg type="email" style={{ marginRight: 8, color: 'rgba(0,0,0,0.8)', verticalAlign: 'text-bottom' }} />
                  邮箱：
                  <span className={styles.gray}>{staffInfo?.email || '无'}</span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.item} title={postRegionName}>
                  <MenuImg type="area" style={{ marginRight: 8, color: 'rgba(0,0,0,0.8)', verticalAlign: 'text-bottom' }} />
                  区域：
                  <span className={styles.gray}>
                    {postRegionName}[{postRegionId}]
                  </span>
                </div>
              </Col>
              {/* <Col span={12}>
                <div className={styles.item} title={pathName}>
                  <MenuImg type="card1" style={{ marginRight: 8, color: 'rgba(0,0,0,0.8)' }} />
                  归属组织：<span className={styles.gray}>{pathName}</span>
                </div>
              </Col> */}
              <Col span={12}>
                <div className={styles.item} title={crmInfo.externalUserCode || ''}>
                  <MenuImg type="card1" style={{ marginRight: 8, color: 'rgba(0,0,0,0.8)' }} />
                  CRM工号：<span className={styles.gray}>{crmInfo.externalUserCode || ''}</span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.item} title={crmInfo.outOrgId || ''}>
                  <MenuImg type="card1" style={{ marginRight: 8, color: 'rgba(0,0,0,0.8)' }} />
                  CRM组织编码：<span className={styles.gray}>{crmInfo.outOrgId || ''}</span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.item} title={crmInfo.outOrgName || ''}>
                  <MenuImg type="card1" style={{ marginRight: 8, color: 'rgba(0,0,0,0.8)' }} />
                  CRM组织名称：<span className={styles.gray}>{crmInfo.outOrgName || ''}</span>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
        {hasTenant && (
          <Col xs={colspan}>
            <Card title={<span className="bold">租户切换</span>} bordered={false} className={styles.card}>
              <Row gutter={16}>
                {tenantList.length > 0
                  ? tenantList.map(item => (
                    <Col span={24} key={item.tenantId}>
                      <div
                        className={classNames(styles.item, styles.bordered, 'text-center', 'pointer', {
                            [styles.actived]: tenantId === item.tenantId,
                          })}
                        onClick={e => changeTenant(e, item.tenantId)}
                      >
                        {item.tenantName}
                      </div>
                    </Col>
                    ))
                  : null}
              </Row>
            </Card>
          </Col>
        )}
        <Col xs={colspan}>
          <Card title={<span className="bold">角色切换</span>} bordered={false} className={styles.card}>
            <Row gutter={[16, 8]}>
              {portalRoles.length > 0
                ? portalRoles.map(item => (
                  <Col span={24} key={item.sysRoleId}>
                    <div
                      className={classNames(styles.item, styles.bordered, 'text-center', 'pointer', {
                          [styles.actived]: roleId === item.sysRoleId,
                        })}
                      onClick={e => changeRole(e, item.sysRoleId)}
                    >
                      {item.sysRoleName}
                    </div>
                  </Col>
                  ))
                : null}
            </Row>
          </Card>
        </Col>
        <Col xs={colspan}>
          <Card title={<span className="bold">我的菜单</span>} bordered={false} className={styles.card}>
            <Row gutter={[16, 8]}>
              <Col span={12}>
                <div
                  className={classNames(styles.item, 'pointer')}
                  onClick={() => {
                    showUserInfo(false);
                    delay(() => {
                      showCollection();
                    }, 300);
                  }}
                >
                  <MenuImg type="collection" style={{ marginRight: 8 }} />
                  菜单收藏
                </div>
              </Col>
              {find(all, { urlAddr: '/customize/user' }) === undefined ? null : (
                <Col span={12}>
                  <div
                    className={classNames(styles.item, 'pointer')}
                    onClick={() => {
                      showUserInfo(false);
                      delay(() => {
                        router.replace('/customize/user');
                      }, 300);
                    }}
                  >
                    <MenuImg type="layout" style={{ marginRight: 8 }} />
                    布局设置
                  </div>
                </Col>
              )}
              {find(all, { urlAddr: '/bulletin/manage' }) === undefined ? null : (
                <Col span={12}>
                  <div
                    className={classNames(styles.item, 'pointer')}
                    onClick={() => {
                      showUserInfo(false);
                      delay(() => {
                        router.replace('/bulletin/manage');
                      }, 300);
                    }}
                  >
                    <MenuImg type="notice" style={{ marginRight: 8 }} />
                    公告发布
                  </div>
                </Col>
              )}
              {/* <Col span={12}>
                <div
                  className={classNames(styles.item, 'pointer')}
                  onClick={() => {
                    showUserInfo(false);
                    delay(() => {
                      show();
                    }, 300);
                  }}
                >
                  <MenuImg type="password" style={{ marginRight: 8 }} />
                  密码修改
                </div>
              </Col> */}
              {/* <li>
                        <span className="entrance-tit">密码重置</span>
                      </li> */}
              {/* <Col span={8}>
                <div
                  className={classNames(styles.item, 'pointer')}
                  onClick={() => {
                    showUserInfo(false);
                    delay(() => {
                      showAvatarComponent();
                    }, 300);
                  }}
                >
                  <MenuImg type="password" style={{ marginRight: 8 }} /> 头像修改
                </div>
              </Col> */}
              <Col span={12}>
                <div
                  className={classNames(styles.item, 'pointer')}
                  onClick={() => {
                    showUserInfo(false);
                    setDownVisible(true);
                    // delay(() => {
                    //   router.replace('/download');
                    // }, 300);
                  }}
                >
                  <MenuImg type="download" style={{ marginRight: 8 }} />
                  下载中心
                </div>
              </Col>
              <Col span={12}>
                <div
                  className={classNames(styles.item, 'pointer')}
                  onClick={() => {
                    showUserInfo(false);
                    router.replace('/');
                    window.scrollTo(0, 0);
                    dispatch({
                      type: 'setting/updateSettings',
                      payload: { guideVisible: true },
                    });
                  }}
                >
                  <Icon type="question-circle" style={{ marginRight: 8, fontSize: 15, color: 'rgba(0,133,208,1)' }} />
                  操作指引
                </div>
              </Col>

              {/* <Col span={12}>
                <div
                  className={classNames(styles.item, 'pointer')}
                  onClick={() => {
                    showUserInfo(false);
                    setJinkuFlag(true);
                  }}
                >
                  <Icon type="question-circle" style={{ marginRight: 8, fontSize: 15, color: 'rgba(0,133,208,1)' }} />
                  金库入口
                </div>
              </Col> */}
            </Row>
          </Card>
        </Col>
      </Row>
      {jinkuFlag && <Apply setJinkuFlag={setJinkuFlag} setJinkuResult={setJinkuResult} />}

      <ChangePassword visible={visible} close={hide} />
      {collectionVisible && <CollectionManageDrawer visible={collectionVisible} close={hideCollection} />}
      <AvatarComponent
        src={`portal/FileStoreController/dfsReadImage.do?docLink=${userInfo.userPhoto}`}
        getImageInfo={getImageInfo}
        visible={avatarComponentVisible}
        close={hideAvatarComponent}
      />
      <Modal title="下载中心" visible={downVisible} footer={null} onCancel={handleCancel} destroyOnClose width={800} className={styles.downCenter}>
        <DownCenter />
      </Modal>
    </div>
  );
}

export default connect(({ login, menu }) => ({
  user: login.user,
  all: menu.all,
}))(UserInfo);
