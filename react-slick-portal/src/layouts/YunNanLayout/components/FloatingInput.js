import React, { useState, useRef, useEffect } from 'react';
import { Input, Icon } from 'antd';
import PubSub from 'pubsub-js';
import AIBadge from '@/layouts/YunNanLayout/img/AIBadge.gif';
import sendBtnLarge from '../img/sendBtnLarge.png';
// import minSearch from '../img/minSearch.png';
import styles from '../less/floatingInput.less';
import AIIcon from '../img/AI_icon.gif';

const FloatingInput = () => {
  const [inputValue, setInputValue] = useState('');
  const [styleObj, setStyleObj] = useState({ opacity: 0.6 });
  const [isMin, setIsMin] = useState(false);

  const handleSearch = value => {
    if (value) {
      // 打开智能助手
      PubSub.publish('systemOpt_openAIModal', {
        keyword: value,
        isCenter: true,
      });
      setInputValue('');
    }
  };

  const handleKeyDown = e => {
    if (e.key === 'Enter' && e.ctrlKey) {
      // 按下 Ctrl + Enter 时，插入换行符
      setInputValue(prevValue => `${prevValue}\n`);
      e.preventDefault(); // 阻止默认行为
    } else if (e.key === 'Enter' && inputValue) {
      handleSearch(inputValue);
    }
  };

  const [position, setPosition] = useState({ right: 0, bottom: 90 });
  const robotRef = useRef(null);

  const handleMouseDown = event => {
    event.preventDefault();
    const eventX = event.clientX;
    const eventY = event.clientY;

    let isClick = true;
    const handleMouseMove = event1 => {
      isClick = false;
      const newPosition = {
        right: eventX - event1.clientX + position.right,
        bottom: eventY - event1.clientY + position.bottom,
      };
      setPosition(newPosition);
    };

    const handleMouseUp = () => {
      if (isClick) {
        setIsMin(false);
        isClick = false;
      }
      setPosition(pre => {
        let { bottom } = pre;
        if (pre.bottom > window.innerHeight - 100) {
          bottom = window.innerHeight - 100;
        }
        if (pre.bottom < 50) {
          bottom = 50;
        }
        return {
          right: 0,
          bottom,
        };
      });
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  useEffect(() => {
    const image = robotRef.current;
    if (image) {
      image.style.right = `${position.right}px`;
      image.style.bottom = `${position.bottom}px`;
    }
  }, [position]);


  return (
    <>
      <div>
        {
          isMin ? (
            <div>
              <div
                ref={robotRef}
                className={styles.minImg}
                onMouseDown={handleMouseDown}
                // style={{ right: `${position.right}px`, bottom: `${position.bottom}px` }}
              >
                <img
                  src={AIIcon}
                  alt=""
                  width="80"
                  height="90"
                />
              </div>
            </div>
          ) : (
            <div className={styles.floatingInput}>
              <div className={styles.inputBox} style={{ ...styleObj }}>
                <img src={AIBadge} alt="" width={54} height={36} />
                <Input.TextArea
                  className={styles.input}
                  allowClear
                  placeholder="请输入（按Entet键发送，按Ctrl+Enter换行）"
                  // enterButton="发送"
                  value={inputValue}
                  // onSearch={handleSearch}
                  onChange={({ target: { value } }) => {
                    setInputValue(value);
                  }}
                  onKeyDown={handleKeyDown}
                  style={{
                    width: 500,
                    margin: '10px 0px',
                  }}
                  autoSize={{ minRows: 1, maxRows: 5 }}
                  onFocus={() => setStyleObj({ opacity: 1, boxShadow: 'rgba(0, 0, 0, 0.2) 0px 2px 8px' })}
                  onBlur={() => setStyleObj({ opacity: 0.5, boxShadow: 'inset 30px 0px 34px 0px rgba(35, 160, 222, 0.2)' })}
                />
                <img src={sendBtnLarge} style={{ cursor: 'pointer', margin: '8px 9px' }} width={40} height={32} onClick={() => handleSearch(inputValue)} alt="" />
              </div>
              <Icon type="close-circle" theme="filled" className={styles.closeIcon} onClick={() => setIsMin(true)} />
            </div>
          )
        }
      </div>
    </>
  );
};
export default FloatingInput;
