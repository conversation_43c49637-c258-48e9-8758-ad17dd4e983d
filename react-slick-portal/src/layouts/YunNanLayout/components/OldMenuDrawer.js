import React from 'react';
import { Menu, Tabs, Empty } from 'antd';
import classNames from 'classnames';
import { connect } from 'dva';
import Link from 'umi/link';
import ScrollBar from '@/components/ScrollBar';
import { getFinalUrl } from '@/utils/utils';
import '../less/menuDrawer.less';

const { TabPane } = Tabs;

function MenuDrawer({ recent, collection, size }) {
  return (
    <div id="menu-drawer" className={classNames('menu-drawer')}>
      <Tabs
        renderTabBar={(props, DefaultTabBar) => <DefaultTabBar {...props} className="reset" />}
      >
        <TabPane tab="最近访问" key="1">
          {recent.length === 0 ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="没有您的访问记录" />
          ) : (
            <ScrollBar autoHide autoHeight autoHeightMax={size.height}>
              <Menu mode="inline">
                {/**
                 * recent中urlAddr值为[iframe]开头的表示第三方集成的子系统
                 * 取menuCode值
                 * 第三方集成的页面统一指向@/pages/iframe。会根据传进去的menuCode从all中遍历出对应的菜单地址
                 */}
                {recent.map(menu => (
                  <Menu.Item key={menu.menuCode}>
                    <Link
                      className="text-ellipsis"
                      title={menu.menuName}
                      to={getFinalUrl(menu)}
                    >
                      {menu.menuName}
                    </Link>
                  </Menu.Item>
                ))}
              </Menu>
            </ScrollBar>
          )}
        </TabPane>
        <TabPane tab="我的收藏" key="2">
          {collection.length === 0 ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="没有您的收藏" />
          ) : (
            <ScrollBar autoHide autoHeight autoHeightMax={size.height}>
              <Menu mode="inline">
                {/**
                 * collection中urlAddr值为[iframe]开头的表示第三方集成的子系统
                 * 取menuCode值，menuCode是全局唯一的
                 * 第三方集成的页面统一指向@/pages/iframe。会根据传进去的id从all中遍历出对应的菜单地址
                 */}
                {collection.map(menu => (
                  <Menu.Item key={menu.menuCode}>
                    <Link
                      className="text-ellipsis"
                      title={menu.menuName}
                      to={getFinalUrl(menu)}
                    >
                      {menu.menuName}
                    </Link>
                  </Menu.Item>
                ))}
              </Menu>
            </ScrollBar>
          )}
        </TabPane>
      </Tabs>
    </div>
  );
}

export default connect(({ menu, setting }) => ({
  recent: menu.recent,
  collection: menu.collection,
  size: setting.size,
}))(MenuDrawer);
