/* eslint-disable no-shadow */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/jsx-indent */
/* eslint-disable camelcase */
import React, { useState, useEffect } from 'react';
import { Button, Icon, Empty, Tabs } from 'antd';
import memoizeOne from 'memoize-one';
import isEqual from 'lodash/isEqual';
import classNames from 'classnames';
import { connect } from 'dva';
// import Link from 'umi/link';
import Link from '@/components/AuthLink';
import { getFinalUrl } from '@/utils/utils';
import { getFirstLevelMenu, getSecondLevelMenuInfo, getThirdLevelMenuInfo } from './utils';
import styles from '../less/header2.less';

const { TabPane } = Tabs;

const _getFirstLevelMenu = memoizeOne(getFirstLevelMenu, isEqual);
const _getSecondLevelMenuInfo = memoizeOne(getSecondLevelMenuInfo, isEqual);
const _getThirdLevelMenuInfo = memoizeOne(getThirdLevelMenuInfo, isEqual);

// 获取一级菜单下所有的三级菜单
const getAllThirdLevelMenus = (allMenus, firstLevelMenuId) => {
  const secondLevelMenus = _getSecondLevelMenuInfo(allMenus, firstLevelMenuId);
  const allThirdMenus = [];
  secondLevelMenus.forEach(menu2 => {
    const thirdMenus = _getThirdLevelMenuInfo(allMenus, menu2.menuId);
    if (thirdMenus.length > 0) {
      allThirdMenus.push({
        secondLevelMenu: menu2,
        thirdLevelMenus: thirdMenus,
      });
    }
  });
  return allThirdMenus;
};

const initial = {
  menuLel1: [],
  menuLel2: [],
  menuLel3: [],
  allThirdMenus: [],
  activeMenuId1: '',
  activeMenuId2: '',
  activeMenuId3: '',
};

function reducer(state, action) {
  const { type, payload } = action;
  if (type === 'init') {
    return {
      ...state,
      ...payload,
    };
  }

  if (type === 'setMenuData') {
    return {
      ...state,
      ...payload,
    };
  }

  throw Error();
}

function AllMenu({ dispatch: dispatchRefresh, menu: { all, appSysCode, taskSysCode }, refreshLoading, hideAllMenu }) {
  const [state, dispatch] = React.useReducer(reducer, initial);
  // 顶部菜单id
  const [menuLelOneId, setMenuLelOneId] = useState();
  // 弹出框打开
  // const [visible, setVisible] = useState(false);
  // const [modalUrl, setModalUrl] = useState('');

  function getMenuLel3(e, menuId, menuName) {
    e.preventDefault();
    const activeMenuId2 = parseInt(menuId, 10);
    const menuLel3 = _getThirdLevelMenuInfo(all, activeMenuId2);
    const allThirdMenus = getAllThirdLevelMenus(all, state.activeMenuId1);

    dispatch({
      type: 'setMenuData',
      payload: {
        activeMenuId2,
        menuLel3,
        allThirdMenus,
        title: menuName,
      },
    });
  }

  // 菜单打开方式
  async function handleOpenMenu(menu) {
    // const menuUrl = openMenu(menu, all, dispatchRefresh);

    if (menu.menuOpenMode === '2') {
      // setVisible(true);
      // setModalUrl(menuUrl);
      hideAllMenu(false);
    }
  }

  // 负责渲染级联菜单
  function renderMenu(menu, hideAllMenu) {
    // 1100 表示叶子节点
    if (parseInt(menu.menuType, 10) === 1100) {
      if (menu.menuOpenMode !== '1') {
        return (
          <a onClick={() => handleOpenMenu(menu)} className={classNames('text-ellipsis')} key={menu.menuId}>
            {menu.menuName}
          </a>
        );
      }

      return (
        <Link to={getFinalUrl(menu)} onClick={() => hideAllMenu(false)} title={menu.menuName} className="text-ellipsis">
          {menu.menuName}
        </Link>
      );
    }
    return (
      <a title={menu.menuName} href="#" className="text-ellipsis">
        {menu.menuName}
        <Icon type="right" />
      </a>
    );
  }

  function renderMenu3(menu, hideAllMenu) {
    // 1100 表示叶子节点
    if (parseInt(menu.menuType, 10) === 1100) {
      if (menu.menuOpenMode !== '1') {
        return (
          <a onClick={() => handleOpenMenu(menu)} className={classNames('text-ellipsis')} key={menu.menuId}>
            {menu.menuName}
          </a>
        );
      }

      return (
        <Link to={getFinalUrl(menu)} onClick={() => hideAllMenu(false)} title={menu.menuName} className="text-ellipsis">
          {menu.menuName}
        </Link>
      );
    }
    return (
      <a title={menu.menuName} href="#" className="text-ellipsis">
        {menu.menuName}
        <Icon type="right" />
      </a>
    );
  }

  useEffect(() => {
    const menuLel1 = _getFirstLevelMenu(all, appSysCode, taskSysCode);
    const activeMenuId1 = menuLelOneId || menuLel1?.[0]?.menuId;
    const menuLel2 = _getSecondLevelMenuInfo(all, activeMenuId1);
    const activeMenuId2 = menuLel2?.[0]?.menuId;
    const menuLel3 = _getThirdLevelMenuInfo(all, activeMenuId2);
    const allThirdMenus = getAllThirdLevelMenus(all, activeMenuId1);

    dispatch({
      type: 'init',
      payload: {
        menuLel1,
        activeMenuId1,
        activeMenuId2,
        menuLel2,
        menuLel3,
        allThirdMenus,
        title: menuLel2?.[0]?.menuName,
      },
    });
  }, [dispatch, all, appSysCode, taskSysCode]);

  const changeTab = menuId => {
    const activeMenuId1 = parseInt(menuId, 10);

    setMenuLelOneId(activeMenuId1);

    const menuLel2 = _getSecondLevelMenuInfo(all, activeMenuId1);
    const activeMenuId2 = menuLel2?.[0]?.menuId;
    const menuLel3 = _getThirdLevelMenuInfo(all, activeMenuId2);
    const allThirdMenus = getAllThirdLevelMenus(all, activeMenuId1);

    dispatch({
      type: 'setMenuData',
      payload: {
        activeMenuId1,
        activeMenuId2,
        menuLel2,
        menuLel3,
        allThirdMenus,
        title: menuLel2?.[0]?.menuName,
      },
    });
  };

  return (
    <div className="topbar-dropdown-box">
      {state.menuLel1.length > 0 ? (
        <>
          <Tabs animated={false} onChange={changeTab}>
            {state.menuLel1.map(menu => (
              <TabPane tab={menu.menuName} key={menu.menuId}>
                <div className={styles.row}>
                  {state.menuLel2?.length > 0 ? (
                    <div className={styles.col}>
                      <div className={styles.body}>
                        <ul className={styles.list}>
                          {state.menuLel2.map(menu2 => (
                            <li
                              id={menu2.menuId}
                              className={classNames({ [styles.active]: menu2.menuId === state.activeMenuId2 })}
                              onClick={e => getMenuLel3(e, menu2.menuId, menu2.menuName)}
                              key={menu2.menuId}
                            >
                              {renderMenu(menu2, hideAllMenu)}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ) : null}

                  <div className={classNames(styles.panel)}>
                    {state.menuLel3?.length > 0 && (
                      <>
                        <div className={styles.title}>{state.title}</div>
                        <dl className={styles.list}>
                          {state.menuLel3.map(menu3 => (
                            <li
                              className={classNames({ [styles.active]: menu3.menuId === state.activeMenuId3 })}
                              key={menu3.menuId}
                            >
                              {renderMenu3(menu3, hideAllMenu)}
                            </li>
                          ))}
                        </dl>
                      </>
                    )}

                    {state.allThirdMenus?.map(({ secondLevelMenu, thirdLevelMenus }) => (
                      secondLevelMenu.menuId !== state.activeMenuId2 && (
                        <React.Fragment key={secondLevelMenu.menuId}>
                          <div className={styles.title}>
                            {secondLevelMenu.menuName}
                          </div>
                          <dl className={styles.list}>
                            {thirdLevelMenus.map(menu3 => (
                              <li key={menu3.menuId}>
                                {renderMenu3(menu3, hideAllMenu)}
                              </li>
                            ))}
                          </dl>
                        </React.Fragment>
                      )
                    ))}
                  </div>
                </div>
              </TabPane>
            ))}
          </Tabs>
          <div className={styles.refreshButton}>
            <Button
              type="primary"
              loading={refreshLoading}
              onClick={() => {
                dispatchRefresh({
                  type: 'menu/refreshMenu',
                });
              }}
            >
              刷新
            </Button>
          </div>
        </>
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}

      {/* <Modal visible={visible} onCancel={() => setVisible(false)} footer={null} width="800px">
        <iframe title="modal" src={modalUrl} height="600px" width="752px" />
      </Modal> */}
    </div>
  );
}

export default connect(({ menu, loading }) => ({
  menu,
  refreshLoading: loading.effects['menu/refreshMenu'],
}))(AllMenu);
