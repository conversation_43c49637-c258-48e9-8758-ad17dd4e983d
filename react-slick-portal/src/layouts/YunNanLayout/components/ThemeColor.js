import React from 'react';
import { Icon, Tooltip } from 'antd';
// import { formatMessage } from 'umi/locale';
import styles from '../less/themeColor.less';
import { THEME_STYLE } from '@/enums';

const Tag = ({ color, check, ...rest }) => (
  <div
    {...rest}
    style={{
      backgroundColor: color,
    }}
  >
    {check ? <Icon type="check" /> : ''}
  </div>
);

const ThemeColor = ({ colors, title, value, onChange }) => {
  let colorList = colors;
  if (!colors) {
    colorList = [
      {
        key: THEME_STYLE.DEFAULT,
        color: 'rgba(0,133,208,1)',
        value: THEME_STYLE.DEFAULT,
        tips: '默认',
      },
      // {
      //   key: THEME_STYLE.LIGHT,
      //   // disabled: true,
      //   color: '#3366EE',
      //   value: THEME_STYLE.LIGHT,
      //   tips: '深蓝色',
      // },
      // {
      //   key: 'orange',
      //   disabled: true,
      //   color: '#fa9022',
      // },
    ];
  }
  return (
    <div className={styles.themeColor}>
      <h3 className={styles.title}>{title}</h3>
      <div className={styles.content}>
        {colorList.map(({ key, color, disabled, tips }) => (
          <Tooltip key={color} title={tips}>
            <Tag
              className={styles.colorBlock}
              key={key}
              color={color}
              disabled={disabled}
              check={value === key}
              onClick={() => onChange && onChange(key)}
            />
          </Tooltip>
        ))}
      </div>
    </div>
  );
};

export default ThemeColor;
