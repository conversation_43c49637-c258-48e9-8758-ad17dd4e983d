@import './variables.less';

:global {
  .view-normal {
    padding-top: @header-height;
  }
  .view-inner {
    #layout-body {
      padding-top: 0 !important;
      padding-left: 0 !important;
    }
  }
  #root,
  #layout-container,
  #layout-body {
    height: 100%;
  }

  #layout-container,
  #layout-body,
  #layout-header,
  .layout-tabs-nav,
  .setting-drawer,
  .menu-drawer-basic {
    transition: all 0.3s ease;
  }

  #layout-container {
    position: relative;
    left: 0;
    background-color: #E9EEF1;

    .layout-body-main {
      background-color: #E9EEF1;
    }
  }
  // OPEN SETTING DRAWER
  .setting-drawer {
    position: fixed;
    top: 0;
    right: -@setting-drawer-width;
    bottom: 0;
    z-index: 999;
    width: @setting-drawer-width;
    // transform: translate(@setting-drawer-width, 0);
  }

  .setting-drawer-active {
    left: -@setting-drawer-width !important;
    #layout-body {
      position: relative;
    }
    #layout-header,
    .layout-tabs-nav,
    .menu-drawer-basic {
      right: @setting-drawer-width !important;
      left: -@setting-drawer-width !important;
    }
    .setting-drawer {
      right: 0;
    }
    .setting-drawer-mask {
      right: @setting-drawer-width !important;
      left: 0;
      transition: all 0.000001s ease 0.3s;
    }
  }

  .setting-drawer-mask {
    position: fixed;
    top: 0;
    right: -100%;
    bottom: 0;
    left: 100%;
    z-index: 998;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    filter: alpha(opacity=50);
  }

  // OPEN MENU DRAWER
  .menu-drawer-basic {
    position: fixed;
    top: @header-height;
    bottom: 0;
    left: 0;
    width: 0;
  }

  @base-menu-drawer-width: 50px;
  @second-menu-drawer-width: 192px;


  .base-menu-drawer-active {
    #layout-body {
      padding-left: @base-menu-drawer-width;
    }
    .menu-drawer-basic {
      width: @base-menu-drawer-width;
    }
    &.tabs-nav-fixed {
      .layout-tabs-nav {
        left: @base-menu-drawer-width;
      }
    }
  }

  .second-menu-drawer-active {
    #layout-body {
      padding-left: @second-menu-drawer-width + @base-menu-drawer-width;
    }
    .menu-drawer-basic {
      width: @second-menu-drawer-width + @base-menu-drawer-width;
    }
    &.tabs-nav-fixed {
      .layout-tabs-nav {
        left: @second-menu-drawer-width + @base-menu-drawer-width;
      }
    }
  }

  // LAYOUT TABS NAV
  .layout-tabs-nav {
    height: 34px; // 务必设置一个固定高度，否则global.size.height计算失效
    margin-bottom: 0;
    padding-left: @slick-space-base;
    background-color: #fff;
    // box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.04);
    .ant-tabs-tab {
      margin-right: 5px;
    }
    .anticon {
      margin-right: 0 !important;
      margin-left: 8px;
      font-size: 12px;
    }
  }

  .tabs-nav-fixed {
    .layout-tabs-nav {
      position: fixed;
      top: @header-height !important;
      right: 0;
      left: 0;
      z-index: 100;
    }
    #layout-body {
      padding-top: 32px;
    }
  }
}
