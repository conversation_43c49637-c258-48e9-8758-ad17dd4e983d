@import '~antd/lib/style/themes/default.less';
@import './variables.less';

.navMenu {
  height: @header-height;
  overflow: hidden;

  :global {
    .ant-popover-content {
      .ant-popover-inner-content {
        // padding-right: 0;
        overflow-x: hidden;
        overflow-y: auto;
      }
    }
  }
}

.group {
  // margin-top: 16px;
  // margin-bottom: 16px;
  // background: rgba(250, 250, 250, 1);
  // border-radius: 2px;
}

.split {
  margin: 12px 0 8px;
}

.title {
  margin-bottom: 8px;
  color: @text-color;
  font-weight: bold;
}
.item {
  display: block;
  padding: 6px 16px 6px 0;
  color: @menu-color;
  line-height: 20px;
  // &:hover {
  //   background-color: @primary-1;
  // }
}

.panel {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  max-height: 400px;
  padding: 4px 0;
  overscroll-behavior: 'contain';

  .col {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    width: 152px;

    .menuName {
      margin-top: 8px;
      padding: 0 8px;
      color: @blue-6;
      font-weight: 400;
      font-size: 13px;
      line-height: 20px;
    }

    ul {
      margin: 0;
      padding: 0;
      list-style: none;
      li {
        &.ng-item {
          a {
            display: block;
            padding: 6px 8px;
            color: @menu-color;
            line-height: 20px;
          }
        }
      }
      &::after {
        display: block;
        clear: both;
        height: 0;
        font-size: 0;
        visibility: hidden;
        content: ' ';
      }
    }
  }
}
