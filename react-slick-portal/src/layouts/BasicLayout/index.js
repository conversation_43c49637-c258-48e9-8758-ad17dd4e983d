import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Icon, Tabs, message, Modal } from 'antd';
import { connect } from 'dva';
import router from 'umi/router';
import { Helmet } from 'react-helmet';
import classNames from 'classnames';
import { formatMessage } from 'umi-plugin-react/locale';
import find from 'lodash/find';
import findIndex from 'lodash/findIndex';
import isObject from 'lodash/isObject';
import delay from 'lodash/delay';
import { useDebounceFn } from '@umijs/hooks';
import Guide from 'byte-guide';
import Header from './components/Header';
import MenuDrawer from './components/MenuDrawer';
import SettingDrawer from '../BlueStyleLayout/components/SettingDrawer';
import Exception from '@/components/Exception';
import Authorized from '@/utils/Authorized';
import traceLogAction from '@/services/traceLogAction';
import request from '@/utils/request';
import { IEVersion, mouseWheel, getViewPort, isJSON, openMenu } from '@/utils/utils';
import Dashboard from '@/pages/Dashboard';
import './less/index.less';
import HomeUserGuide from './components/HomeUserGuide';
import { getDictData } from '@/services/common';


const wheel = mouseWheel();
const { TabPane } = Tabs;

// 把对象的key序列化成小写
function serialize(data) {
  const result = {};
  Object.keys(data).forEach(val => {
    result[`${val.trim().toLocaleLowerCase()}`] = data[`${val}`];
  });
  return result;
}

function triggerResizeEvent() {
  const event = document.createEvent('HTMLEvents');
  event.initEvent('resize', true, false);
  window.dispatchEvent(event);
}

function hasScrollbar() {
  return (
    document.body.scrollHeight - 45 > (window.innerHeight || document.documentElement.clientHeight)
  );
}

// eslint-disable-next-line no-unused-vars
function getScrollTop() {
  return document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
}

function BasicLayout(props) {
  const {
    dispatch,
    currMenu,
    location: { pathname },
    all,
    setting: { theme, settingDrawerActive, showSecondMenu, viewMode, size, showLeftMenu, gutter, currentOpenLeftMenu },
    children,
    openMenuModal,
  } = props;

  const home = {
    key: '/',
    url: '/',
    closable: false,
    title: formatMessage({ id: 'menu.home' }),
    content: <Dashboard />,
  };

  // eslint-disable-next-line no-unused-vars
  const [activeKey, setActiveKey] = useState('');
  const [title, setTitle] = useState('');
  const [tabPanes, setTabPanes] = useState([home]);
  const [wheelDirection, setWheelDirection] = useState(null);
  const [microApp, setMicroApp] = useState();

  // 防抖，计算当前画布大小
  const { run: setSize } = useDebounceFn(() => {
    const { width, height } = getViewPort(false);
    const $header = document.getElementById('layout-header');
    const $tabsNav = document.querySelector('.layout-tabs-nav');
    // const $pane = document.querySelector('.ant-tabs-tabpane-active');
    const $menuDrawer = document.getElementById('menu-drawer');
    const contentHeight =
      height -
      ($header === null ? 0 : $header.offsetHeight) -
      ($tabsNav === null ? 0 : $tabsNav.offsetHeight) -
      // ($pane === null
      //   ? 0
      //   : parseInt(getComputedStyle($pane).paddingTop, 10) +
      //     parseInt(getComputedStyle($pane).paddingBottom, 10)) -
      (viewMode === 'inner' ? 0 : parseInt(gutter, 10) * 2);
    const contentWidth = width - ($menuDrawer === null ? 0 : $menuDrawer.offsetWidth);
    dispatch({
      type: 'setting/saveSize',
      payload: { width: contentWidth, height: contentHeight, fullHeight: height, fullWidth: width },
    });
  }, 50);

  /**
   * 改变tab时的回调,实时更新url
   */
  /* eslint-disable */
  function onTabChange(activeKey, tabPanes) {
    for (let item of tabPanes) {
      if (item.key === `${activeKey}`) {
        if (item.openUrl) {
          request(item.openUrl, {
            method: 'GET',
            showError: false,
          });
        }
      }
    }

    /**
     * IMPROVE: 比如下面场景：
     * 首先，用Portal.open('/iframe/xxx?abc=123')打开一个iframe页面('?abc=123'部分表示往iframe页面传参)
     * 接着，在未关闭该iframe页面时，又Portal.open('/iframe/xxx?abc=456')  参数部分变了
     * 结果，iframe中的props.location.search未更新，导致传参错误
     * 处理方式：当同一个页面，search变化时，需要更新tabPanes中对应项的url以及children，更新children才能保证他上面挂载的props.location.search被更新
     */
    router.push(find(tabPanes, { key: activeKey }).url);
  }
  /* eslint-enable */

  const getMicroName = str => str.replace('/', '');

  // 摧毁keep-alive状态的页签
  const destoryMicroKeepAliveByKey = targetKey => {
    if (microApp && targetKey.indexOf('Micro/') !== -1) {
      const name = getMicroName(targetKey);
      microApp.unmountApp(name, { clearAliveState: true });
    }
  };

  // 摧毁keep-alive状态的页签
  const destoryAllMicroKeepAlive = () => {
    if (microApp) {
      microApp.unmountAllApps({ clearAliveState: true });
    }
  };

  const closeMenuModal = () => {
    dispatch({
      type: 'menu/saveInitData',
      payload: {
        openMenuModal: {
          visible: false,
          url: '',
        },
      },
    });
  };

  useEffect(() => {
    // 只有在非ie浏览器时，才引入microApp
    if (IEVersion() === -1) {
      import('@micro-zoe/micro-app').then(_microApp => {
        setMicroApp(_microApp);
      });
    }
  }, []);

  /**
   * 关闭tab时的回调
   */
  function onTabRemove(targetKey, currentTabKey, tabPanes) {
    // 如果关闭的是当前tab, 要激活哪个tab?
    // 首先尝试激活左边的, 再尝试激活右边的
    let nextTabKey = currentTabKey;
    if (nextTabKey === targetKey) {
      let currentTabIndex = -1;
      tabPanes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          currentTabIndex = i;
        }
      });

      // 如果当前tab左边还有tab, 就激活左边的
      if (currentTabIndex > 0) {
        nextTabKey = tabPanes[currentTabIndex - 1].key;
      }
      // 否则就激活右边的tab
      else if (currentTabIndex === 0 && tabPanes.length > 1) {
        nextTabKey = tabPanes[currentTabIndex + 1].key;
      }
    }

    // 关闭时候实时更新url。只有在当前页面与目标新页面路径不同时才更新。
    const nextTabUrl = find(tabPanes, { key: nextTabKey }).url;
    if (pathname !== nextTabUrl) {
      router.replace(nextTabUrl);
    }

    // 过滤panes
    const newTabPanes = tabPanes.filter(pane => pane.key !== targetKey);
    setTabPanes(newTabPanes);

    destoryMicroKeepAliveByKey(targetKey);
  }

  /**
   *
   * @param {string} pathname  当前页面路径
   * @param {object[]} tabPanes
   * @param {object} currMenu  当前菜单信息，必须包含3个字段：{menuId,urlAddr,menuName,menuCode}
   * @param {string} viewMode 视图模式,可选值 'normal | inner'
   */
  function renderBody(pathname, tabPanes, currMenu, viewMode, size) {
    const activeKey = pathname;
    return (
      <Authorized
        authority={currMenu === undefined || currMenu === null ? undefined : currMenu.authority}
        noMatch={<Exception type="403" />}
      >
        <div id="layout-body">
          {viewMode === 'inner' ? (
            tabPanes[findIndex(tabPanes, { key: activeKey })] &&
            tabPanes[findIndex(tabPanes, { key: activeKey })].content
          ) : (
            <>
              <Tabs
                activeKey={activeKey}
                renderTabBar={(props, DefaultTabBar) => (
                  <DefaultTabBar {...props} className="layout-tabs-nav" />
                )}
                type="editable-card"
                onEdit={targetKey => onTabRemove(targetKey, activeKey, tabPanes)}
                onChange={activeKey => onTabChange(activeKey, tabPanes)}
                hideAdd
                animated={IEVersion() !== 9}
                className="layout-body-main"
              >
                {/**
                 * 100% 是size.height的初始值，表示setSize未计算结束
                 * 必须保证加载children前size计算结束，因为children依赖size的
                 * 必须返回undefined。返回null 会出现warning
                 */}
                {size.height === '100%'
                  ? undefined
                  : tabPanes.map(pane => (
                    // eslint-disable-next-line react/jsx-indent
                    <TabPane
                      tab={pane.key === '/' ? '工作台' : pane.title}
                      key={pane.key}
                      closable={pane.key !== '/'}
                      style={{ padding: '8px 8px' }}
                    >
                      {pane.content}
                    </TabPane>
                  ))}
              </Tabs>
              {showLeftMenu && (
                <MenuDrawer
                  currMenu={currMenu}
                  pathname={pathname}
                  history={props?.history}
                />
              )}
            </>
          )}
        </div>
      </Authorized>
    );
  }

  /**
   * 监听postMessage指令
   * 指令格式：{to:"portal",action:"close|open",pathname:"/notice"}
   */
  useEffect(() => {
    // 监听来自低代码平台的提示消息
    function handleLowCode(event) {
      if (!event.data || !event.data.u__$Data) {
        return;
      }
      const data = event.data.u__$Data;
      const { to, action, type, content } = serialize(data);
      if (to !== 'portal' || action !== 'tip') {
        return;
      }
      if (type === 'success') {
        message.success(content);
      } else if (type === 'error') {
        message.error(content);
      } else {
        message.warning(content);
      }
    }

    function handleActions(event) {
      let data;
      // 判断是否是来自低代码平台的消息（u__$Data）
      if (event.data && event.data.u__$Data) {
        data = event.data.u__$Data;
      } else {
        if (!isJSON(event.data)) {
          return;
        }
        data = JSON.parse(event.data);
        if (!isObject(data)) {
          return;
        }
      }

      // 把对象中的key全部变小写且去头尾空格
      const { to, action, pathname, targetmenu, directopen } = serialize(data);

      // 子系统主动发送校验登录
      if (to.toLocaleLowerCase() === 'portal' && action.toLocaleLowerCase() === 'check') {
        dispatch({
          type: 'login/logout',
        });
        return;
      }

      // 不是有效指令，
      if (
        !to ||
        !action ||
        !pathname ||
        to.trim().toLocaleLowerCase() !== 'portal' ||
        !['close', 'open'].includes(action.toLocaleLowerCase())
      ) {
        // message.error('无效指令，正确格式：{to:"portal",action:"close|open",pathname:""}');
        return;
      }
      // 格式化pathname,防止不以斜杆开头，或以斜杆结尾
      let formatPathname = pathname;
      if (!/^\/\w+/i.test(pathname)) {
        formatPathname = `/${pathname}`;
      }
      if (pathname.endsWith('/')) {
        formatPathname = formatPathname.slice(0, formatPathname.length - 1);
      }
      if (action.toLocaleLowerCase() === 'close') {
        destoryMicroKeepAliveByKey(formatPathname);
        const newTabPanes = [...tabPanes.filter(item => item.key !== formatPathname)];
        router.replace(targetmenu ? decodeURIComponent(targetmenu) : newTabPanes[newTabPanes.length - 1].key);
        setTabPanes(newTabPanes);
      }

      if (action.toLocaleLowerCase() === 'open') {
        // directopen表示直接打开，不需要通过openMenu去判断（防止循环引用）
        if (directopen === 'true' || directopen === true) {
          router.replace(formatPathname);
          return;
        }

        // 用于去除url从?开始的参数内容
        const endIndex = formatPathname.indexOf('?') === -1 ? formatPathname.length : formatPathname.indexOf('?');
        const urlParams = formatPathname.slice(endIndex);

        // 如果是打开/iframe/xxx，则用menucode去打开，xxx就是menucode
        if (formatPathname.startsWith('/iframe')) {
          openMenu(formatPathname.slice(8, endIndex), all, dispatch, urlParams);
          return;
        }

        // 如果是打开工作台页面，则通过url去搜索对应的menu
        const urlAddr = formatPathname.slice(0, endIndex);
        const menu = all.find(item => item.urlAddr === urlAddr);

        openMenu(menu, all, dispatch, urlParams);
      }
    }

    // closeAll 关闭所有标签

    function closeAll(event) {
      if (!isJSON(event.data)) {
        return;
      }
      const data = JSON.parse(event.data);
      if (!isObject(data)) {
        return;
      }
      // 把对象中的key全部变小写且去头尾空格
      const { to, action, pathname } = serialize(data);

      if (action.toLocaleLowerCase() === 'closeall') {
        destoryAllMicroKeepAlive();
        const newTabPanes = [...tabPanes.filter(item => item.key === '/')];
        setTabPanes(newTabPanes);
      }
    }

    // window.self === window.top 返回false 说明页面嵌套在iframe中 不需监听当前的postMessage 主系统已经监听
    if (window.self === window.top) {
      window.addEventListener('message', handleActions);
      window.addEventListener('message', handleLowCode);
    }

    window.addEventListener('message', closeAll);

    return () => {
      window.removeEventListener('message', handleActions);
      window.removeEventListener('message', handleLowCode);
    };
  }, [tabPanes]);

  // 监听画布size
  useLayoutEffect(() => {
    setSize();
    window.addEventListener('resize', setSize);
    return () => {
      window.removeEventListener('resize', setSize);
    };
  }, [setSize]);

  // 修复'react-grid-layout'组件在宽度变化时不能适配的bug
  useLayoutEffect(() => {
    if (pathname === '/' || pathname === '/customize/user' || pathname === '/customize/role') {
      delay(triggerResizeEvent, 100);
    }
  }, [pathname]);

  // 只在初始化阶段，鼠标滚轮事件监听
  useEffect(() => {
    // wheel.config({
    //   up: () => {
    //     if (hasScrollbar() && !document.querySelector('body').classList.contains('noScroll')) {
    //       setWheelDirection('up');
    //     }
    //   },
    //   down: () => {
    //     if (hasScrollbar() && !document.querySelector('body').classList.contains('noScroll')) {
    //       setWheelDirection('down');
    //     }
    //   },
    // });
  }, []);

  // 负责更新TabPanes
  // 根据pathname和all判断当前页面是否存在于菜单中
  useEffect(() => {
    if (pathname.toLocaleLowerCase() !== '/') {
      const newMenu = currMenu;
      if (newMenu !== undefined) {
        setTitle(newMenu.menuName);
        // 如果key不存在就要新增一个tabPane
        if (find(tabPanes, { key: pathname }) === undefined) {
          setTabPanes([
            ...tabPanes,
            {
              key: pathname,
              url: pathname + props.location.search || '',
              title: newMenu.menuName,
              content: children,
              openUrl: currMenu.ifAsync && currMenu.ifAsync === '1000' ? currMenu.openUrl : null,
            },
          ]);
          setActiveKey(pathname);

          // 记录打开菜单
          traceLogAction.traceOpenMenuLog({
            menuId: currMenu.menuId,
            menuName: currMenu.menuName,
          });

          // 新增打开标签发起请求
          if (currMenu.ifAsync && currMenu.ifAsync === '1000') {
            // request()
            if (currMenu.openUrl) {
              request(currMenu.openUrl, {
                method: 'GET',
                showError: false,
              });
            }
          }

          // 在菜单有展示而非隐藏时
          if (currMenu.showFlag === '1000') {
            // 更新历史记录菜单
            dispatch({
              type: 'menu/updateRecentMenu',
              payload: currMenu.menuId,
            });
          }
        }
      }
    } else {
      setTitle(home.title);
      setActiveKey('/');
    }
  }, [pathname, all, currMenu, props.location.search, children, dispatch, home.title]);

  // 切换Tab时，判断是否Iframe内嵌的第三方页面，需要控制body的滚动条，避免出现2个
  // useEffect(() => {
  //   if (pathToRegexp('/iframe/:id').test(pathname)) {
  //     document.querySelectorAll('body')[0].style.overflow = 'hidden';
  //   } else {
  //     document.querySelector('body').style.overflow = 'hidden auto';
  //   }
  // }, [pathname]);


  return (
    <>
      <Helmet>
        <title>{`${title} - ${formatMessage({ id: 'app.title' })}`}</title>
      </Helmet>
      <div
        id="layout-container"
        {...wheel.events}
        className={classNames(`view-${viewMode}`, `theme-${theme}`, {
          'base-menu-drawer-active': showLeftMenu && currentOpenLeftMenu && !showSecondMenu,
          'second-menu-drawer-active': showLeftMenu && currentOpenLeftMenu && showSecondMenu,
          'setting-drawer-active': settingDrawerActive,
          'tabs-nav-fixed': true,
        })}
      >
        {viewMode === 'normal' ? <Header /> : null}
        {renderBody(pathname, tabPanes, currMenu, viewMode, size)}
        {viewMode === 'normal' ? (
          <>
            <SettingDrawer />
            <div
              className={classNames('setting-drawer-mask')}
              onClick={() => {
                dispatch({
                  type: 'setting/toggleSettingDrawer',
                  payload: false,
                });
                // 修复'react-grid-layout'组件在宽度变化时不能适配的bug
                delay(triggerResizeEvent, 100);
              }}
            />
          </>
        ) : null}

        {/* {status === mapping.LOCKED && <Locked />} */}

        {/* 操作指引 */}
        <HomeUserGuide />
        <Modal visible={openMenuModal.visible} onCancel={closeMenuModal} footer={null} width="800px">
          <iframe title={openMenuModal.url} src={openMenuModal.url} height="600px" width="752px" />
        </Modal>
      </div>
    </>
  );
}

export default connect(({ setting, menu, login }) => ({
  setting,
  all: menu.all,
  openMenuModal: menu.openMenuModal,
  status: login.status,
}))(BasicLayout);
