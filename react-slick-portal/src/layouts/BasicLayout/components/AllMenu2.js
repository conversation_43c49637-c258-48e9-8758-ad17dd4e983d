import React, { useState, useMemo } from 'react';
import { connect } from 'dva';
import { Menu, Icon, Modal, Popover } from 'antd';
import Link from 'umi/link';
import memoizeOne from 'memoize-one';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import sortBy from 'lodash/sortBy';
import classNames from 'classnames';
import find from 'lodash/find';
import request from '@/utils/request';
import mapping from '@/services/menu';
import {
  getFirstLevelMenu,
  getSecondLevelMenuInfo,
  getThirdLevelMenuInfo,
} from './utils';
import { getItem, getFinalUrl, toArray, isNumber, isEmptyArray, openWindowTab } from '@/utils/utils';
import styles from '../less/header.less';
import styles2 from '../less/allMenu2.less';

const { MENU_TYPE_DIR } = mapping;
const columnWidth = 160;
const LEAF_MENU_TYPE = '1100'; // 叶子菜单的menuType
const getColumnCount = menus => menus.length <= 4 ? menus.length : 4;

// const _quickSearch = memoizeOne(quickSearch, isEqual);
const _getFirstLevelMenu = memoizeOne(getFirstLevelMenu, isEqual);
// const _getAllMenuByLetter = memoizeOne(getAllMenuByLetter, isEqual);
const _getSecondLevelMenuInfo = memoizeOne(getSecondLevelMenuInfo, isEqual);
const _getThirdLevelMenuInfo = memoizeOne(getThirdLevelMenuInfo, isEqual);

function AllMenu2({ hideAllMenu, menu: { all, appSysCode, taskSysCode } }) {
  const [visible, setVisible] = useState(false);
  const [modalUrl, setModalUrl] = useState('');
  const user = getItem('user');
  const { sessionId, userInfo } = user;

  function serialize(all, appSysCode, taskSysCode) {
    const raw = cloneDeep(all);
    const result = [];
    const menuLel1 = _getFirstLevelMenu(raw, appSysCode, taskSysCode);
    menuLel1.forEach(item1 => {
      if (item1.menuType === `${MENU_TYPE_DIR}`) {
        let children = sortBy(_getSecondLevelMenuInfo(raw, item1.menuId), 'children');

        if (Array.isArray(children) && children.length > 0) {
          children = children.map(item2 => {
            const grandson = sortBy(_getThirdLevelMenuInfo(raw, item2.menuId), 'children');
            if (Array.isArray(grandson) && grandson.length > 0) {
              item2.children = grandson;
            }
            return item2;
          });

          item1.children = children;
        }
      }
      result.push(item1);
    });
    return result;
  }

  const menuArr = useMemo(() => serialize(all, appSysCode, taskSysCode), [all, appSysCode, taskSysCode]);

  async function openMenu(menu) {
    // var MENU_OPEN_MODE_WORK_BENCH_LABEL = "1"; //工作台标签页打开
    // var MENU_OPEN_MODE_POPUP = "2"; //弹出框打开
    // var MENU_OPEN_MODE_NEW_BROWSER = "3"; //新开浏览器打开
    // var MENU_OPEN_MODE_BROWSER_LABEL = "4"; //浏览器新标签打开
    let menuUrl = menu.urlAddr.replace('[iframe]', '');
    if (menuUrl.indexOf('?') === -1) {
      menuUrl += `?bss3SessionId=${sessionId}`;
    } else {
      menuUrl += `&bss3SessionId=${sessionId}`;
    }

    if (menuUrl.indexOf('{SYS_USER_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_USER_ID}', userInfo.userCode);
    }
    if (menuUrl.indexOf('{SYS_ORG_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_ORG_ID}', userInfo.userOrgId);
    }

    // 采用SSO单点校验方式进行菜单打开
    if (menuUrl.indexOf('ssoType=1') !== -1) {
      // let urlParams = parseUrlParams(menuUrl);
      const urlParams = {
        bssSessionId: sessionId,
      };
      // 菜单地址SSO加密，拼接返回的signString和singTimestamp
      const response = await request(
        `orgauth/SystemInfoController/calcSign.do?systemInfoId=${menu.systemCode}`,
        {
          method: 'POST',
          data: urlParams,
        }
      );

      if (response && response.resultCode === '0') {
        menuUrl = `${menuUrl}&signString=${response.signString}&signTimestamp=${response.signTimestamp}`;
      }
    }

    if (menu.menuOpenMode === '4') {
      openWindowTab(menuUrl);
    } else if (menu.menuOpenMode === '3') {
      window.open(menuUrl, menu.menuName, 'width=800,height=600,location=yew,toolbar=yes');
    } else {
      setVisible(true);
      setModalUrl(menuUrl);
      hideAllMenu(false);
    }
  }

  // 负责渲染级联菜单
  function renderMenu(menu) {
    // 1100 表示叶子节点
    if (menu.menuType === LEAF_MENU_TYPE) {
      if (menu.menuOpenMode !== '1') {
        return (
          <a
            onClick={() => openMenu(menu)}
            className={classNames('text-ellipsis')}
            key={menu.menuId}
          >
            {menu.menuName}
          </a>
        );
      }
      return (
        <Link
          to={getFinalUrl(menu, undefined, user)}
          title={menu.menuName}
          className="text-ellipsis"
          data-reg-id={`topMenu_${menu.menuCode}`}
        >
          {menu.menuName}
        </Link>
      );
    }
    return (
      <a title={menu.menuName} href="#" className="text-ellipsis">
        {menu.menuName}
        <Icon type="right" />
      </a>
    );
  }

  // 升序
  const menuSortUp = (a, b) => {
    if (!Array.isArray(a?.children) && !Array.isArray(b?.children)) {
      return 0;
    }

    if (!Array.isArray(a?.children)) {
      return -1;
    }

    if (!Array.isArray(b?.children)) {
      return 1;
    }

    return a.children.length - b.children.length;
  };

  // 降序
  const menuSortDown = (a, b) => {
    if (!Array.isArray(a?.children) && !Array.isArray(b?.children)) {
      return 0;
    }

    if (!Array.isArray(a?.children)) {
      return 1;
    }

    if (!Array.isArray(b?.children)) {
      return -1;
    }

    return b.children.length - a.children.length;
  };

  // 升序
  const colSortUp = (a, b) => a.count - b.count;

  // 降序
  const colSortDown = (a, b) => b.count - a.count;

  // 将目录即菜单的目录，归并到一个新的菜单组中
  const formatMenuGroups = (menuGroups, masterMenu) => {
    // 先取出正常的目录
    const res = toArray(menuGroups).filter(item => item.menuType !== LEAF_MENU_TYPE);

    const newMenuGroup = {
      menuName: masterMenu?.menuName,
      menuId: masterMenu?.menuId,
    };

    const children = toArray(menuGroups).filter(item => item.menuType === LEAF_MENU_TYPE);
    newMenuGroup.children = children;

    return children.length > 0 ? [...res, newMenuGroup] : menuGroups;
  };

  // 瀑布流
  // 最终效果是尽量保持原始顺序，尽量让各列长度相等
  // 假设有24个菜单，共5列，那么每次取5个，最后一列不够取，就取4个
  // 第一次排序，把取来的5个菜单从短到长排好
  // 第二次排序，把第二次取来的5个菜单从长到短排好，分别接到之前的对应列
  // 也就是最短+最长，第二短+第二长…………
  // 如此重复
  // 从第三次开始，从短到长的队列就不一定是按照序号了，每次都要重新计算从短到长的序列号顺序
  const waterfallLayout = (_menuGroups, masterMenu) => {
    const menuGroups = formatMenuGroups(_menuGroups, masterMenu);
    const columnCount = getColumnCount(menuGroups);

    // 创建列容器
    let cols = [];
    for (let i = 0; i < columnCount; i += 1) {
      cols.push([]);
    }

    // 用到多少列，每次就取多少列进入循环体操作
    // up为true时按升序排列，为false时按降序排列
    for (let i = 0, up = true; i < menuGroups.length; i += columnCount, up = !up) {
      // 第一次，把取出的列按从短到长排好
      // 第二次，把5个从长到短排好，分别接到之前的对应列

      // 本次进入循环体操作的菜单组
      let groups = menuGroups.slice(i, i + columnCount);

      groups = groups.sort(up ? menuSortUp : menuSortDown);

      for (let j = 0; j < columnCount; j += 1) {
        const group = groups[j];

        if (group) {
          // 将菜单组存入列容器
          cols[j].push(group);

          // 更新列容器长度count
          const count = isNumber(cols[j].count) ? cols[j].count : 0;
          // +1 是加上标题目录的长度
          const newCount = (isEmptyArray(group.children) ? 0 : group.children.length) + 1;
          cols[j].count = count + newCount;
        }
      }

      // 将cols中的列容器根据count重新排序
      cols = cols.sort(up ? colSortUp : colSortDown);
    }

    // 最后再进行一次升序排列
    cols = cols.sort(colSortDown);

    return cols;
  };


  /**
   * 多列布局(2或3列)，适用于有3级菜单的
   * @param {object[]} menuLvl2
   */
  const newMultiColumnLayout = (menuLvl2, masterMenu) => {
    const cols = waterfallLayout(menuLvl2, masterMenu);

    return (
      <div className={styles2.panel}>
        {
          cols.map(col => (
            <div className={styles2.col}>
              {
                col.map((group, n) => (
                  <div key={`${group.menuName}_${n}`} style={{ breakInside: 'avoid' }}>
                    <div className={styles2.menuName}>
                      <span>{group.menuName}</span>
                    </div>
                    <div>
                      <ul>
                        {toArray(group.children).map((menu, i) => (
                          <li className={styles2['ng-item']} key={`${menu.menuId}_${i}`}>
                            {renderMenu(menu)}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))
              }
            </div>
          ))
        }
      </div>
    );
  };

  /**
   * 单列布局，适用于没有3级菜单
   * @param {*} childrens
   */
  const singleColumnLayout = menuLel2 => (
    <div>
      <Menu className={styles.menu}>
        {menuLel2.map(child => (
          <Menu.Item key={child.menuId}>
            {child.menuOpenMode !== '1' ? (
              <a
                onClick={() => openMenu(child)}
                className={classNames(styles2.item, 'text-ellipsis')}
              >
                {child.menuName}
              </a>
            ) : (
              <Link
                className={classNames('text-ellipsis')}
                to={getFinalUrl(child)}
                onClick={() => hideAllMenu(false)}
              >
                {child.menuName}
              </Link>
            )}
          </Menu.Item>
        ))}
      </Menu>
    </div>
  );

  function handleWidth(menu1) {
    if (!find(menu1.children, 'children')) {
      return columnWidth;
    }
    if (menu1.children.length <= 4) {
      return columnWidth * menu1.children.length;
    }
    return columnWidth * 4;
  }

  return (
    <div className={styles2.navMenu}>
      {menuArr.length > 0 &&
        menuArr.map(menu1 => (
          <Popover
            key={menu1.menuId}
            destroyPopupOnHide
            placement={!find(menu1.children, 'children') ? 'bottom' : 'bottomLeft'}
            overlayStyle={{
              width: handleWidth(menu1),
              maxHeight: document.documentElement.clientHeight,
            }}
            content={
              !find(menu1.children, 'children')
                ? singleColumnLayout(sortBy(menu1.children, 'children'), hideAllMenu)
                : newMultiColumnLayout(sortBy(menu1.children, 'children'), menu1)
            }
            trigger="hover"
            getPopupContainer={trigger => trigger.parentNode}
          >
            <span className={classNames(styles.action)} id={menu1.menuCode}>
              {menu1.menuName} <Icon type="down" className={styles.arrow} />
            </span>
          </Popover>
        ))}

      <Modal visible={visible} onCancel={() => setVisible(false)} footer={null} width="800px">
        <iframe src={modalUrl} height="600px" width="752px" title="iframe" />
      </Modal>
    </div>
  );
}

export default connect(({ menu }) => ({
  menu,
}))(AllMenu2);
