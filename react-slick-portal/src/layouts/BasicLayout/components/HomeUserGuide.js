import React, { useState, useEffect } from 'react';
import Guide from 'byte-guide';
import { connect } from 'dva';
import { find } from 'lodash';
import { getDictData } from '@/services/common';
import styles from '../less/homeUserGuide.less';

function HomeUserGuide(props) {
  const { guideVisible, currentOpenLeftMenu, user, dispatch } = props;
  const [homeUserGuideSteps, setHomeUserGuideSteps] = useState([]);

  const guideClose = () => {
    const { body } = document;
    body.parentNode.style.overflow = 'scroll';
    window.scrollTo(0, 0);
    dispatch({
      type: 'setting/updateSettings',
      payload: { guideVisible: false },
    });
  };

  const initData = async () => {
    const portalRole = find(user.portalRoles, { sysRoleId: user.userInfo.roleId });
    const defaultPlacement = 'bottom';
    const guides = await getDictData({ groupCode: portalRole.sysRoleCode });
    const _homeUserGuideSteps = guides.map(guide => ({
      title: guide?.groupName ?? '操作指引',
      selector: guide.paramCode,
      content: <div className="modalcontent">{guide.paramValue}</div>,
      placement: guide?.paramName ?? defaultPlacement,
    }));
    setHomeUserGuideSteps(_homeUserGuideSteps);

    if (user.userInfo.loginedNum <= 1) {
      dispatch({
        type: 'setting/updateSettings',
        payload: { guideVisible: true },
      });
    }
  };

  useEffect(() => {
    initData();
  }, [user.userInfo]);

  useEffect(() => {
    if (!currentOpenLeftMenu && guideVisible) {
      dispatch({
        type: 'setting/updateSettings',
        payload: { currentOpenLeftMenu: true },
      });
    }
  }, [guideVisible]);

  return (
    <Guide
      modalClassName={styles.modalstyle}
      className="homeUserGuide"
      steps={homeUserGuideSteps}
      onClose={guideClose}
      stepText={(stepIndex, stepCount) => `第${stepIndex}步，共${stepCount}步`}
      nextText="下一步"
      okText="我知道了"
      visible={guideVisible}
      mask
    />
  );
}


export default connect(({ setting, login }) => ({
  guideVisible: setting.guideVisible,
  currentOpenLeftMenu: setting.currentOpenLeftMenu,
  user: login.user,
}))(HomeUserGuide);
