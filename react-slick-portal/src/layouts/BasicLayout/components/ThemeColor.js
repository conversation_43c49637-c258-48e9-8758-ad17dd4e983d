import React from 'react';
import { Icon } from 'antd';
import styles from '../less/themeColor.less';

const Tag = ({ color, check, ...rest }) => (
  <div
    {...rest}
    style={{
      backgroundColor: color,
    }}
  >
    {check ? <Icon type="check" /> : ''}
  </div>
);

const ThemeColor = ({ colors, title, value, onChange }) => {
  let colorList = colors;
  if (!colors) {
    colorList = [
      {
        key: 'blue',
        color: '#1890FF',
      },
      {
        key: 'darkBlue',
        disabled: true,
        color: '#4E68BC',
      },
      {
        key: 'orange',
        disabled: true,
        color: '#fa9022',
      },
    ];
  }
  return (
    <div className={styles.themeColor}>
      <h3 className={styles.title}>{title}</h3>
      <div className={styles.content}>
        {colorList.map(({ key, color, disabled }) => (
          // <Tooltip key={color} title={formatMessage({ id: `app.setting.themecolor.${key}` })}>
          <Tag
            className={styles.colorBlock}
            key={key}
            color={color}
            disabled={disabled}
            check={value === color}
            onClick={() => onChange && onChange(key)}
          />
          // </Tooltip>
        ))}
      </div>
    </div>
  );
};

export default ThemeColor;
