const TASK_MAP = {
  TASK_MANAGER: 'RWGL_MENU_TASK_MANAGE',
  // 预警任务
  WARNING_TASK: {
    MENUCODE: 'RWGL_MENU_TASK001',
    REDWARNING: 'RWGL_MENU_TASK001_1',
    HIGHRISK: 'RWGL_MENU_TASK001_2',
    CUSTOMERCOMPLAINTS: 'RWGL_MENU_TASK001_3',
    CONTRACTEXPIRES: 'RWGL_MENU_TASK001_4',
  },

  //  集团营销
  SPECIAL_MARKETING_TASK: {
    MENUCODE: 'RWGL_MENU_TASK002',
    SPECIAL_MARKETING: 'RWGL_MENU_TASK002_1',
  },

  // 生日关怀
  SERVICE_TASK: {
    MENUCODE: 'RWGL_MENU_TASK004',
    BIRTHDAY_SERVICE: 'RWGL_MENU_TASK004_1',
  },

  // 客户任务
  CUST_TYPE: {
    MENUCODE: 'RWGL_MENU_TASK005',
    CUST_SERVICE: 'RWGL_MENU_TASK005_1',
  },

  // 拜访任务
  VISIT_TASK: {
    MENUCODE: 'RWGL_MENU_TASK003',
    VISIT_TASK: 'RWGL_MENU_TASK003_1',
  },

  // 商机任务
  OPP_TASK: {
    MENUCODE: 'RWGL_MENU_TASK006',
    OPP_TASK: 'RWGL_MENU_TASK006_1',
  },

  // 售前支撑任务
  OPP_PRE_TASK: {
    MENUCODE: 'RWGL_MENU_TASK015',
    PRE_SALE_TASK: 'RWGL_MENU_TASK015_1',
  },



  SURVEY_TYPE: {
    MENUCODE: 'RWGL_MENU_TASK010',
    SURVEY_TYPE: 'RWGL_MENU_TASK010_1',
  },

  AGREEMENT_TYPE: {
    MENUCODE: 'RWGL_MENU_TASK007',
    AGREEMENT_SERVICE: 'RWGL_MENU_TASK007_1',
    CONTACT_TEMPLATE_SERVICE: 'RWGL_MENU_TASK007_2',
  },

  ORDER_TYPE: {
    MENUCODE: 'RWGL_MENU_TASK008',
    ORDER_SERVICE: 'RWGL_MENU_TASK008_1',
  },

  CPC_TYPE: {
    MENUCODE: 'RWGL_MENU_TASK009',
    CPC_TYPE: 'RWGL_MENU_TASK009_1',
  },
  BPC_TYPE: {
    MENUCODE: 'RWGL_MENU_TASK011',
    BPC_SERVICE: 'RWGL_MENU_TASK011_1',
  },
  JLB_TYPE: {
    MENUCODE: 'RWGL_MENU_TASK012',
    PROJECT_SERVICE: 'RWGL_MENU_TASK012_1',
    COLLABORATION_SERVICE: 'RWGL_MENU_TASK012_2',
  },
  //调度任务
  DISPATCH_TYPE: {
    MENUCODE: 'RWGL_MENU_TASK013',
    TIME_OUT_DISPATCH: 'RWGL_MENU_TASK013_1',
  },
  //云网任务
  CLOUD_TYPE: {
    MENUCODE: 'RWGL_MENU_TASK014',
    ICP_RECORD_SERVICE: 'RWGL_MENU_TASK014_1',
  },
};
export default TASK_MAP;
