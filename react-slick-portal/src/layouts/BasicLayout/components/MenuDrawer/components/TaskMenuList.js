import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Menu, Icon } from 'antd';
import { connect } from 'dva';
import PubSub from 'pubsub-js';
import styles from '../../../less/menuDrawer.less';
import { getWaitMenuCount, getWaitMenuList } from '@/services/menu';
import { transformToArray, openMenu, isEmptyStr, isEmptyArray } from '@/utils/utils';

const TaskMenuList = props => {
  const { all, selectedKeys, setSelectedKeys, show, setLoading, dispatch, openMenuOfWait, waitRefreshInfoRef, setWaitRefreshInfo } = props;
  const [openKeys, setOpenKeys] = useState([]);
  // const [allCount, setAllCount] = useState(0); // 待办任务总数
  const [taskMenuList, setTaskMenuList] = useState([]);

  const openTaskWithSubTaskCode = subTaskCode => {
    if (!subTaskCode) {
      return;
    }

    if (subTaskCode === 'openTheFirst') {
      setSelectedKeys([taskMenuList?.[0]?.children?.[0]?.menuCode]);
      setOpenKeys([taskMenuList?.[0]?.menuCode]);
    }

    const taskCode = transformToArray(taskMenuList)
      .find(item => transformToArray(item?.children).findIndex(menu => menu?.menuCode === subTaskCode) !== -1)?.menuCode;

    setOpenKeys([taskCode]);
  };

  const formTreeData = (menuInfos, menuId) => {
    const resultMenu = [];
    for (let i = 0; i < menuInfos.length; i += 1) {
      const menuInfo = menuInfos[i];
      if (menuInfo.pId === menuId) {
        if (menuInfo.isParent) {
          menuInfo.isLeaf = false;
          menuInfo.children = formTreeData(menuInfos, menuInfo.menuId);
        } else {
          menuInfo.isLeaf = true; //
        }
        resultMenu.push(menuInfo);
      }
    }
    return resultMenu;
  };

  // const formatCount = count => isNumber(count) && Number(count) > 999 ? '999+' : count;

  const addCountForTreeData = (treeData, countInfo) =>
    transformToArray(treeData).map(item => {
      const taskCountInfos = transformToArray(countInfo?.taskInfos).find(info => info?.menuCode === item?.menuCode);

      const res = { ...item };
      res.count = taskCountInfos?.count;

      res.children = transformToArray(item?.children).map(task => ({
        ...task,
        count: transformToArray(taskCountInfos?.subTaskInfo).find(info => info?.menuCode === task?.menuCode)?.count ?? 0,
      }));

      return res;
    });

  // 获取待办任务数量
  // const getWaitTaskCount = list => {
  //   let count = 0;

  //   toArray(list).forEach(item => {
  //     count += toArray(item?.children).reduce((pre, cur) => pre + toNumber(cur.count), 0);
  //   });

  //   return count;
  // };

  const dealMenu = (menuList, menuCodeList) => {
    menuList.forEach(menu => {
      if (!isEmptyStr(menu.menuCode)) {
        menuCodeList.push(menu.menuCode);
      }

      if (!isEmptyArray(menu.children)) {
        dealMenu(menu.children, menuCodeList);
      }
    });
  };

  const getMenusCode = menusList => {
    const menuCodeList = [];
    dealMenu(menusList, menuCodeList);

    return menuCodeList;
  };

  const initTaskTree = async (showLoading, firstLoading) => {
    const expandedkeysData = [];
    if (showLoading && !firstLoading) {
      setLoading(true);
    }

    const query1 = getWaitMenuList();
    // const query2 = getWaitMenuCount();
    const allMenuInfo = await query1;
    // const countInfo = await query2;

    const taskJsonTemp = [];
    // 根目录菜单id
    const fistMenuId = 276890; // 任务管理一级菜单
    let secondMenuList = ''; // 二级菜单列表
    allMenuInfo.forEach(menuInfo => {
      // 二级菜单
      if (menuInfo.parMenuId === fistMenuId) {
        taskJsonTemp.push({
          id: menuInfo.menuCode,
          menuCode: menuInfo.menuCode,
          pId: fistMenuId,
          name: menuInfo.menuName,
          isParent: menuInfo.menuType === '1000',
          menuId: menuInfo.menuId,
          count: 0,
          urlAddr: menuInfo.menuType === '1000' ? '' : menuInfo.urlAddr,
        });
        secondMenuList = `${secondMenuList},${menuInfo.menuId}`;
      }
      expandedkeysData.push(menuInfo.id);
    });

    allMenuInfo.forEach(menuInfo => {
      // 三级菜单
      if (menuInfo.menuLevel === 2 && secondMenuList.indexOf(menuInfo.parMenuId) !== -1) {
        taskJsonTemp.push({
          id: menuInfo.menuCode,
          menuCode: menuInfo.menuCode,
          menuId: menuInfo.menuId,
          pId: menuInfo.parMenuId,
          name: menuInfo.menuName,
          isParent: menuInfo.menuType === '1000',
          count: 0,
          urlAddr: menuInfo.menuType === '1000' ? '' : menuInfo.urlAddr,
        });
        expandedkeysData.push(menuInfo.id);
      }
    });
    const treeData = formTreeData(taskJsonTemp, fistMenuId);
    const menuCodeList = getMenusCode(transformToArray(treeData));

    let countInfo = [];
    if (firstLoading) {
      const finalTreeData = addCountForTreeData(treeData, countInfo);
      setTaskMenuList(finalTreeData);
    }
    if (!isEmptyArray(menuCodeList)) {
      // countInfo = await getWaitMenuCount(menuCodeList);
    }

    const finalTreeData = addCountForTreeData(treeData, countInfo);
    setTaskMenuList(finalTreeData);

    dispatch({
      type: 'undo/save',
      payload: {
        authorityUndoList: finalTreeData,
      },
    });
    // setAllCount(getWaitTaskCount(finalTreeData));
    if (showLoading) {
      setLoading(false);
      setWaitRefreshInfo({ ...waitRefreshInfoRef.current, loading: false });
    }
    return finalTreeData;
  };

  useImperativeHandle(props.cRef, () => ({
    initTaskTree: () => {
      initTaskTree(true, false);
    },
  }));

  useEffect(() => {
    openTaskWithSubTaskCode(selectedKeys?.[0]);
  }, [selectedKeys]);

  const initData = async () => {
    initTaskTree(true, true);
  };

  useEffect(() => {
    initData();
  }, []);


  useEffect(() => {
    // 订阅"打开左侧待办任务菜单列表，并打开相应页签"的消息
    PubSub.subscribe('openWaitTaskListAndTab', (info, menuCode) => {
      if (info === 'openWaitTaskListAndTab' && taskMenuList.length > 0) {
        openMenuOfWait(menuCode, taskMenuList);
      }
    });

    return () => {
      PubSub.unsubscribe('openWaitTaskListAndTab');
    };
  }, [taskMenuList]);

  const isOpened = key => openKeys.includes(String(key));
  const getIconType = key => isOpened(key) ? 'down' : 'right';

  return (
    <div className={styles.taskMenuList} style={{ display: show ? 'block' : 'none' }}>
      <Menu
        mode="inline"
        openKeys={openKeys}
        onOpenChange={_openKeys => { setOpenKeys(_openKeys); }}
        selectedKeys={selectedKeys}
      >
        {
          transformToArray(taskMenuList).map(item => (
            <Menu.SubMenu
              key={item.menuCode}
              title={(
                <span>
                  <Icon type={getIconType(item.menuCode)} style={{ marginRight: 4, fontSize: 10 }} />
                  <span>
                    <span className={styles.menuText}>{item.name}</span>
                    <span style={{ color: (Number(item.count) > 0 || item.count === '999+') ? 'red' : 'inherit' }}>({item?.count ?? 0})</span>
                  </span>
                </span>
              )}
            >
              {
                transformToArray(item.children).map(menu => (
                  <Menu.Item key={menu.menuCode}>
                    <div
                      className="text-ellipsis"
                      title={menu.name}
                      data-reg-id={`wait_${menu.menuCode}`}
                      onClick={() => {
                        openMenu(menu, all, dispatch);
                      }}
                    >
                      <span>
                        <span className={styles.menuText}>{menu.name}</span>
                        <span style={{ color: Number(menu.count) > 0 ? 'red' : 'inherit' }}>({menu?.count ?? 0})</span>
                      </span>
                    </div>
                  </Menu.Item>
                ))
              }
            </Menu.SubMenu>
          ))
        }
      </Menu>
    </div>
  );
};

export default connect(({ menu }) => ({
  all: menu.all,
}))(TaskMenuList);
