import React, { Component } from 'react';
import { Layout, Icon, Badge, Modal } from 'antd';
import { formatMessage } from 'umi/locale';
import classNames from 'classnames';
import { connect } from 'dva';
import PubSub from 'pubsub-js';
import Link from 'umi/link';
import Trigger from 'rc-trigger';
import Debounce from 'lodash-decorators/debounce';
import SelectLang from '@/components/SelectLang';
import { enableLanguage } from '@/defaultSettings';
import RecentMenuDropdown from './RecentMenuDropdown';
import AccountUpdate from '@/pages/FourAAccountManage/AccountUpdate';
import AllMenu2 from './AllMenu2';
import CollectionMenuDropdown from './CollectionMenuDropdown';
// import SearchIcon from './SearchIcon';
import 'rc-trigger/assets/index.css';
import styles from '../less/header.less';
import UserInfo from './UserInfo';
import AllMenu from './AllMenu';
import HeaderSearchMenu from './HeaderSearchMenu';
import avatar from '../img/user-head.png';
import { getItem, isEmptyStr } from '@/utils/utils';
import { getRoleHighSearchList, getUserSearchHistoryList } from '@/services/menu';
import { loginoutResService } from '@/services/common';

const showHeaderMenuSearch = false;
const { Header } = Layout;

class HeaderView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      showMenuPanel: false,
      showSearchMenuPanel: false,
      showUserPanel: false,
    };
  }

  componentDidMount() {
    this.getRecommendList();
  }

  getRecommendList = async () => {
    const query1 = getRoleHighSearchList(getItem('user')?.userInfo?.roleId);
    const query2 = getUserSearchHistoryList();
    const _heightSearchList = await query1;
    const _searchHistoryList = await query2;

    const { dispatch } = this.props;
    dispatch({
      type: 'menu/saveRecommendMenuList',
      payload: {
        heightSearchList: _heightSearchList,
        searchHistoryList: _searchHistoryList,
      },
    });
  };

  handleLogout = e => {
    e.preventDefault();
    const { dispatch, login: { loginoutResSerList } } = this.props;
    // 手动更新ok按钮loading状态
    // onOk返回promise对象时，只有在resolve()之后才会关闭
    const modal = Modal.confirm({
      title: formatMessage({ id: 'logoutConfirm' }),
      onOk() {
        loginoutResService({ loginoutResSerList });

        modal.update({ okButtonProps: { loading: true } });
        return new Promise(resolve => {
          dispatch({
            type: 'login/logout',
          }).then(() => {
            modal.update({ okButtonProps: { loading: true } });
            resolve();
          });
        }).catch(() => console.log('Oops errors!'));
      },
    });
  };

  toggleSettingDrawer = () => {
    const {
      dispatch,
      setting: { settingDrawerActive },
    } = this.props;

    dispatch({
      type: 'setting/toggleSettingDrawer',
      payload: !settingDrawerActive,
    });
  };

  saveCurrentOpenLeftMenu = () => {
    const {
      dispatch,
      setting: { currentOpenLeftMenu },
    } = this.props;
    dispatch({
      type: 'setting/saveCurrentOpenLeftMenu',
      payload: !currentOpenLeftMenu,
    });
    this.triggerResizeEvent();
  };

  hideAllMenu = isVisible => {
    if (isVisible) {
      this.setState({ showMenuPanel: true }, () => {
        document.querySelector('body').classList.add('noScroll');
      });
    } else {
      this.setState({ showMenuPanel: false }, () => {
        document.querySelector('body').classList.remove('noScroll');
      });
    }
  };

  setShowSearchMenuPanel = isVisible => {
    if (isVisible) {
      this.setState({ showSearchMenuPanel: true }, () => {
        document.querySelector('body').classList.add('noScroll');
      });
    } else {
      this.setState({ showSearchMenuPanel: false }, () => {
        document.querySelector('body').classList.remove('noScroll');
      });
    }
  };

  showUserInfo = isVisible => {
    if (isVisible) {
      this.setState({ showUserPanel: true });
    } else {
      this.setState({ showUserPanel: false });
    }
  };

  handleLock = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'login/lock',
    });
  };

  // 图片无法加载时，使用默认图片
  imgExists = e => {
    // 默认图片
    const imgUrl = avatar;
    if (imgUrl) {
      e.target.src = imgUrl;
    }
  };

  // 300毫秒是侧边栏menu-drawer动画的过渡时间，保证在动画结束后触发
  @Debounce(300)
  // eslint-disable-next-line class-methods-use-this
  triggerResizeEvent() {
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }


  render() {
    const {
      login: {
        user: {
          userInfo: { userName, userPhoto },
        },
      },
      setting: { showLeftMenu },
      unRead,
      showSubSystemAccountSelect,
    } = this.props;


    return (
      <Header id="layout-header" className={classNames(styles.wrapper)}>
        <div className="media-box">
          <div className="media-left">
            {/* 侧边栏开关 */}
            {showLeftMenu && (
              <span className={styles.action} onClick={this.saveCurrentOpenLeftMenu}>
                <Icon type="align-left" />
              </span>
            )}
            {/* logo */}
            <span className={classNames(styles.logo, styles.action)} />
            {/* title */}
            <span className={classNames(styles.title, styles.action)}>
              {formatMessage({ id: 'app.title' })}
            </span>
            {/* 全部菜单 */}
            <Trigger
              popupPlacement="bottomLeft"
              action={['hover']}
              popupAlign={{
                overflow: {
                  adjustX: 1,
                  adjustY: 1,
                },
              }}
              // getPopupContainer={() => document.getElementById('layout-header')}
              mouseEnterDelay={0.3}
              popupClassName={styles.allMenuDropdown}
              builtinPlacements={{
                bottomLeft: {
                  points: ['tl', 'bl'],
                },
              }}
              popupVisible={this.state.showMenuPanel}
              // popupTransitionName="slide-up"
              popup={<AllMenu hideAllMenu={this.hideAllMenu} />}
              onPopupVisibleChange={isVisible => this.hideAllMenu(isVisible)}
              onPopupAlign={() => {
                // $(popupDomNode).css({
                //   left: '0',
                //   top: '60px',
                //   right: '0',
                //   position: 'fixed',
                // })
                // 防止被layout-tabs-nav遮挡
                document.querySelector(
                  '.rc-trigger-popup'
                ).parentNode.parentNode.style.zIndex = 1000;
              }}
            >
              <span
                className={classNames(styles.allMenu, styles.action, {
                  [styles.active]: this.state.showMenuPanel,
                })}
                id="header_windows"
              >
                <Icon type="windows" theme="filled" />
              </span>
            </Trigger>
            {/* 搜索框 */}
            {
              showHeaderMenuSearch && (
                <HeaderSearchMenu
                  setShowSearchMenuPanel={this.setShowSearchMenuPanel}
                  searchHistoryList={this.state.searchHistoryList}
                  heightSearchList={this.state.heightSearchList}
                  showDropDown={this.state.showSearchMenuPanel}
                  getRecommendList={this.getRecommendList}
                />
              )}
            {!showLeftMenu && (
              <>
                <CollectionMenuDropdown />
                <RecentMenuDropdown />
              </>
            )}
          </div>
          <div className="media-right">
            {/* 快速报障 */}
            {/* <span
              onClick={() =>
                PubSub.publish('quick_report', {
                  reportTo: 'all',
                  ejectMode: 'serial',
                  type: 'service',
                  system: '03',
                  natureOfFailure: '2', // 1代表一键报障 2代表快捷报障
                })
              }
              className={classNames(styles.action)}
            >
              <Icon type="bug" />
            </span> */}
            {/* 快捷搜索 */}
            {/* <SearchIcon /> */}
            {/* 系统消息 */}
            <Link to="/notice" className={classNames(styles.notice, styles.action)}>
              {unRead ? (
                <Badge count={unRead}>
                  <Icon type="bell" />
                </Badge>
              ) : (
                <Icon type="bell" />
              )}
            </Link>
            {/* 用户面板 */}
            <Trigger
              popupPlacement="bottomLeft"
              action={['hover']}
              popupAlign={{
                overflow: {
                  adjustX: 1,
                  adjustY: 1,
                },
              }}
              mouseEnterDelay={0.3}
              popupClassName={styles.allMenuDropdown}
              builtinPlacements={{
                bottomLeft: {
                  points: ['tl', 'bl'],
                },
              }}
              popupTransitionName="slide-up"
              popup={<UserInfo showUserInfo={this.showUserInfo} />}
              popupVisible={this.state.showUserPanel}
              onPopupVisibleChange={isVisible => this.showUserInfo(isVisible)}
            >
              <div
                className={classNames(styles.avatar, styles.action, {
                  [styles.active]: this.state.showUserPanel,
                })}
              >
                <img
                  alt="avatar"
                  src={isEmptyStr(userPhoto) ? '' : `portal/FileStoreController/dfsReadImage.do?docLink=${userPhoto}`}
                  onError={this.imgExists.bind(this)}
                  height="24"
                  width="24"
                />
                <span className="margin-left-sm">{userName}</span>
                <Icon type="down" />
              </div>
            </Trigger>
            {/* 退出 */}
            <span onClick={this.handleLogout} className={classNames(styles.logout, styles.action)}>
              <Icon type="poweroff" />
            </span>
            {/* 切换语言 */}
            {enableLanguage ? <SelectLang className={classNames(styles.action)} /> : null}

            {/* 锁屏 */}
            {/* <span onClick={this.handleLock} className={classNames(styles.action)}>
            <Icon type="lock" />
          </span> */}
            {/* 设置面板开关 */}
            <span className={styles.action} onClick={this.toggleSettingDrawer}>
              <Icon type="align-right" />
            </span>
          </div>


          <div className="media-body">
            <AllMenu2 hideAllMenu={this.hideAllMenu} />
          </div>
        </div>
        {
          showSubSystemAccountSelect === 'edit' && (
            <AccountUpdate />
          )
        }
      </Header>
    );
  }
}

export default connect(({ login, setting, notice, fourAAccount }) => ({
  login,
  setting,
  unRead: notice.unRead,
  showSubSystemAccountSelect: fourAAccount.showSubSystemAccountSelect,
}))(HeaderView);
