import React, { useEffect } from 'react';
import { connect } from 'dva';
import { Spin, Empty } from 'antd';
import HistoryItem from './HistoryItem';
import styles from '../../index.less';

const ChatHistory = ({ message, dispatch, height }) => {
  const { historyList = [], hasMoreHistory, historyPageNum, historyLoading } = message;

  const onChatHistoryScroll = e => {
    if (!hasMoreHistory) return;

    if (e.wheelDelta < 0) {
      const chatHistory = document.getElementById('chatHistory');
      if (chatHistory && (chatHistory.scrollTop + chatHistory.clientHeight + 2) >= chatHistory.scrollHeight) {
        dispatch({ type: 'message/getHistoryList', payload: { pageNum: historyPageNum + 1 } });
      }
      console.log('onChatHistoryScroll chat :: ', historyPageNum, hasMoreHistory);
    }
  };

  const addChatHistoryScrollListener = () => {
    const chatItems = document.getElementById('chatHistory');
    chatItems && chatItems.addEventListener('mousewheel', onChatHistoryScroll, false);
  };

  const removeChatHistoryScrollListener = () => {
    const chatItems = document.getElementById('chatHistory');
    chatItems && chatItems.removeEventListener('mousewheel', onChatHistoryScroll, false);
  };

  useEffect(() => {
    addChatHistoryScrollListener();
    return () => {
      removeChatHistoryScrollListener();
    };
  }, [historyPageNum, hasMoreHistory]);

  return (
    <Spin spinning={historyLoading}>
      <div id="chatHistory" className={styles.chatHistory} style={{ height: `${height}px` }}>
        <div>
          {
            historyList.length
            ? historyList.map(item => (
              <HistoryItem msgInfo={item} />
            ))
            : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }
        </div>
      </div>
    </Spin>
  );
};
export default connect(({ message }) => ({
  message,
}))(ChatHistory);
