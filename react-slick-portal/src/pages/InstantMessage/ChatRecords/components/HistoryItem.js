import React from 'react';
import { Avatar } from 'antd';
import styles from '../../index.less';

const MessageItem = ({ msgInfo = {} }) => {
  const handleName = str => {
    if (str?.length > 2) {
      return str.substring(str.length - 2);
    }
      return str;
  };

  return (
    <div className={styles.historyItem}>
      <div className={styles.avatarImg}>
        <Avatar style={{ backgroundColor: '#2a97f7', verticalAlign: 'middle' }}>
          {handleName(msgInfo.sendName)}
        </Avatar>
      </div>
      <div style={{ flexGrow: '1' }}>
        <div className={styles.sendInfo}>
          <div>{msgInfo.sendName}</div>
          <div>{msgInfo.createDate}</div>
        </div>
        <div className={styles.sendText}>{msgInfo.messageText}</div>
      </div>

    </div>
  );
};
export default MessageItem;
