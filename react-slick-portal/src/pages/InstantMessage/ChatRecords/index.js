import React, { useState } from 'react';
import { connect } from 'dva';
import { Modal, Row, Col, Input, Icon, Tabs } from 'antd';
import ChatHistory from './components/ChatHistory';
import SearchPart from './components/SearchPart';
import styles from '../index.less';

const { TabPane } = Tabs;

const ChatRecords = props => {
  const { size: { height, width }, message, dispatch } = props;
  const { curUser, historyVisible, searchObj } = message;

  // const [searchValue, setSearchValue] = useState('');
  const [activeKey, setActiveKey] = useState('all');

  const closeModal = () => {
    dispatch({ type: 'message/saveRecordsVisible', payload: false });
    dispatch({ type: 'message/updateSearchObj', payload: {} });
  };

  const searchChange = value => {
    // setSearchValue(e?.target?.value || '');
    dispatch({
      type: 'message/changeSearch',
      payload: { ...searchObj, filterVal: value || '' },
    });
  };

  return (
    <Modal
      title={`聊天记录（${curUser.objName || ''}）`}
      visible={historyVisible}
      onCancel={closeModal}
      destroyOnClose
      mask={false}
      maskClosable={false}
      footer={null}
      bodyStyle={{ padding: '0', height: height * 0.75 }}
      width={width * 0.50}
      getContainer={() => document.getElementById('messageModal')}
      wrapClassName={styles.historyModal}
    >
      <div className={styles.recordSearch}>
        <Row>
          <Col>
            <div className={styles.searchInput}>
              <Input.Search
                // value={searchValue}
                placeholder="搜索"
                prefix={<Icon type="search" style={{ color: 'rgba(0,0,0,.25)' }} />}
                onSearch={searchChange}
              />
            </div>
          </Col>
        </Row>
        <Row>
          <Col span={18}>
            <Tabs defaultActiveKey="all" activeKey={activeKey} onChange={key => setActiveKey(key)}>
              <TabPane tab="全部" key="all">
                <ChatHistory height={height * 0.75 - 53 - 33} />
              </TabPane>
            </Tabs>
          </Col>
          <Col span={6}>
            <SearchPart height={height * 0.75 - 53} />
          </Col>
        </Row>
      </div>
    </Modal>
  );
};
export default connect(({ setting, message }) => ({
  size: setting.size,
  message,
}))(ChatRecords);
