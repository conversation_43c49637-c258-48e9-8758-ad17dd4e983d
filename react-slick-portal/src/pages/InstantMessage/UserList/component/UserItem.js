import React from 'react';
import { Avatar } from 'antd';
import moment from 'moment';
import styles from '../../index.less';

const UserItem = ({ userInfo = {}, isCurUser, onClick }) => {
  // objName:好友名称    messageText:最后一条消息   createDate:最后消息时间(今天就显示时分,之前的时间显示月日)
  const { objName = '', messageText = '', createDate = '' } = userInfo;
  const handleName = () => {
    if (objName.length > 2) {
      return objName.substring(objName.length - 2);
    }
    return objName;
  };
  const today = moment().startOf('day');

  return (
    <div
      className={styles.userItem}
      onClick={onClick}
      style={{ background: isCurUser ? '#f4f4f4' : '#FFF', borderRadius: '4px' }}
    >
      <Avatar size={36} style={{ color: '#FFF', backgroundColor: '#2a97f7', fontSize: '12px' }}>
        {handleName()}
      </Avatar>
      <div style={{ flexGrow: '1', marginLeft: '8px' }}>
        <div className={styles.userName}>{objName}</div>
        <div className={styles.lastMsg}>{messageText}</div>
      </div>
      <div className={styles.time}>
        {createDate && (moment(createDate).isSame(today, 'day') ? moment(createDate).format('HH:mm') : moment(createDate).format('MM-DD'))}
      </div>
    </div>
  );
};
export default UserItem;
