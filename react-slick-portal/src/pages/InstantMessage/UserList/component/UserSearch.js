import React from 'react';
import { connect } from 'dva';
import { Empty, Avatar } from 'antd';
import { scrollToBottom } from '@/utils/scroller';
import { openWebSocket } from '@/utils/websocket';
import styles from '../../index.less';

const UserSearch = ({ showSearchResult, message }) => {
  const { searchUsers } = message;

  const selectUser = item => {
    const userObj = {
      userId: item.sysUserId,
      userName: item.userName,
    };
    // 打开聊天窗口
    openWebSocket(userObj);
    showSearchResult(false);
    scrollToBottom('bottomElement', 'chatItems');
  };

  const handleName = str => {
    if (str?.length > 2) {
      return str.substring(str.length - 2);
    }
      return str;
  };

  return (
    <div className={styles.userSearch}>
      {
        searchUsers.length
        ? (
          searchUsers.map(item => (
            <div className={styles.searchResult} onClick={() => selectUser(item)}>
              <Avatar size={36} style={{ color: '#FFF', backgroundColor: '#2a97f7', fontSize: '12px' }}>{handleName(item.userName)}</Avatar>
              <div className={styles.userName}>{item.userName}</div>
            </div>
          ))
        )
        : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      }
    </div>
  );
};
export default connect(({ message }) => ({
  message,
}))(UserSearch);
