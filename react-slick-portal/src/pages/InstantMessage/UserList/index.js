import React, { useState } from 'react';
import { connect } from 'dva';
import { Input, Empty, List, Icon, Popover } from 'antd';
// import InfiniteScroll from 'react-infinite-scroller';
import { scrollToBottom } from '@/utils/scroller';
import UserSearch from './component/UserSearch';
import UserItem from './component/UserItem';
import styles from '../index.less';


const UserList = ({ size: { height }, message, dispatch }) => {
  const { users, curUser } = message;
  const [showSearchPanel, setShowSearchPanel] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  // const [loading, setLoading] = useState(false);
  // const [hasMore, setHasMore] = useState(true);

  const selectUser = async item => {
    const userId = item?.objId;
    if (!userId) return;
    if (curUser.objId !== item.objId) {
      await dispatch({
        type: 'message/changeCurUser',
        payload: item,
      });
      await dispatch({
        type: 'message/getMessageList',
        payload: { chatId: userId, pageNum: 1 },
      });
    }
    scrollToBottom('bottomElement', 'chatItems');
  };

  const showSearchResult = isVisible => {
    if (isVisible) {
      setShowSearchPanel(true);
    } else {
      setShowSearchPanel(false);
    }
  };

  const searchChange = e => {
    setSearchValue(e?.target?.value || '');
    dispatch({
      type: 'message/getSearchUser',
      payload: e?.target?.value || '',
    });
  };

  // 处理用户列表滚动加载，确定下入参是什么
  // const handleInfiniteOnLoad = pages => {
  //   console.log(pages);
  //   // setLoading(true);
  //   // if (users.length > 20) {
  //   //   message.warning('Infinite List loaded all');
  //   //   setLoading(false);
  //   //   setHasMore(false);
  //   //   return;
  //   // }
  //   // let data = getUserData();
  //   // if (Array.isArray(data) && data.length > 0) {
  //   //   data = users.concat(data);
  //   //   setUsers(data);
  //   // }
  //   // setLoading(false);
  // };

  return (
    <div className={styles.userContainer} style={{ height: height * 0.9 }}>
      <div className={styles.search}>
        <Popover
          placement="bottom"
          trigger="click"
          visible={showSearchPanel}
          onVisibleChange={isVisible => showSearchResult(isVisible)}
          content={<UserSearch showSearchResult={showSearchResult} />}
        >
          <Input
            value={searchValue}
            placeholder="搜索"
            prefix={<Icon type="search" style={{ color: 'rgba(0,0,0,.25)' }} />}
            onChange={searchChange}
          />
        </Popover>
      </div>
      <div className={styles.userList}>
        {users.length ? (
          // <InfiniteScroll initialLoad={false} pageStart={0} loadMore={handleInfiniteOnLoad} hasMore={!loading && hasMore} useWindow={false}>
          <List
            dataSource={users}
            renderItem={item => (
              <List.Item key={item.objId}>
                <UserItem userInfo={item} onClick={() => selectUser(item)} isCurUser={curUser.objId === item.objId} />
              </List.Item>
              )}
          >
            {/* {loading && hasMore && (
                <div className="demo-loading-container">
                  <Spin />
                </div>
              )} */}
          </List>
          // </InfiniteScroll>
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </div>
    </div>
  );
};
export default connect(({ setting, message }) => ({
  size: setting.size,
  message,
}))(UserList);
