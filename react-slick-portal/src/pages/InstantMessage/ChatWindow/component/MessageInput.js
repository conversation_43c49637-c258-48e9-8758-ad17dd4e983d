import React from 'react';
import { connect } from 'dva';
import { Input, Button, Icon, Message } from 'antd';
import { scrollToBottom } from '@/utils/scroller';
import styles from '../../index.less';

const MessageInput = ({ message: { curUser, inputValue }, dispatch }) => {
  const changeMsgValue = value => {
    dispatch({
      type: 'message/changeInputValue',
      payload: value || '',
    });
  };

  const onSendMessage = async () => {
    if (!inputValue) {
      return;
    }

    const msgInfo = {
      receiverId: curUser.objId,
      messageType: 'text',
      messageText: inputValue,
    };

    await dispatch({
      type: 'message/sendMessage',
      payload: msgInfo,
    });
    scrollToBottom('bottomElement', 'chatItems');
  };

  const openHistoryModal = () => {
    dispatch({ type: 'message/getHistoryList', payload: { pageNum: 1 } });
    dispatch({ type: 'message/saveRecordsVisible', payload: true });
  };

  return (
    <div className={styles.chatInput}>
      <div className={styles.icon}>
        <Icon type="smile" style={{ marginRight: '12px', fontSize: '16px' }} onClick={() => Message.info('功能开发中...')} />
        <Icon type="file" style={{ marginRight: '12px', fontSize: '16px' }} onClick={() => Message.info('功能开发中...')} />
        <Icon type="message" style={{ marginRight: '12px', fontSize: '16px' }} onClick={openHistoryModal} />
      </div>
      <Input.TextArea
        placeholder="请输入消息"
        autoSize={{ minRows: 6, maxRows: 6 }}
        value={inputValue}
        onChange={e => changeMsgValue(e.target.value)}
        onPressEnter={e => {
          if (e.ctrlKey && e.keyCode === 13) {
            changeMsgValue(`${inputValue}\n`);
          } else if (e.key === 'Enter' && e.keyCode === 13) {
            if (inputValue?.replaceAll('\n', '')?.trim()?.length > 0) {
              onSendMessage();
            }
            e.preventDefault(); // 禁止回车的默认换行
          }
        }}
      />
      <div className={styles.chatSendBtn}>
        <div className={styles.tips}>Enter发送，Ctrl+Enter换行</div>
        <Button
          type="primary"
          onClick={onSendMessage}
          disabled={!inputValue || inputValue?.replaceAll('\n', '')?.trim()?.length <= 0}
        >
          发送
        </Button>
      </div>
    </div>
  );
};
export default connect(({ message }) => ({
  message,
}))(MessageInput);
