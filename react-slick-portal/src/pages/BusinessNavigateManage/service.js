import request from '@/utils/request';

export function queryTableData(props) {
  const { current, pageSize = 10, ...restParams } = props;

  return request('portal/FuncNavigationController/queryPage.do', {
    data: {
      pageNum: current,
      pageSize,
      ...restParams,
    },
  }).then(res => {
    if (Array.isArray(res?.resultObject?.list)) {
      return {
        total: res.resultObject.total,
        data: res.resultObject.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  });
}

export function addNavigate(props) {
  return request('portal/FuncNavigationController/addData.do', {
    data: {
      ...props,
    },
  }).then(res => res?.success === true);
}

export function updateNavigate(props) {
  const { statusCdName, ...rest } = props;
  return request('portal/FuncNavigationController/updateData.do', {
    data: {
      ...rest,
    },
  }).then(res => res?.success === true);
}

export function deleteNavigate(funcNavigationId, menuRelId) {
  return request('portal/FuncNavigationController/deleteData.do', {
    data: {
      funcNavigationId,
      menuRelId,
    },
  }).then(res => res?.success === true);
}

export function getNavigateDetail(funcNavigationId) {
  return request(`portal/FuncNavigationController/selectById.do?funcNavigationId=${funcNavigationId}`, {
    method: 'get',
  }).then(res => res?.resultObject ?? {});
}

//  statusCd 传1000表示取消发布，传1200表示发布
export function releaseOrNot(funcNavigationId, statusCd) {
  return request('portal/FuncNavigationController/updateStatusCd.do', {
    data: {
      funcNavigationId,
      statusCd,
    },
  }).then(res => res?.success === true);
}
