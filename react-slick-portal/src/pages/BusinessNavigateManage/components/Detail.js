import React, { useState, useEffect } from 'react';
import { Drawer, Form, Row, Col, Input, Button, Spin, Modal, Divider, message } from 'antd';
import SlickTable from '@/components/SlickTable';
import { MODES } from '@/hooks/useContact';
import styles from '../styles.less';
import { deleteByIndex, isEmptyArray, isEmptyStr, transformToArray } from '@/utils/utils';
import MenuChoose from './MenuChoose';
import { getNavigateDetail, addNavigate, updateNavigate } from '../service';

const MODAL_TYPE = {
  FUNC: 'func',
  NODE: 'node',
};

const Detail = props => {
  const {
    mode,
    visible,
    close,
    form,
    defaultValue,
  } = props;
  const { getFieldDecorator } = form;
  const disabled = mode === MODES.READ_ONLY;
  const [nodeList, setNodeList] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentNodeIndex, setCurrentNodeIndex] = useState();
  const [detailInfo, setDetailInfo] = useState({});
  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [filterIdList, setFilterIdList] = useState([]);

  const initData = async () => {
    setLoading(true);
    const detail = await getNavigateDetail(defaultValue?.funcNavigationId);
    setDetailInfo(detail);
    setLoading(false);

    setNodeList(transformToArray(detail?.funcNavigationAttr));
  };

  useEffect(() => {
    if (mode !== MODES.ADD) {
      initData();
    }
  }, []);

  const buildTitle = () => {
    switch (mode) {
      case MODES.ADD:
        return '新增-业务导航';
      case MODES.EDIT:
        return '编辑-业务导航';
      default:
        return '业务导航';
    }
  };

  const handleNodeInfoChange = (key, value, index) => {
    const newNode = {
      ...nodeList[index],
      [key]: value,
    };

    setNodeList([
      ...nodeList.slice(0, index),
      newNode,
      ...nodeList.slice(index + 1),
    ]);
  };

  const moveUp = (record, index, nodeIndex, funcList) => {
    const _funcList = [...funcList];
    const newTop = {
      ...record,
      navigationSort: Number(record?.navigationSort) - 1,
    };

    const oldTop = _funcList[index - 1];
    const newBottom = {
      ...oldTop,
      navigationSort: Number(oldTop?.navigationSort) + 1,
    };

    _funcList[index - 1] = newTop;
    _funcList[index] = newBottom;
    handleNodeInfoChange('funcNavigationAttrRel', _funcList, nodeIndex);
  };

  const moveDown = (record, index, nodeIndex, funcList) => {
    const _funcList = [...funcList];

    const newBottom = {
      ...record,
      navigationSort: Number(record?.navigationSort) + 1,
    };

    const oldBottom = _funcList[index + 1];
    const newTop = {
      ...oldBottom,
      navigationSort: Number(oldBottom?.navigationSort) - 1,
    };

    _funcList[index] = newTop;
    _funcList[index + 1] = newBottom;
    handleNodeInfoChange('funcNavigationAttrRel', _funcList, nodeIndex);
  };

  const deleteFunc = (record, index, nodeIndex, funcList) => {
    let _funcList = [...funcList];
    _funcList = deleteByIndex(_funcList, index);

    _funcList = _funcList.map((item, _index) => ({
      ...item,
      navigationSort: _index + 1,
    }));
    handleNodeInfoChange('funcNavigationAttrRel', _funcList, nodeIndex);
  };

  const deleteNode = nodeIndex => {
    let _nodeList = deleteByIndex(nodeList, nodeIndex);

    _nodeList = _nodeList.map((item, _index) => ({
      ...item,
      navigationSort: _index + 1,
    }));

    setNodeList(_nodeList);
  };

  const addFuncs = funcs => {
    const funcList = transformToArray(nodeList[currentNodeIndex]?.funcNavigationAttrRel);
    let navigationSort = Number(funcList.length);

    const newFuncList = funcs.map(item => {
      navigationSort += 1;
      return {
        menuRelId: item.menuId,
        menuRelName: item.menuName,
        navigationSort,
        navigationType: '1100',
      };
    });
    handleNodeInfoChange('funcNavigationAttrRel', [...funcList, ...newFuncList], currentNodeIndex);
  };

  const addNode = () => {
    const newNode = {
      navigationSort: nodeList?.length + 1,
      funcNavigationAttrRel: [],
      navigationType: '1000',
    };

    setNodeList([...nodeList, newNode]);
  };

  const chooseNodeInfo = nodeInfo => {
    const newNode = {
      ...nodeList[currentNodeIndex],
      menuRelId: nodeInfo?.[0]?.menuId,
      menuRelName: nodeInfo?.[0]?.menuName,
    };

    setNodeList([
      ...nodeList.slice(0, currentNodeIndex),
      newNode,
      ...nodeList.slice(currentNodeIndex + 1),
    ]);
  };

  const buildColumns = ({ nodeIndex, funcList }) => {
    const res = [
      {
        title: '顺序',
        dataIndex: 'navigationSort',
        align: 'center',
        width: 100,
      },
      {
        title: '功能名称',
        align: 'center',
        dataIndex: 'menuRelName',
      },
    ];

    if (!disabled) {
      res.push({
        title: '操作',
        width: 140,
        render: (_, record, index) => (
          <div>
            {
              index !== 0 && (
                <span>
                  <a type="link" onClick={() => { moveUp(record, index, nodeIndex, funcList); }}>上移</a>
                  <Divider type="vertical" />
                </span>
              )
            }
            {
              index !== funcList.length - 1 && (
                <span>
                  <a type="link" onClick={() => { moveDown(record, index, nodeIndex, funcList); }}>下移</a>
                  <Divider type="vertical" />
                </span>
              )
            }
            <a type="link" onClick={() => { deleteFunc(record, index, nodeIndex, funcList); }}>删除</a>
          </div>
        ),
      });
    }
    return res;
  };

  const check = () => {
    let res = true;
    if (isEmptyArray(nodeList)) {
      res = false;
      message.warning('请至少添加一个导航步骤');
      return res;
    }
    nodeList.forEach(node => {
      if (isEmptyStr(node.menuRelId)) {
        res = false;
      }
    });
    if (res === false) {
      message.warning('请输入节点名称');
    }
    return res;
  };

  const submit = () => {
    form.validateFields(async err => {
      if (err) {
        return;
      }

      const checkRes = check();
      if (!checkRes) {
        return;
      }

      setSubmitLoading(true);

      const params = {
        ...detailInfo,
        navigationName: form.getFieldValue('navigationName'),
        funcNavigationAttr: nodeList,
      };

      if (mode === MODES.ADD) {
        const res = await addNavigate(params);
        if (res === true) {
          message.success('提交成功');
          close(true);
        } else {
          message.error('提交失败');
        }
      } else if (mode === MODES.EDIT) {
        const res = await updateNavigate(params);
        if (res === true) {
          message.success('修改成功');
          close(true);
        } else {
          message.error('修改失败');
        }
      }
      setSubmitLoading(false);
    });
  };

  return (
    <Drawer
      title={buildTitle()}
      destroyOnClose
      width={620}
      visible={visible}
      onClose={close}
      bodyStyle={{ paddingBottom: 80 }}
    >

      <Form labelCol={{ span: 4 }} wrapperCol={{ span: 16 }}>
        <Row style={{ marginBottom: 10 }}>
          <Col span={24}>
            <Form.Item label="导航名称">
              {getFieldDecorator('navigationName', {
                rules: [
                  {
                    required: true,
                    message: '请输入导航名称',
                  },
                ],
                initialValue: defaultValue?.navigationName,
              })(
                <Input
                  allowClear
                  disabled={disabled}
                />
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row style={{ marginBottom: 18 }}>
          <Col span={4}>
            <span className={styles.label} style={{ marginLeft: disabled && isEmptyArray(nodeList) ? 16 : 0 }}>
              {
                disabled
                  ? `导航步骤： ${isEmptyArray(nodeList) ? '无' : ''}`
                  : '设置导航步骤： '
              }
            </span>
          </Col>
          {
            !disabled && (
              <Button type="primary" size="small" onClick={addNode}>新增导航节点</Button>
            )
          }
        </Row>
        <Spin spinning={loading}>
          {
            transformToArray(nodeList).map((node, index) => (
              <div className={styles.card} key={node.funcNavigationAttrId}>
                <div className={styles.nameRow}>
                  <span className={styles.name}>{node?.navigationSort}、节点名称：</span>
                  {
                    disabled ? node.menuRelName
                      : (
                        <Input
                          className={styles.input}
                          allowClear
                          disabled={disabled}
                          value={node.menuRelName}
                          readOnly
                          onClick={() => {
                            setModalVisible(MODAL_TYPE.NODE);
                            setCurrentNodeIndex(index);
                            setFilterIdList(nodeList.map(item => item?.menuRelId));
                          }}
                          onChange={e => {
                            handleNodeInfoChange('menuRelName', e?.target?.value, index);
                          }}
                        />
                      )
                  }
                  {
                    !disabled && (
                      <Button
                        className={styles.btn}
                        type="danger"
                        onClick={() => {
                          Modal.confirm({
                            title: '是否确定删除该节点？',
                            onOk: () => {
                              deleteNode(index);
                            },
                          });
                        }}
                      >
                        删除节点
                      </Button>
                    )
                  }
                </div>
                <div className={styles.detailRow}>
                  <span>节点提醒文字：</span>
                  {
                    disabled ? node.remark
                      : (
                        <Input
                          className={styles.input}
                          allowClear
                          disabled={disabled}
                          value={node.remark}
                          onChange={e => {
                            handleNodeInfoChange('remark', e?.target?.value, index);
                          }}
                        />
                      )
                  }
                </div>
                <div className={styles.btnAdd}>
                  {
                    !disabled && (
                      <Button
                        type="primary"
                        onClick={() => {
                          setModalVisible(MODAL_TYPE.FUNC);
                          setCurrentNodeIndex(index);
                          setFilterIdList(transformToArray(node?.funcNavigationAttrRel).map(item => item?.menuRelId));
                        }}
                      >
                        增加视图功能
                      </Button>
                    )
                  }
                </div>
                {
                  !isEmptyArray(node?.funcNavigationAttrRel) && (
                    <SlickTable
                      rowKey={record => record.funcNavigationId}
                      columns={buildColumns({
                        funcList: transformToArray(node?.funcNavigationAttrRel),
                        nodeIndex: index,
                      })}
                      pagination={false}
                      data={{
                        list: transformToArray(node?.funcNavigationAttrRel),
                      }}
                    />
                  )
                }
              </div>
            ))
          }
        </Spin>
        <div
          style={{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
          }}
        >
          {
            disabled
              ? <Button onClick={close}>关闭</Button>
              : (
                <div>
                  <Button
                    type="primary"
                    className="margin-right"
                    onClick={submit}
                    loading={submitLoading}
                  >
                    提交
                  </Button>
                  <Button type="default" onClick={close}>
                    取消
                  </Button>
                </div>
              )
          }
        </div>
      </Form>
      {
        modalVisible && (
          <MenuChoose
            visible={modalVisible}
            close={() => { setModalVisible(false); }}
            onOk={selectedRows => {
              if (modalVisible === MODAL_TYPE.FUNC) {
                addFuncs(transformToArray(selectedRows));
              } else {
                chooseNodeInfo(transformToArray(selectedRows));
              }
              setModalVisible(false);
            }}
            MODAL_TYPE={MODAL_TYPE}
            filterIdList={filterIdList}
          />
        )
      }

    </Drawer>
  );
};

export default Form.create()(Detail);
