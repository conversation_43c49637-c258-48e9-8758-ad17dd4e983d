import React, { useState } from 'react';
import { connect } from 'dva';
import { Modal, Input, message } from 'antd';
import SlickTable from '@/components/SlickTable';
import useGetMenu from '@/hooks/useGetMenu';
import { isEmptyArray } from '@/utils/utils';


const MenuChoose = props => {
  const { visible, close, onOk, MODAL_TYPE, filterIdList, menu } = props;
  const { search, subMenuList } = useGetMenu(filterIdList, menu);
  const [selectedRows, setSelectedRows] = useState([]);

  const columns = [
    {
      dataIndex: 'menuName',
      title: '菜单名称',
      ellipsis: true,
      width: 150,
    },
    {
      dataIndex: 'parMenuName',
      title: '上级菜单',
      ellipsis: true,
      width: 150,
    },
    {
      dataIndex: 'menuDesc',
      title: '菜单描述',
      ellipsis: true,
    },
  ];

  const submit = () => {
    if (isEmptyArray(selectedRows)) {
      message.warning('请选择数据');
      return;
    }
    onOk(selectedRows);
  };

  return (
    <Modal
      title="选择菜单"
      width={800}
      visible={visible}
      onCancel={close}
      onOk={submit}
    >
      <div style={{ width: 250, marginBottom: 8 }}>
        <Input.Search
          allowClear
          placeholder="搜索关键字"
          onSearch={search}
        />
      </div>
      <SlickTable
        rowKey={record => record.menuId}
        pick={visible === MODAL_TYPE.FUNC ? 'checkbox' : 'radio'}
        onSelectRow={_selectedRows => {
          setSelectedRows(_selectedRows);
        }}
        columns={columns}
        data={{
          list: subMenuList,
        }}
      />
    </Modal>
  );
};

export default connect(({ menu }) => ({
  menu,
}))(MenuChoose);
