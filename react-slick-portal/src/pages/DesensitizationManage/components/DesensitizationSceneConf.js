import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Input,
  Button,
  message,
  Select,
  Spin,
} from 'antd';
import { addSceneConf, editSceneConf } from '../service';

const { Option } = Select;
const FormItem = Form.Item;
const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const textAreaFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};

// 判断空对象
const judgeObj = obj => {
  if (Object.keys(obj).length === 0) return true;
  return false;
};

const namespace = 'desensitizationManage';

function DesensitizationSceneConf(props) {
  const {
    form: { getFieldDecorator },
  } = props;

  const [loading, setLoading] = useState(false);

  const saveBehavior = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveBehavior`,
      payload: params,
    });
  };

  const updateRefreshTag = () => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/updateRefreshTag`,
    });
  };

  const saveTableData = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveTableData`,
      payload: params,
    });
  };

  useEffect(() => {
  }, []);

  useEffect(() => {
    if (props.behavior === 'add') {
      props.form.resetFields();
    }
  }, [props.behavior]);

  const addDesensitizationScene = async data => {
    const res = await addSceneConf(data);
    if (res && res.success) {
      setLoading(false);
      saveBehavior('disabled');
      message.success('新增成功');
      updateRefreshTag();
      saveTableData({ ...props.tableData, ...data });
      return;
    }
    message.error(res.resultMsg);
    setLoading(false);
  };

  const editDesensitizationScene = async data => {
    const res = await editSceneConf(data);
    if (res && res.success) {
      setLoading(false);
      saveBehavior('disabled');
      message.success('修改成功');
      updateRefreshTag();
      saveTableData({ ...props.tableData, ...data });
      return;
    }
    message.error(res.resultMsg);
    setLoading(false);
  };

  const handleKeep = () => {
    props.form.validateFields((err, fieldsValue) => {
      if (err) return;
      setLoading(true);
      if (props.behavior === 'edit') {
        editDesensitizationScene({ ...fieldsValue, id: props.tableData.id });
      } else {
        addDesensitizationScene(fieldsValue);
      }
    });
  };

  const handleCancel = () => {
    saveBehavior('disabled');
  };

  return (
    <Spin spinning={loading}>
      <Form>
        <Row>
          <Col span={6}>
            <FormItem label="场景名称" {...formItemLayout}>
              {getFieldDecorator('sceneName', {
                rules: [
                  {
                    required: true,
                    message: '场景名称不能为空',
                  },
                ],
              })(
                <Input
                  placeholder="请输入"
                  disabled={props.behavior === 'disabled'}
                />
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="场景编码" {...formItemLayout}>
              {getFieldDecorator('sceneCode', {
                rules: [
                  {
                    required: true,
                    message: '场景编码不能为空',
                  },
                ],
              })(
                <Input
                  placeholder="请输入"
                  disabled={props.behavior === 'disabled'}
                />
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="归属系统" {...formItemLayout}>
              {getFieldDecorator('systemNbr', {
                rules: [
                  {
                    required: true,
                    message: '归属系统不能为空',
                  },
                ],
              })(
                <Select
                  placeholder="请选择"
                  disabled={props.behavior === 'disabled'}
                >
                  {props.sysCode.length
                    ? props.sysCode.map(item => (
                      <Option value={`${item.systemNbr}`} key={item.id}>
                        {item.systemName}
                      </Option>
                    ))
                    : null}
                </Select>
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="状态" {...formItemLayout}>
              {getFieldDecorator('statusCd')(
                <Select
                  placeholder="请选择"
                  disabled={props.behavior === 'disabled'}
                >
                  <Select.Option value="1000" key="1000">
                    有效
                  </Select.Option>
                  <Select.Option value="1100" key="1100">
                    无效
                  </Select.Option>
                </Select>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span={12}>
            <FormItem label="描述" {...textAreaFormItemLayout}>
              {getFieldDecorator('sceneDesc')(
                <TextArea
                  rows={4}
                  placeholder="请输入"
                  disabled={props.behavior === 'disabled'}
                />
              )}
            </FormItem>
          </Col>
        </Row>
        {props.behavior !== 'disabled' ? (
          <div style={{ textAlign: 'center' }}>
            <Button type="primary" className="margin-right" onClick={handleKeep}>
              保存
            </Button>
            <Button type="default" onClick={handleCancel}>
              取消
            </Button>
          </div>
        ) : null}
      </Form>
    </Spin>
  );
}

export default connect(({ desensitizationManage }) => ({
  sysCode: desensitizationManage.sysCode,
  sceneId: desensitizationManage.sceneId,
  behavior: desensitizationManage.behavior,
  tableData: desensitizationManage.tableData,
}))(
  Form.create({
    mapPropsToFields(props) {
      if (!judgeObj(props.tableData)) {
        if (props.behavior !== 'create') {
          return {
            sceneName: Form.createFormField({ value: props.tableData.sceneName }),
            sceneCode: Form.createFormField({ value: props.tableData.sceneCode }),
            systemNbr: Form.createFormField({ value: props.tableData.systemNbr }),
            statusCd: Form.createFormField({ value: props.tableData.statusCd }),
            sceneDesc: Form.createFormField({ value: props.tableData.sceneDesc }),
          };
        }
      }
      return {};
    },
  })(DesensitizationSceneConf)
);
