import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Form, Row, Select, message, Input, Button, Modal, Cascader } from 'antd';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { buildColumns } from '@/utils/wrapColumns';
import { ruleConfPage, addRuleConf, updateRuleConf, deleteRuleConfByIds, queryAllRuleCode } from '../service';
import styles from '../styles.less';

const { Search } = Input;

const judgeObj = obj => {
  if (Object.keys(obj).length === 0) return true;
  return false;
};

const Index = props => {
  const { form } = props ?? {};
  const { getFieldDecorator } = form;
  const [visible, setVisible] = useState(false);
  const [mode, setMode] = useState('add');
  const [sensitiveType, setSensitiveType] = useState([]);
  const [confoundType, setConfoundType] = useState([]);
  const [encryptionType, setEncryptionType] = useState([]);
  const [cascaderOptions, setCascaderOptions] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchVal, setSearchVal] = useState(null);
  const [cascaderLabel, setCascaderLabel] = useState(false);
  const [currentRecord, setCurrentRecord] = useState({});
  const [selectRows, setSelectRows] = useState([]);


  const { tableProps, search: { submit } } = useAntdTable(
    params => {
      if (!judgeObj(props.tableData)) {
        return ruleConfPage({
          pageNum: params.current,
          pageSize: 5,
          entity: { sceneId: props.tableData.id, effectiveField: searchVal } });
      }
      return Promise.resolve([]);
    },
    {
      form,
      // 手动触发请求
      manual: true,
    },
  );
  const { pagination, ...restTableProps } = tableProps;

  const onSearch = value => {
    setSearchVal(value !== '' ? value : null);
    submit();
  };

  useEffect(() => {
    if (!judgeObj(props.tableData)) {
      // 手动触发请求
      submit();
    }
    setSelectRows([]);
  }, [props.tableData]);

  const handleSelectRows = value => {
    setSelectRows(value);
  };

  const handleAdd = () => {
    setCurrentRecord({});
    setMode('add');
    setVisible(true);
  };

  const resetCascaderOptions = value => {
    if (value === '1000') {
      setCascaderOptions(sensitiveType);
    }
    if (value === '1100') {
      setCascaderOptions(confoundType);
    }
    if (value === '1200') {
      setCascaderOptions(encryptionType);
    }
  };

  const ruleTypeChange = value => {
    resetCascaderOptions(value);
    if (currentRecord.ruleType !== value) {
      setCurrentRecord({ ...currentRecord, rulePath: null });
    }
  };

  const handleEdit = record => {
    setCurrentRecord(record);
    resetCascaderOptions(record.ruleType);
    setCascaderLabel(record.ruleDesc);
    setMode('edit');
    setVisible(true);
  };

  const handleDelete = async () => {
    const ids = selectRows.map(item => ({ id: item.id }));
    setLoading(true);
    const res = await deleteRuleConfByIds(ids);
    setLoading(false);
    if (res && res.success) {
      message.success('删除成功');
      submit();
      setSelectRows([]);
      return;
    }
     message.error(res.resultMsg);
  };

  const handleModalCancel = () => {
    setVisible(false);
  };

  const handleModalOk = () => {
    if (judgeObj(props.tableData)) {
      message.error('请选择脱敏场景');
      return;
    }
    form.validateFields(async (err, fieldsValue) => {
      if (err) {
        return;
      }
      const { effectiveField, ruleType, statusCd } = fieldsValue;
      if (mode === 'add') {
        setConfirmLoading(true);
        const res = await addRuleConf({
          effectiveField,
          ruleType,
          statusCd,
          sceneId: props.tableData.id,
          rulePath: fieldsValue.ruleCode.join(','),
          ruleCode: fieldsValue.ruleCode[fieldsValue.ruleCode.length - 1],
          ruleDesc: cascaderLabel,
        });
        setConfirmLoading(false);
        if (res && res.success) {
          setVisible(false);
          message.success('新增成功');
          submit();
          return;
        }
        message.error(res.resultMsg);
      } else if (mode === 'edit') {
        setConfirmLoading(true);
        const res = await updateRuleConf({
          id: currentRecord.id,
          effectiveField,
          ruleType,
          statusCd,
          sceneId: props.tableData.id,
          rulePath: fieldsValue.ruleCode.join(','),
          ruleCode: fieldsValue.ruleCode[fieldsValue.ruleCode.length - 1],
          ruleDesc: cascaderLabel,
        });
        setConfirmLoading(false);
        if (res && res.success) {
          setVisible(false);
          message.success('修改成功');
          submit();
          return;
        }
        message.error(res.resultMsg);
      }
    });
  };

  const columns = buildColumns([
    {
      title: '脱敏场景',
      dataIndex: 'sceneId',
      render: () => props.tableData.sceneName,
    },
    {
      title: '脱敏字段',
      dataIndex: 'effectiveField',
    },
    {
      title: '脱敏方式',
      dataIndex: 'ruleType',
      render: (text, record) => {
        if (record.ruleType === '1000') {
          return '模糊化';
        }
        if (record.ruleType === '1100') {
          return '混淆';
        }
        if (record.ruleType === '1200') {
          return '加密';
        }
        return '';
      },
    },
    {
      title: '脱敏规则',
      dataIndex: 'ruleDesc',
    },
    {
      title: '状态',
      dataIndex: 'statusCd',
      render: (text, record) => {
        if (record.statusCd === '1000') {
          // 有效
          return <span className="text-success">有效</span>;
        }
        // 失效
        return <span className="text-danger">无效</span>;
      },
    },
    {
      title: '操作',
      width: 100,
      fixed: 'right',
      render: (text, record) => (
        <a
          onClick={() => {
            handleEdit(record);
          }}
        >
          编辑
        </a>
      ),
    },
  ]);

  const queryRuleCodeOptions = async code => {
    const res = await queryAllRuleCode(code);
    if (res && res.success) {
      return res.resultObject;
    }
    return [];
  };

  const initData = async () => {
    const tmp = await queryRuleCodeOptions('SensitiveType');
    setSensitiveType(tmp);
    setConfoundType(await queryRuleCodeOptions('ConfoundType'));
    setEncryptionType(await queryRuleCodeOptions('EncryptionType'));
    setCascaderOptions(tmp);
  };

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
  }, [currentRecord]);

  const handleCascaderChange = (value, selectedOptions) => {
    const selectedLabels = selectedOptions.map(option => option.label);
    setCascaderLabel(selectedLabels.join('/'));
  };

  return (
    <div>
      <div className={styles.systemExtra}>
        <div>
          <Search
            style={{ width: '200px' }}
            placeholder="脱敏字段"
            onSearch={onSearch}
          />
        </div>
        <div>
          <Button type="primary" onClick={handleAdd}>
            新增
          </Button>
        </div>
      </div>
      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        pick="checkbox"
        loading={loading || restTableProps?.loading}
        {...restTableProps}
        data={{
          pagination: {
            ...pagination,
            pageSize: 5,
          },
        }}
        scroll={{ x: 'max-content' }}
        extra={
          selectRows.length > 0 ? (
            <Button type="danger" ghost onClick={handleDelete}>
              删除
            </Button>
          ) : null
        }
        onSelectRow={handleSelectRows}
        selectedRows={selectRows}
      />
      {
        visible && (
          <Modal
            title={mode === 'add' ? '新增脱敏规则' : '编辑脱敏规则'}
            visible={visible}
            onOk={handleModalOk}
            onCancel={handleModalCancel}
            width="500px"
            destroyOnClose
            confirmLoading={confirmLoading}
          >
            <Form labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Row>
                <Form.Item label="脱敏场景">
                  <Input readOnly disabled defaultValue={props.tableData.sceneName} />
                </Form.Item>
              </Row>
              <Row>
                <Form.Item label="脱敏字段">
                  {getFieldDecorator('effectiveField', {
                    initialValue: currentRecord.effectiveField ? currentRecord.effectiveField : '',
                    rules: [{ required: true, message: '请输入脱敏字段' }],
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
              </Row>
              <Row>
                <Form.Item label="脱敏方式">
                  {getFieldDecorator('ruleType', {
                    initialValue: currentRecord.ruleType ? currentRecord.ruleType : '1000',
                    rules: [
                      {
                        required: true,
                        message: '请选择脱敏方式',
                      },
                    ],
                  })(
                    <Select
                      style={{ width: '100%' }}
                      allowClear
                      placeholder="请选择"
                      onChange={ruleTypeChange}
                    >
                      <Select.Option key="1000" value="1000">模糊化</Select.Option>
                      <Select.Option key="1100" value="1100">混淆</Select.Option>
                      <Select.Option key="1200" value="1200">加密</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Row>
              <Row>
                <Form.Item label="脱敏规则">
                  {getFieldDecorator('ruleCode', {
                    initialValue: currentRecord.rulePath ? currentRecord.rulePath.split(',') : [],
                    rules: [
                      {
                        required: true,
                        message: '请选择脱敏规则',
                      },
                    ],
                  })(
                    <Cascader
                      options={cascaderOptions}
                      onChange={handleCascaderChange}
                    />
                  )}
                </Form.Item>
              </Row>
              <Row>
                <Form.Item label="状态" value={currentRecord.statusCd}>
                  {getFieldDecorator('statusCd', {
                    initialValue: currentRecord.statusCd ? currentRecord.statusCd : '1000',
                    rules: [{ required: true, message: '请选择状态' }],
                  })(
                    <Select placeholder="请选择">
                      <Select.Option value="1000" key="1000">
                        有效
                      </Select.Option>
                      <Select.Option value="1100" key="1100">
                        无效
                      </Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Row>
            </Form>
          </Modal>
        )
      }
    </div>
  );
};

export default connect(({ desensitizationManage }) => ({
  tableData: desensitizationManage.tableData,
}))(Form.create()(Index));
