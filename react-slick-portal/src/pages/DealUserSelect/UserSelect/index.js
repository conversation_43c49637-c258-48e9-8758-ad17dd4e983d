import React, { useState, useEffect } from 'react';
import { Card, Button, Modal, message, Divider, Row, Col, Form, Icon } from 'antd';
import styles from '../styles.less';
import UserSelectTable from '../components/UserSelectTable';
import { sendUserInfoToParent, closeUserSelect } from '../components/userSelectServices';
const { info } = Modal;

function UserSelect(props) {
  const [params, setParams] = useState(null);
  const [roleParams, setRoleParams] = useState(null);

  const [selectedRow, setSelectedRow] = useState(null); // 保存选中用户id
  const [isLowCode, setIsLowCode] = useState(false);

  // 请求地址: http://localhost:8000/#/dealUserSelect/UserSelect?roleId=10002&regionId=530000&systemNbr=RISK&&labels=PROD001,VIP001
  // workbenchFlag 为非必填;workbenchFlag 为非必填；regionId、roleId、labels三个非必填，但是必须要有一个
  useEffect(() => {
    let query = props.location.query;
    let newParams = {
      regionId: query.regionId,
      roleId: query.roleId,
      labels: query.labels?.split(',') ?? [],
      systemNbr: query.systemNbr,
    };
    setParams(newParams);
    setIsLowCode(query.lowCode === '1');
    setRoleParams({
      systemNbr: query.systemNbr,
      workbenchFlag: query.workbenchFlag ? query.workbenchFlag : 0, // 查询角色的时候，默认查出门户角色
    });
  }, [props.location]);

  const onConfirm = () => {
    if (!selectedRow) {
      info({ title: '请选择处理人' });
      return;
    }
    sendUserInfoToParent(selectedRow, isLowCode);
  };

  const onCancel = () => {
    closeUserSelect(isLowCode);
  };

  return (
    <Card
      title={isLowCode ? '' : '处理人选择'}
      className={styles['deal-user-card']}
      extra={!isLowCode && <Icon type="close" className={styles['close-icon']} onClick={onCancel} />}
    >
      <div className={styles.main}>
        <UserSelectTable queryParams={params} roleParams={roleParams} onRowClick={record => setSelectedRow(record)} />
      </div>
      <Divider className={styles['bottom-divider']} />
      <Row>
        <Col offset={16} span={8} className={styles['bottom-area']}>
          <Button type="default" style={{ marginRight: '8px' }} onClick={onCancel}>
            取消
          </Button>
          <Button type="primary" onClick={onConfirm}>
            确认
          </Button>
        </Col>
      </Row>
    </Card>
  );
}

export default Form.create()(UserSelect);
