import React, { useState, useEffect } from 'react';
import { Card, Input, Button, Modal, Divider, Tree, Row, Col, Form, Icon, Spin } from 'antd';
import request from '@/utils/request';
import styles from '../styles.less';
import UserSelectTable from '../components/UserSelectTable';
import { sendUserInfoToParent, closeUserSelect, filterTree, buildTree } from '../components/userSelectServices';

const { Search } = Input;
const { info } = Modal;

function AreaUserSelect(props) {
  const [params, setParams] = useState(null);
  const [roleParams, setRoleParams] = useState({});
  const [paramsOpt, setParamsOpt] = useState({}); // 根据参数是否显示、只读对应的筛选项

  const [selectedRow, setSelectedRow] = useState(null); // 保存选中用户id

  const [treeData, setTreeData] = useState([]);
  const [allTreeData, setAllTreeData] = useState([]);
  const [treeExpandedKeys, setTreeExpandedKeys] = useState([]);
  const [curRegionId, setCurRegionId] = useState(null);
  const [treeLoading, setTreeLoading] = useState(true);

  const [scrollHeight, setScrollHeight] = useState(400);
  const [isLowCode, setIsLowCode] = useState(false);

  useState(() => {
    setScrollHeight(window.document.body.scrollHeight - 200);
  }, []);

  // 请求地址: http://localhost:8000/#/dealUserSelect/AreaUserSelect?roleId=1686032459131237&regionId=8530100&labels=PROD001,VIP001&systemNbr=RISK&viewMode=inner
  // workbenchFlag 为非必填；regionId、roleId、labels三个非必填，但是必须要有一个
  useEffect(() => {
    const { query } = props.location;
    const newParams = {
      regionId: query.regionId,
      systemNbr: query.systemNbr,
      parRegionId: query.parRegionId,
    };

    // 获取各参数显示、只读规则
    const optObj = {
      role: { isShow: true, isReadonly: false },
      labels: { isShow: true, isReadonly: false },
      // impOrgFlag: { isShow: true, isReadonly: false },, impOrgFlag
    };
    const { roleId, labels } = query;
    if (roleId) {
      const roleArr = roleId.split('*');
      newParams.roleId = roleArr[0] || undefined;
      if (roleArr[1] === '0') {
        optObj.role = { ...optObj.role, isShow: false, isReadonly: true };
      } else if (roleArr[2] === '1') {
        optObj.role = { ...optObj.role, isReadonly: true };
      }
    }
    if (labels) {
      const labelsArr = labels.split('*');
      newParams.labels = labelsArr[0] ? labelsArr[0].split(',') : [];
      if (labelsArr[1] === '0') {
        optObj.labels = { ...optObj.labels, isShow: false, isReadonly: true };
      } else if (labelsArr[2] === '1') {
        optObj.labels = { ...optObj.labels, isReadonly: true };
      }
    }
    // if (impOrgFlag) {
    //   const impOrgFlagArr = impOrgFlag.split('*');
    //   newParams.impOrgFlag = impOrgFlagArr[0] || undefined;
    //   if (impOrgFlagArr[1] === '0') {
    //     optObj.impOrgFlag = { ...optObj.impOrgFlag, isShow: false, isReadonly: true };
    //   } else if (impOrgFlagArr[2] === '0') {
    //     optObj.impOrgFlag = { ...optObj.impOrgFlag, isReadonly: true };
    //   }
    // }
    setParamsOpt(optObj);

    // 区域初始化设值
    if (newParams.regionId) {
      setCurRegionId([newParams.regionId]);
    } else {
      setCurRegionId([]);
    }
    setParams(newParams);
    setIsLowCode(query.lowCode === '1');
    setRoleParams({
      systemNbr: query.systemNbr,
      workbenchFlag: query.workbenchFlag ? query.workbenchFlag : 0, // 查询角色的时候，默认查出门户角色
    });

    initTree(newParams.regionId, newParams.parRegionId);
  }, [props.location]);

  const initTree = (regionId, parRegionId) => {
    setTreeLoading(true);
    request('orgauth/CommonRegionController/getCommonRegionTree.do', {
      data: { parRegionId: -1 },
      complete: () => setTreeLoading(false),
    }).then(res => {
      let treeData;
      if (parRegionId) {
        treeData =
          res.resultObject?.length > 0 ? res.resultObject[0].children?.filter(regionTree => `${regionTree.commonRegionId}` === parRegionId) : null;
      } else {
        treeData = res.resultObject;
      }
      let expandedKeys = [];
      buildTree(treeData, expandedKeys, regionId, 'id', 'regionName', 'parRegionId');
      setTreeData(treeData);
      setAllTreeData(treeData);
      setTreeExpandedKeys(expandedKeys);
    });
  };

  const onSearchTree = val => {
    const newTreeData = [];
    const expandedKeys = [];
    filterTree(allTreeData, val, newTreeData, expandedKeys);
    setTreeData(newTreeData);
    setTreeExpandedKeys(expandedKeys);
  };

  const onTreeSelect = selectedKeys => {
    setCurRegionId(selectedKeys);
    setParams({
      ...params,
      regionId: selectedKeys[0],
    });
  };

  const onExpandTree = expandedKeys => {
    setTreeExpandedKeys(expandedKeys);
  };

  const onConfirm = () => {
    if (!selectedRow) {
      info({ title: '请选择处理人' });
      return;
    }
    sendUserInfoToParent(selectedRow, isLowCode);
  };

  const onCancel = () => {
    closeUserSelect(isLowCode);
  };

  return (
    <Card
      title={isLowCode ? '' : '处理人选择'}
      className={styles['deal-user-card']}
      extra={!isLowCode && <Icon type="close" className={styles['close-icon']} onClick={onCancel} />}
    >
      <div className={styles.main}>
        <div className={styles.left}>
          <Spin spinning={treeLoading}>
            <div className={styles.title}>区域一览</div>
            <Search placeholder="请输入关键字搜索" onSearch={onSearchTree} className={styles.search} />
            <div className={styles['left-tree']} style={{ height: scrollHeight }}>
              <Tree onSelect={onTreeSelect} selectedKeys={curRegionId} onExpand={onExpandTree} expandedKeys={treeExpandedKeys} treeData={treeData} />
            </div>
          </Spin>
        </div>
        <div className={styles.right}>
          <UserSelectTable queryParams={params} roleParams={roleParams} paramsOpt={paramsOpt} onRowClick={record => setSelectedRow(record)} />
        </div>
      </div>
      <Divider className={styles['bottom-divider']} />
      <Row>
        <Col offset={16} span={8} className={styles['bottom-area']}>
          <Button type="default" style={{ marginRight: '8px' }} onClick={onCancel}>
            取消
          </Button>
          <Button type="primary" onClick={onConfirm}>
            确认
          </Button>
        </Col>
      </Row>
    </Card>
  );
}

export default Form.create()(AreaUserSelect);
