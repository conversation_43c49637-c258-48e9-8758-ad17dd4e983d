.wrap {
  display: flex;
  justify-content: center;
  width: 100%;
  min-width: 1200px;

  .innerWrap {
    position: relative;
    width: calc(100% - 60px);
    min-width: 1200px;
    max-width: 1860px;
    margin: 4px 0 92px 0;

    .openBtn{
      position: absolute;
      top: 16px;
      right: 30px;
      z-index: 10;
    }
  }
}

.searchRow {
  margin-left: 33%;

  .searchInput {
    // width: 40%;
    vertical-align: top;
    :global {
      .ant-input {
        // width: 100%;
        height: 36px;
        font-size: 14px;
      }
    }
  }

  .searchButton {
    height: 36px;
    font-size: 14px;
    vertical-align: top;
    :global {
      .ant-btn {
        height: 40px;
        font-size: 14px;
      }
    }

    .icon {
      font-size: 13px;
    }
  }

  .typeRadio {
    margin-bottom: 10px;
    margin-left: 5px;
  }

}

.bannerFlex {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  width: 100%;

  .bannerWrap {
    width: 66.94235%;
    max-width: 1370px;
  }

  .smallWrap {
    width: 100%;

    .banner {
      padding-bottom: 27.46715%;
      background-image: url(../../../img/homeBanner.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  .rightInfo {
    width: 32.213%;
    // max-width: 460px;

    .smallTitle {
      color: #999;
      font-weight: 400;
      font-size: 14px;
    }

    .top {
      height: 60%;
      background-color: #F6F6F6;
      border: 1px solid #eee;
      .cardContent {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: 100%;

        .title {
          display: flex;
          align-items: center;
          margin-top: 15px;

          img {
            margin-right: 13px;
            margin-left: 35px;
            border: 5px solid white;
            border-radius: 50%;
          }

          .text {

            .name {
              margin-bottom: 2px;
              color: #272727;
              font-weight: 600;
              font-size: 16px;
            }

            .code {
              color: #999;
              font-weight: 400;
              font-size: 16px;
            }
          }
        }

        .contentFlex {
          display: flex;
          justify-content: space-around;
          font-size: 18px;

          .textBlock {
            text-align: center;

            .count {
              color: #272727;
              font-weight: 600;
              font-size: 20px;
              cursor: pointer;
            }
          }
        }
      }
    }

    .bottom {
      height: calc(40% - 18px);
      margin-top: 14px;
      margin-bottom: 4px;
      background-color: #F6F6F6;
      border: 1px solid #eee;
      border-top: 0;

      .contentFlex {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 100%;

        .row {
          text-align: center;

          .count {
            color: #272727;
            font-weight: 600;
            font-size: 20px;
          }
        }
      }
    }

  }
}

.homeTabs {
  margin-top: 5px;
  margin-bottom: 0;

  :global {
    .ant-tabs-tab {
      margin-right: 56px;
      margin-left: 56px;
      font-weight: 600;
      font-size: 16px;
      line-height: 28px;
      &:hover {
        color: #146FFE;
      }
    }

    .ant-tabs-tab-active {
      color: #146FFE;
      &:hover {
        color: #146FFE;
      }
    }

    .ant-tabs-ink-bar {
      background-color: #146FFE;
    }
  }
}
