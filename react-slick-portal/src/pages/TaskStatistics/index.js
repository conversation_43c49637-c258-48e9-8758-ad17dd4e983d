import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, Row, Col, Select, Icon, Input, Button, Tag, Modal,Divider  } from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
import AdminTaskStatistics from './AdminTaskStatistics';
import PersonalTaskStatistics from './PersonalTaskStatistics';
import { getItem } from '@/utils/utils';
function taskStatistics(props) {
  const[isManage,setIsmanage ] = useState(false);
  useEffect(() => {
    const {portalRoles ,userInfo} = getItem('user');
    // console.log(getItem('user'));
    let roles  = portalRoles;
    for (let i = 0; i < roles.length; i++) {
      if(roles[i].sysRoleId == userInfo.roleId){
        if(roles[i].sysRoleType ==  "1100"){
          setIsmanage(true);
          break;
        }
    }
    }
  },[])
  return (
      <Card
        style={{ minHeight: props.size.height }}
        bordered
        title="任务统计分析"
      >
          {isManage?<AdminTaskStatistics {...props} />:<PersonalTaskStatistics {...props} />}
      </Card>
  );
}

export default connect( ({ setting }) => ({
  size: setting.size,
}) )(Form.create()(taskStatistics));
