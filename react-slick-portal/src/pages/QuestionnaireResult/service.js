import request from '@/utils/request';

/**
 * 调查问卷标题列表
 * @param {*} params
 * @returns
 */
export async function getTitleList() {
  return request('portal/QuestionnaireInfoController/getTitleList.do', {
    method: 'get',
  });
}

/**
 * 调查问卷图表统计
 * @returns
 */
export async function getStatistics(title) {
  return request('portal/QuestionnaireInfoController/getStatistics.do', {
    method: 'post',
    data: {
      questionnaireTitle: title,
    },
  });
}

/**
 * 调查问卷列表数据
 * @returns
 */
export async function getQuestionnaireGridData(params) {
  return request('portal/QuestionnaireInfoController/selectQuestionnaireGridData.do', {
    method: 'post',
    data: params,
  });
}
