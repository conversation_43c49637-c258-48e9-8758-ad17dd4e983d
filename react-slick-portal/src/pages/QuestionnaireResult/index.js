import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Card, Form, Row, Col, Button, Select, Divider, message, Spin } from 'antd';
import { Chart, Coord, Tooltip, <PERSON>eom, Legend, Label, Axis } from 'bizcharts';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight } from '@/utils/utils';
import { getTitleList, getStatistics, getQuestionnaireGridData } from './service';

const { Option } = Select;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const QuestionnaireResult = ({ size: { height }, form }) => {
  const { getFieldDecorator } = form;
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const [loading, setLoading] = useState(false);
  const [curTitle, setCurTitle] = useState('');
  const [titleList, setTitleList] = useState([]);
  const [pieData, setPieData] = useState([]);

  // 获取图表数据
  const getPieData = async title => {
    try {
      const result = await getStatistics(title);
      if (result?.success) {
        setPieData(result.resultObject);
      }
    } catch (error) {
      message.error(error);
    }
  };

  // 获取标题列表
  const getTitleData = async () => {
    try {
      setLoading(true);
      const result = await getTitleList();
      if (result?.success) {
        setTitleList(result.resultObject);
        if (result.resultObject.length > 0) {
          await getPieData(result.resultObject[0]);
          form.setFieldsValue({ questionnaireTitle: result.resultObject[0] });
          setCurTitle(result.resultObject[0]);
        }
      }
      setLoading(false);
    } catch (error) {
      message.error(error);
    }
  };

  // 获取列表数据
  const getTableData = async ({ current, pageSize }) => {
    if (curTitle) {
      const result = await getQuestionnaireGridData({
        page: current,
        pageNum: current,
        pageSize,
        rowNum: pageSize,
        sortName: '',
        sortOrder: 'asc',
        questionnaireTitle: curTitle,
      });
      if (Array.isArray(result.list)) {
        return {
          total: result.total,
          data: result.list,
        };
      }
      return {
        total: 0,
        data: [],
      };
    }
    return {
      total: 0,
      data: [],
    };
  };

  useEffect(() => {
    getTitleData();
  }, []);

  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  const columns = [
    {
      title: '标题',
      dataIndex: 'questionnaireTitle',
      ellipsis: true,
      align: 'center',
      width: '20%',
    },
    {
      title: '类型',
      dataIndex: 'questionnaireType',
      align: 'center',
    },
    {
      title: '填报人姓名',
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '填报人账号',
      dataIndex: 'accountNum',
      align: 'center',
    },
    {
      title: '填报人手机号',
      dataIndex: 'phoneNum',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: '填报时间',
      dataIndex: 'createDate',
      align: 'center',
      width: 160,
    },
    {
      title: '结果',
      dataIndex: 'result',
      align: 'center',
    },
    {
      title: '意见',
      dataIndex: 'opinion',
      align: 'center',
      ellipsis: true,
    },
  ];

  const {
    tableProps,
    search: { submit },
  } = useAntdTable(
    params => getTableData({ ...params, pageSize: size }),
    // 依赖变化会触发请求
    [curTitle, height],
    {
      defaultPageSize: size,
      form,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  const handleSubmit = e => {
    e.preventDefault();
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      getPieData(fieldsValue.questionnaireTitle);
      setCurTitle(fieldsValue.questionnaireTitle);
      submit();
    });
  };

  return (
    <Spin spinning={loading}>
      <Card
        title="调查问卷结果列表"
        style={{ minHeight: height }}
        bordered
      >
        <div>
          <Form onSubmit={handleSubmit}>
            <Row gutter={16} type="flex" justify="start">
              <Col span={6}>
                <Form.Item label="问卷标题" {...formItemLayout}>
                  {getFieldDecorator('questionnaireTitle')(
                    <Select
                      showSearch
                      placeholder="请选择"
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {
                        titleList.length ? (
                          titleList.map(item => (
                            <Option key={item} value={item}>{item}</Option>
                          ))
                        ) : null
                      }
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={18}>
                <Button type="primary" htmlType="submit">
                  查询
                </Button>
              </Col>
            </Row>
          </Form>
          <Divider />
          <Row gutter={16}>
            <Col span={6}>
              <div>
                <div
                  style={{
                    textAlign: 'center',
                    fontSize: '16px',
                    fontWeight: '600',
                    margin: '16px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    cursor: 'default',
                  }}
                  title={curTitle}
                >
                  {curTitle}
                </div>
                <Chart height={400} data={pieData} padding="auto" forceFit>
                  <Coord type="theta" radius={0.65} innerRadius={0.58} />
                  <Axis name="resultCount" />
                  <Tooltip />
                  <Legend marker="square" />
                  <Geom
                    type="intervalStack"
                    position="resultCount"
                    color="result"
                    style={{
                      lineWidth: 1,
                      stroke: '#fff',
                    }}
                    tooltip={['result*resultCount*resultRate', (result, resultCount, resultRate) => ({
                        title: '满意度',
                        name: result,
                        value: `${resultCount} ( ${(Number(resultRate) * 100).toFixed(2)}% )`,
                    })]}
                  >
                    <Label
                      content="resultRate"
                      formatter={(val, item) => `${item.point.result}: ${(Number(val) * 100).toFixed(2)}%`}
                    />
                  </Geom>
                </Chart>
              </div>
            </Col>
            <Col span={18}>
              <SlickTable
                rowKey={record => record.id}
                data={{
                  pagination: {
                    ...pagination,
                    pageSize: size,
                  },
                }}
                {...restTableProps}
                columns={columns}
              />
            </Col>
          </Row>
        </div>
      </Card>
    </Spin>
  );
};
export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(QuestionnaireResult));
