import React, { useState, useEffect } from 'react';
import { Card, Form, Row, Col, Input, Button, Modal, Divider, message, Drawer } from 'antd';
import { connect } from 'dva';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight } from '@/utils/utils';
import Edit from './components/Edit';
import { list, removeById } from './service';

const judgeObj = obj => Object.keys(obj).length === 0;

function WatermarkManage({ size: { height }, form }) {
  const [size, setSize] = useState(getPageSizeByCardHeight(height - 64 - 8));
  const { getFieldDecorator } = form;

  const [paramsObj, setParamsObj] = useState({});
  const [visible, setVisible] = useState(false);
  const [mode, setMode] = useState('edit');
  const [currentRecord, setCurrentRecord] = useState({});

  const getTableData = async data => {
    const res = await list(data);
    if (res && res.success) {
      return {
        total: res.resultObject.total,
        data: res.resultObject.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  };

  const { tableProps, refresh } = useAntdTable(
    params => getTableData({ pageSize: 15, pageNum: params.current, entity: paramsObj }),
    [paramsObj, height],
    {
      defaultPageSize: size,
      form,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    setSize(getPageSizeByCardHeight(height - 64 - 8));
  }, [height]);

  const handleReset = () => {
    form.resetFields();
  };

  const submit = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      // 处理表单值，忽略空字符串的参数
      const values = Object.fromEntries(Object.entries(fieldsValue).filter(([key, value]) => key !== '' && value !== ''));
      setParamsObj(values);
    });
  };

  const handleEdit = record => {
    setVisible(true);
    if (judgeObj(record)) {
      setMode('add');
    } else {
      setCurrentRecord(record);
      setMode('edit');
    }
  };

  const onClose = () => {
    setVisible(false);
  };

  const columns = [
    {
      title: '水印名称',
      dataIndex: 'watermarkName',
      ellipsis: true,
    },
    {
      title: '水印编码',
      dataIndex: 'watermarkCode',
      ellipsis: true,
    },
    {
      title: '水印内容',
      dataIndex: 'watermarkText',
      ellipsis: true,
    },
    {
      title: '水印类型',
      dataIndex: 'watermarkType',
      width: '100px',
      render: (text, record) => {
        if (record.watermarkType === 1000) {
          return '明水印';
        }
        if (record.watermarkType === 1100) {
          return '暗水印';
        }
        return '';
      },
    },
    {
      title: '描述',
      dataIndex: 'watermarkDesc',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: '100px',
      align: 'center',
      render: (text, record) => {
        if (record.statusCd === '1000') {
          // 有效
          return <span className="text-success">有效</span>;
        }
        // 失效
        return <span className="text-danger">无效</span>;
      },
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={() => handleEdit(record)}>编辑</a>
          <Divider type="vertical" />
          <a
            onClick={() => {
              Modal.confirm({
                title: '是否确认删除',
                onOk: async () => {
                  const res = await removeById({ id: record.id });
                  if (res && res.success) {
                    message.success('操作成功', 1, () => {
                      refresh();
                    });
                  }
                },
              });
            }}
          >
            删除
          </a>
        </span>
      ),
    },
  ];

  return (
    <>
      <Card
        title="水印配置管理"
        style={{ minHeight: height }}
        className="cute"
        extra={(
          <div style={{ display: 'inline-block' }}>
            <Button
              className="margin-right"
              type="primary"
              onClick={() => {
                handleEdit({});
              }}
            >
              新增
            </Button>
          </div>
        )}
      >
        <Form className="flow fix-label">
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="水印名称">{getFieldDecorator('watermarkName')(<Input allowClear placeholder="请输入" />)}</Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="水印编码">{getFieldDecorator('watermarkCode')(<Input allowClear placeholder="请输入" />)}</Form.Item>
            </Col>
            <Col span={12} className="text-right">
              <Button type="primary" onClick={submit}>
                查询
              </Button>
              <Button className="margin-left" onClick={handleReset}>
                重置
              </Button>
            </Col>
          </Row>
        </Form>
        <SlickTable
          style={{ marginTop: 8 }}
          rowKey={record => record.id}
          columns={columns}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: 15,
            },
          }}
        />
      </Card>
      <Drawer
        title={mode === 'edit' ? '编辑水印配置' : '新增水印配置'}
        destroyOnClose
        width={620}
        onClose={onClose}
        visible={visible}
        bodyStyle={{ paddingBottom: 80 }}
      >
        <Edit data={currentRecord} mode={mode} close={onClose} refresh={refresh} />
      </Drawer>
    </>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(WatermarkManage));
