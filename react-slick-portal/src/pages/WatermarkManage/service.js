import request from '@/utils/request';

export async function add(data) {
  return request('portal/SystemWatermarkConfController/add.do', {
    data,
  }).then(res => res);
}

export async function edit(data) {
  return request('portal/SystemWatermarkConfController/edit.do', {
    data,
  }).then(res => res);
}

export async function removeById(data) {
  return request('portal/SystemWatermarkConfController/delete.do', {
    data,
  }).then(res => res);
}

export async function list(data) {
  return request('portal/SystemWatermarkConfController/list.do', {
    data,
  }).then(res => res);
}
