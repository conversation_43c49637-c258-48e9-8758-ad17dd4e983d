/* 集团客户关系批量处理 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Button, Card, Col, Divider, Form, message, Row, Select } from 'antd';
import { connect } from 'dva';
import { useFormTable } from '@umijs/hooks';
import PropTypes from 'prop-types';
import moment from 'moment';
import SlickTable from '@/components/SlickTable';
import style from './index.less';
import { TASK_STATUS, TASK_TYPE } from '@/pages/CustRelationBatch/const';
import { listRecordByCreateStaff } from '@/pages/CustRelationBatch/service';
import FileImport from '@/pages/CustRelationBatch/components/FileImport';
import CustomSpace from '@/components/CustomSpace';
import { exportExcelFile } from '@/utils/utils';

const DEFAULT_PAGE_SIZE = 10;

const getTableData = async ({ current, pageSize }, formData) => {
  const { taskStatus, taskType } = formData;
  try {
    const res = await listRecordByCreateStaff({
      taskStatus,
      taskType,
      pageNum: current,
      pageSize,
    });

    if (!res) {
      throw new Error('请求失败，请重试');
    }

    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE') {
      return {
        total: resultObject?.total || 0,
        list: resultObject?.list || [],
      };
    }
    throw new Error(resultMsg || '获取数据失败');
  } catch (error) {
    message.error(error.message || '获取数据失败');
    return {
      total: 0,
      list: [],
    };
  }
};

const Index = ({ size: { height }, form }) => {
  const tableRef = useRef(null);
  const { getFieldDecorator, validateFields } = form;
  const [importVisible, setImportVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  const {
    tableProps,
    search: { submit, reset },
  } = useFormTable(getTableData, {
    defaultPageSize: DEFAULT_PAGE_SIZE,
    form,
    manual: false,
  });

  const { pagination, ...restTableProps } = tableProps;

  /**
   * 导出
   */
  const handleExport = record => {
    exportExcelFile({
      url: 'portal/CustRelationBatchController/exportResultExcelFile.do',
      title: `客户关系导入结果集(${moment().format('YYYY-MM-DD HH-mm-ss')})`,
      sendParams: {
        taskId: record.taskId,
      },
      setLoading: setExportLoading,
    });
  };

  const handleSubmit = useCallback(() => {
    validateFields(async err => {
      if (err) return;

      setIsLoading(true);
      try {
        await submit();
      } catch (error) {
        message.error('查询失败，请重试');
      } finally {
        setIsLoading(false);
      }
    });
  }, [submit, validateFields]);

  const handleReset = useCallback(() => {
    reset();
    validateFields(async err => {
      if (err) return;

      setIsLoading(true);
      try {
        await submit();
      } finally {
        setIsLoading(false);
      }
    });
  }, [reset, submit, validateFields]);

  const handleImport = useCallback(() => {
    setImportVisible(true);
  }, []);

  const handleImportClose = useCallback(() => {
    setImportVisible(false);
    validateFields(async err => {
      if (err) return;

      setIsLoading(true);
      try {
        await submit();
      } finally {
        setIsLoading(false);
      }
    });
  }, [submit, validateFields]);

  const renderStatusOptions = useMemo(
    () =>
      Object.entries(TASK_STATUS).map(([key, label]) => (
        <Select.Option key={key} value={key}>
          {label}
        </Select.Option>
      )),
    []
  );

  const renderTypeOptions = useMemo(
    () =>
      Object.entries(TASK_TYPE).map(([key, label]) => (
        <Select.Option key={key} value={key}>
          {label}
        </Select.Option>
      )),
    []
  );

  return (
    <>
      <Card title="集团客户关系批量操作" className="cute" style={{ minHeight: height }} bordered>
        <Form className="flow fix-label">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label="任务进度">
                {getFieldDecorator('taskStatus', {
                  initialValue: '',
                })(<Select placeholder="请选择">{renderStatusOptions}</Select>)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="任务类型">
                {getFieldDecorator('taskType', {
                  initialValue: '',
                })(<Select placeholder="请选择">{renderTypeOptions}</Select>)}
              </Form.Item>
            </Col>
            <Col span={12} className="text-right">
              <Button type="primary" onClick={handleSubmit} loading={isLoading}>
                查询
              </Button>
              <Button style={{ margin: '0 8px' }} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" onClick={handleImport}>
                客户关系批量导入
              </Button>
            </Col>
          </Row>
        </Form>
        <SlickTable
          style={{ marginTop: 20 }}
          ref={tableRef}
          rowKey={record => record.GROUP_ID}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: DEFAULT_PAGE_SIZE,
            },
          }}
          columns={[
            {
              title: '批次号',
              dataIndex: 'taskId',
              key: 'taskId',
              width: 180,
            },
            {
              title: '任务类型',
              dataIndex: 'taskName',
              key: 'taskName',
              width: 120,
            },
            {
              title: '任务发起人',
              dataIndex: 'createStaffName',
              key: 'createStaffName',
              width: 80,
            },
            {
              title: '任务进度',
              dataIndex: 'taskStatus',
              key: 'taskStatus',
              width: 80,
              render: (_, record) => <span className={style.groupLevel}>{TASK_STATUS[record.taskStatus] || record.taskStatus}</span>,
            },
            {
              title: '任务结果备注',
              dataIndex: 'remark',
              key: 'remark',
              width: 200,
            },
            {
              title: '任务创建时间',
              dataIndex: 'createTime',
              key: 'createTime',
              width: 120,
            },
            {
              title: '操作',
              align: 'center',
              key: 'action',
              width: 200,
              render: (_, record) => {
                const buttons = [
                  <a onClick={() => handleExport(record)} loading={exportLoading}>
                    下载导入结果数据集
                  </a>,
                ];
                return (
                  <CustomSpace align="center" wrap split={<Divider type="vertical" />}>
                    {buttons.map(item => item)}
                  </CustomSpace>
                );
              },
            },
          ]}
        />
      </Card>
      <FileImport visible={importVisible} onClose={handleImportClose} />
    </>
  );
};

Index.propTypes = {
  size: PropTypes.shape({
    height: PropTypes.number.isRequired,
  }).isRequired,
  form: PropTypes.object.isRequired,
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(Index));
