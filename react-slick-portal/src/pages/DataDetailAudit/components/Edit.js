import React, { useState } from 'react';
import { Form, Row, Col, Select, Input, Button, message } from 'antd';
import { connect } from 'dva';
import request from '@/utils/request';

const { Option } = Select;

function Edit(props) {
  const { getFieldDecorator } = props.form;
  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {
    props.form.validateFields((err, fieldsValue) => {
      if (err) return;
      // console.log(fieldsValue,props.passData)
      setLoading(true);
      const paramsObj = {
        ...fieldsValue,
        fileSyncAuditDetailId: props.editBody.fileSyncAuditDetailId,
      };
      request('orgauth/FileSyncAuditDetailController/updateDetail.do', {
        data: paramsObj,
        method: 'POST',
      }).then(res => {
        if (res) {
          props.close();
          props.refresh();
          setLoading(false);
          message.success('更新成功!');
        } else {
          message.error('保存失败!');
          setLoading(false);
        }
      });
    });
  };

  return (
    <>
      <Form layout="vertical" onSubmit={handleSubmit}>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="数据明细">{getFieldDecorator('syncDetail', {
              initialValue: props.editBody.syncDetail,
              rules: [{ required: true, message: '请输入同步明细' }],
            })(<Input.TextArea rows={4} placeholder="请输入" />)}</Form.Item>
          </Col>
        </Row>
      </Form>

      <div
        style={{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
        }}
      >
        {(
          <Button type="primary" className="margin-right" onClick={handleSubmit} loading={loading}>
            编辑
          </Button>
        )}
        <Button type="ghost" onClick={props.close}>
          取消
        </Button>
      </div>
    </>
  );
}

export default connect(({ paramsConfig }) => ({
}))(
  Form.create({
  })(Edit)
);
