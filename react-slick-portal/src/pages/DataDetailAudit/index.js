/* eslint-disable */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, Row, Col, Select, Icon, Input, Button, Tag, Modal, Divider, message, Drawer, DatePicker } from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import { useAntdTable } from '@umijs/hooks';
import request from '@/utils/request';
import Edit from './components/Edit';
import moment from 'moment';

function DataDetailAudit({form}) {
  const [selectedRows, setSelectedRows] = useState([]);
  const { getFieldDecorator } = form;

  const [paramsObj, setParamsObj] = useState({});
  const [sysInfo, setSysInfo] = useState([]);
  const [visible, setVisible] = useState(false);
  const [editBody, setEditBody] = useState({});


  const fileTypeOptions = [
    { value: '1', label: '用户同步' },
    { value: '2', label: '用户角色同步' },
    { value: '3', label: '用户组织同步' },
    { value: '4', label: '角色同步' },
    { value: '5', label: '组织同步' },
  ];

  const statusOptions = [
    { value: '1000', label: '未操作' },
    { value: '1100', label: '同步成功' },
    { value: '1200', label: '同步失败' }
    ]

  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  };

  function transferDateToStr() {
    const date = new Date();
    const y = date.getFullYear();
    let m = date.getMonth() + 1;
    m = m < 10 ? `0${m}` : m;
    let d = date.getDate();
    d = d < 10 ? `0${d}` : d;
    return `${y}-${m}-${d}`;
  }


  const getFileTypeLabel = value => {
    const option = fileTypeOptions.find(option => option.value === value);
    return option ? option.label : value;
  };


  const getTableData = ({ current, pageSize, paramsObj }) => {
    return request('orgauth/FileSyncAuditDetailController/getDetailsByQo.do', {
      data: {
        pageNum: current,
        pageSize: pageSize,
        ...paramsObj,
      },
    }).then(res => {
      if (Array.isArray(res.list)) {
        return {
          total: res.total,
          data: res.list.map(val => {
            const { children, ...rest } = val;
            return rest;
          }),
        };
      }
      return {
        total: 0,
        data: [],
      };
    });
  };

  const { tableProps, refresh } = useAntdTable(
    params => {
      return getTableData({ ...params, paramsObj });
    },
    [paramsObj],
    {
      form,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    getSysInfo()
  }, []);



  const submit = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      let obj = {};
      for (let item in fieldsValue) {
        if (fieldsValue[item] !== undefined && fieldsValue[item] !== '' && fieldsValue[item] !== null) {
          if (item === 'syncDate') {
            const syncDate = moment(fieldsValue.syncDate).format('YYYYMMDD');
            obj[item] = `${syncDate}`;
          } else {
            obj[item] = `${fieldsValue[item]}`;
          }
        }
      }
      setParamsObj(obj);
    });
  };

  const handleSelectRows = value => {
    setSelectedRows(value);
  };

  const handleEdit = record => {
    setEditBody(record);
    setVisible(true);
  };

  const onClose = () => {
    setVisible(false);
  };

  const handleEffect = record => {
    request('orgauth/FileSyncAuditDetailController/syncData.do', {
      data: {
        fileSyncAuditDetailIds: [record.id]
      },
      method: 'POST',
    }).then(res => {
      if (res) {
        refresh();
        res.resultCode === 'TRUE' ? message.success('同步成功!') : message.error(res.resultMsg);
      } else {
        message.error('操作失败！');
      }
    });
  };

  const getSysInfo = record => {
    request('orgauth/SystemInfoController/getSystemInfoList.do', {
      method: 'GET',
    }).then(res => {
      if (res) {
        const sysInfos = res.map(item => ({
          systemNbr: item.systemNbr,
          systemName: item.systemName
        }));
        setSysInfo(sysInfos)
      } else {
        message.error('获取系统信息失败');
      }
    });
  };



  const batchSync = () => {
    let arr = [];
    selectedRows.map(item => {
      arr.push(item.id);
    });
    request('orgauth/FileSyncAuditDetailController/syncData.do', {
      data: {
        fileSyncAuditDetailIds: arr
      },
      method: 'POST',
    })
      .then(res => {
      if (res && res.length > 0) {
        refresh();
        setSelectedRows([]);
        message.success('批量同步成功!');
      } else {
        refresh();
        setSelectedRows([]);
        message.error(res.resultMsg);
      }
    });
  };

  const handleReset = () => {
    form.resetFields();
    setParamsObj({});
  };

  const columns = [
    {
      title: '系统名称',
      dataIndex: 'sysCodeName',
      align: 'center',
      width: '100px',
      ellipsis: true,
      render: (text, record) => {
        if (record.sysCodeName !== undefined &&  record.sysCodeName !== '') {
          return <span>{record.sysCodeName}</span>;
        } else {
          return <span>{record.systemInfoCode}</span>;
        }
      }
    },
    {
      title: '同步时间',
      dataIndex: 'syncDate',
      align: 'center',
      width: '78px',
      ellipsis: true,
    },
    {
      title: '错误原因',
      dataIndex: 'errorReason',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '同步明细',
      dataIndex: 'syncDetail',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '明细所在行',
      width: '85px',
      dataIndex: 'lineNumber',
      ellipsis: true,
      align: 'center'
    },
    {
      title: '同步类型',
      width: '100px',
      dataIndex: 'fileType',
      align: 'center',
      ellipsis: true,
      render: (text, record) => {
        return <span>{getFileTypeLabel(record.fileType)}</span>;
      },
    },
    {
      title: '最近一次更新时间',
      dataIndex: 'updateDate',
      width: '135px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'statusCd',
      width: '100px',
      align: 'center',
      render: (text, record) => {
        if (record.statusCd === '1000') {
          return <span>未操作</span>;
        } else if (record.statusCd === '1100') {
          return <span className="text-success">同步成功</span>;
        } else if (record.statusCd === '1200'){
          return <span className="text-danger">同步失败</span>;
        }
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: '135px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        return (
          <span>
            <a
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </a>
            <Divider type="vertical" />
            {record.statusCd === '1100' ? null : (
              <a
                onClick={() => {
                  handleEffect(record);
                }}
              >
                同步
              </a>
            )}
          </span>
        );
      },
    },
  ];

  return (
    <>
      <Card
        title="明细数据上报管理"
        className="cute"
      >
        <Form className="flow fix-label">
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="系统名称">{getFieldDecorator('systemInfoCode')(
                <Select allowClear placeholder="请选择系统名称" style={{ width: '100%' }}>
                  {sysInfo.map(option => (
                    <Option key={option.systemNbr} value={option.systemNbr}>
                      {option.systemName}
                    </Option>
                  ))}
                </Select>
              )}</Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="同步类型">{getFieldDecorator('fileType')(
                <Select allowClear placeholder="请选择文件同步类型" style={{ width: '100%' }}>
                  {fileTypeOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              )}</Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="同步时间：" {...formItemLayout}>
                {getFieldDecorator('syncDate', {
                })(<DatePicker style={{ width: '100%' }} allowClear />)}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="状态类型">{getFieldDecorator('statusCd')(
                <Select allowClear placeholder="请选择状态类型" style={{ width: '100%' }}>
                  {statusOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              )}</Form.Item>
            </Col>
            <Col span={18} className="text-right">
              <Button type="primary" onClick={submit}>
                查询
              </Button>
              <Button className="margin-left" onClick={handleReset}>
                重置
              </Button>
            </Col>
          </Row>
        </Form>
        <SlickTable
          style={{ marginTop: 8 }}
          rowKey="id"
          columns={columns}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
            },
          }}
          pick="checkbox"
          extra={
            selectedRows.length > 0 ? (
              <Button type="primary" ghost onClick={batchSync}>
                批量同步
              </Button>
            ) : null
          }
          onSelectRow={handleSelectRows}
          selectedRows={selectedRows}
        />
      </Card>
      <Drawer
        title={'明细数据编辑'}
        destroyOnClose
        width={620}
        onClose={onClose}
        visible={visible}
        bodyStyle={{ paddingBottom: 80 }}
      >
        <Edit editBody = {editBody} close={onClose} refresh={refresh} />
      </Drawer>
    </>
  );
}

export default connect(() => ({
}))(Form.create()(DataDetailAudit));
