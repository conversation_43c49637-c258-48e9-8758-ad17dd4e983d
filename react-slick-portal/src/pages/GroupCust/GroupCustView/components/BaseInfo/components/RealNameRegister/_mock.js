import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '@/defaultSettings';

const proxy = {
  'POST /portal/order_IGroupRealNameInfoOperCSV_query': (req, res) => {
    const {
      body: { queryType },
    } = req;
    res.send(
      mockjs.mock({
        resultCode: 'TRUE',
        resultMsg: '操作成功',
        resultObject: {
          rspParam: {
            GROUP_ID: 'G001',
            GROUP_NAME: '测试集团',
            DATA_TYPE: 1,
            NAME: '张三',
            CERT_TYPE: '身份证',
            CERT_NO: '110101199001011234',
            CERT_ADDRESS: '北京市朝阳区',
            EMAIL: '<EMAIL>',
            BILL_ID: '13800138000',
            VALID_DATE: '2023-01-01',
            EXPIRE_DATE: '2023-12-31',
            COMPARE_RESULT: '通过',
            AUTHORIZATION_LETTER: '授权书.pdf',
            PROTOCOL: 'PROTO123456',
            INPUT_DESC: '系统录入',
            REMARKS: '无',
            EXT1: '成功',
          },
        },
        success: true,
      })
    );
  },
  'POST /portal/rule_IGroupRealNameCSV_queryGroupRealNameInfo': (req, res) => {
    const {
      body: { queryType },
    } = req;
    res.send(
      mockjs.mock({
        resultCode: 'TRUE',
        resultMsg: '操作成功',
        resultObject: {
          rspParam: [
            {
              INFO_ID: '1',
              DATA_TYPE: 1,
              GROUP_ID: 'G001',
              GROUP_NAME: '测试集团1',
              NAME: '张三',
              VALID_DATE: '2023-01-01',
              EXPIRE_DATE: '2023-12-31'
            },
          ],
        },
        success: true,
      })
    );
  },
};

export default delay(proxy, defaultSettings.delay);
