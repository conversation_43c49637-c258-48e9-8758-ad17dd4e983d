import React, { useState, useEffect } from 'react';
import { Card, Form, Row, Col, Input, Button, Select, message, Modal } from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import styles from './index.less';
import {
  queryDisticts,
  queryPianQu,
  queryQxArea,
  convertMemberInfo,
  deleteMemberInfo,
} from '@/pages/GroupCust/GroupCustManage/services';
import request from '@/utils/request';

const { Option } = Select;

const InternalMembers = ({ form, currentCust }) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [disInfos, setDisInfos] = useState([]);
  const [countyInfos, setCountyInfos] = useState([]);
  const [regionDetails, setRegionDetails] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const { getFieldDecorator, getFieldValue, setFieldsValue } = form;

  // 查询列表数据
  const fetchData = (params = {}, paginationParams = {}) => {
    setLoading(true);
    const { current = 1, pageSize = 10 } = paginationParams;

    request('portal/GroupMemberController/queryMemberInfo.do', {
      method: 'POST',
      data: {
        ...params,
        QRY_TYPE: '0', // 内部成员类型为0
        pageInfo: {
          currentPage: current,
          pageSize,
        },
      },
    }).then(res => {
      setLoading(false);
      if (res.resultCode === 'TRUE') {
        setDataSource(res.resultObject?.data || []);
        setPagination({
          ...pagination,
          current,
          pageSize,
          total: res.resultObject?.total || 0,
        });
      } else {
        message.error(res.resultMsg || '查询失败');
      }
    }).catch(() => {
      setLoading(false);
      message.error('查询失败');
    });
  };

  useEffect(() => {
    fetchData({}, pagination);
    // eslint-disable-next-line
    getDisInfo();
  }, []);

  const handleSearch = () => {
    form.validateFields((err, values) => {
      if (!err) {
        // 处理时间范围等特殊字段
        const params = {
          ...values,
        };

        // 如果有值为undefined或空字符串的字段，从参数中删除
        Object.keys(params).forEach(key => {
          if (params[key] === undefined || params[key] === '') {
            delete params[key];
          }
        });

        // 查询时重置到第一页
        const newPagination = {
          ...pagination,
          current: 1,
        };
        setPagination(newPagination);
        fetchData(params, newPagination);
      }
    });
  };

  const handleReset = () => {
    form.resetFields();
    setCountyInfos([]);
    setRegionDetails([]);
  };

  const toggleAdvanced = () => {
    setShowAdvanced(!showAdvanced);
  };

  // 新增成员
  const handleAdd = () => {
    // TODO: 实现新增成员功能
  };

  // 批量新增
  const handleBatchAdd = () => {
    // TODO: 实现批量新增功能
  };

  // 批量删除
  const handleBatchDelete = () => {
    // TODO: 实现批量删除功能
  };

  // 导出
  const handleExport = () => {
    // TODO: 实现导出功能
  };

  // 修改成员
  const modifyInnerData = record => {
    Modal.confirm({
      title: '确认操作',
      content: `是否确认修改 ${record.CUST_NAME}(${record.ACCESS_NUM}) 的信息？`,
      okText: '确认',
      cancelText: '取消',
      onOk() {
        // TODO: 调用修改接口
        message.success('操作成功');
      },
    });
  };

  // 转为关键人
  const transInnToKeyPerson = record => {
    Modal.confirm({
      title: '确认操作',
      content: '您确定转换吗?',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        convertMemberInfo({ MODE: 'inMember_convert', GROUP_MEB_REL_ID: record.GROUP_MEB_REL_ID }).then(res => {
          const { resultCode, resultMsg } = res;
          if (resultCode !== 'TRUE') {
            message.error(resultMsg);
          } else {
            message.success('转换成功！');
            // eslint-disable-next-line
            refresh();
          }
        });
      },
    });
  };

  // 删除成员
  const delInnerData = record => {
    Modal.confirm({
      title: '确认删除',
      content: '您确定删除吗?',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        const params = {
          hide_MODE: 'inMember_del',
          GROUP_MEB_ID: record.GROUP_MEB_ID,
          ACCESS_NUM: record.ACCESS_NUM,
          MODE: 'inMember_del',
        };
        deleteMemberInfo(params).then(res => {
          const { resultCode, resultMsg } = res;
          if (resultCode !== 'TRUE') {
            message.error(resultMsg);
          } else {
            message.success('删除成功！');
            // eslint-disable-next-line
            refresh();
          }
        });
      },
    });
  };

  // 获取地市
  const getDisInfo = async () => {
    const resp = await queryDisticts();
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.resultInfo) {
      setDisInfos(resp.resultObject.rspParam.busiInfo.resultInfo);
    }
  };

  // 获取区县
  const queryCounty = val => {
    // 清空区县和片区的值
    setFieldsValue({
      MGMT_COUNTY: undefined,
      AREA: undefined,
    });
    setRegionDetails([]);

    if (val) {
      queryQxArea(val).then(resp => {
        if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.resultInfo) {
          setCountyInfos(resp.resultObject.rspParam.busiInfo.resultInfo);
        }
      });
    } else {
      setCountyInfos([]);
    }
  };

  // 获取片区
  const queryRegionDetails = val => {
    setFieldsValue({
      AREA: undefined,
    });

    if (val) {
      queryPianQu(val).then(resp => {
        if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.resultInfo) {
          setRegionDetails(resp.resultObject.rspParam.busiInfo.resultInfo);
        }
      });
    } else {
      setRegionDetails([]);
    }
  };

  // 处理表格分页变化
  const handleTableChange = paginationParams => {
    const newPagination = {
      ...pagination,
      current: paginationParams.current,
      pageSize: paginationParams.pageSize,
    };
    setPagination(newPagination);

    // 获取当前的搜索条件
    form.validateFields((err, values) => {
      if (!err) {
        const params = {
          ...values,
        };
        Object.keys(params).forEach(key => {
          if (params[key] === undefined || params[key] === '') {
            delete params[key];
          }
        });
        fetchData(params, newPagination);
      }
    });
  };

  // 刷新列表数据
  const refresh = () => {
    form.validateFields((err, values) => {
      if (!err) {
        const params = {
          ...values,
        };
        Object.keys(params).forEach(key => {
          if (params[key] === undefined || params[key] === '') {
            delete params[key];
          }
        });
        fetchData(params, pagination);
      }
    });
  };

  // 下拉框通用样式
  const selectStyle = {
    width: '100%',
    dropdownStyle: {
      maxHeight: 400,
      overflow: 'auto',
    },
    dropdownMatchSelectWidth: false,
    style: {
      width: '100%',
      minWidth: '180px',
    },
  };

  return (
    <div className={styles.container}>
      <Card bordered={false}>
        <Form layout="inline" className={styles.form}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label="集团编码">
                {getFieldDecorator('GROUP_ID', {
                  initialValue: currentCust.GROUP_ID,
                })(<Input placeholder="请输入集团编码" />)}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="手机号码">
                {getFieldDecorator('ACCESS_NUM')(<Input placeholder="请输入手机号码" />)}
              </Form.Item>
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              <a style={{ marginLeft: 8 }} onClick={toggleAdvanced}>
                {showAdvanced ? '收起' : '高级搜索'} <span className={showAdvanced ? 'anticon-up' : 'anticon-down'} />
              </a>
            </Col>
          </Row>
          {showAdvanced && (
            <div style={{ marginTop: 16 }}>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item label="归属地市">
                    {getFieldDecorator('MGMT_DISTRICT')(
                      <Select
                        {...selectStyle}
                        placeholder="请选择归属地市"
                        onChange={queryCounty}
                        allowClear
                      >
                        {disInfos.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="归属区县">
                    {getFieldDecorator('MGMT_COUNTY')(
                      <Select
                        {...selectStyle}
                        placeholder="请选择归属区县"
                        onChange={queryRegionDetails}
                        allowClear
                        disabled={!getFieldValue('MGMT_DISTRICT')}
                      >
                        {countyInfos.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="归属片区">
                    {getFieldDecorator('AREA')(
                      <Select
                        {...selectStyle}
                        placeholder="请选择归属片区"
                        allowClear
                        disabled={!getFieldValue('MGMT_COUNTY')}
                      >
                        {regionDetails.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24} style={{ marginTop: 16 }}>
                <Col span={8}>
                  <Form.Item label="客户标识">
                    {getFieldDecorator('CUST_ID')(<Input placeholder="请输入客户标识" />)}
                  </Form.Item>
                </Col>
              </Row>
            </div>
          )}
        </Form>

        <div style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <Button style={{ marginLeft: 8 }} onClick={handleReset}>
            重置
          </Button>
          <Button type="primary" style={{ marginLeft: 8 }} onClick={handleAdd}>新增</Button>
          <Button style={{ marginLeft: 8 }} onClick={handleBatchAdd}>批量新增</Button>
          <Button style={{ marginLeft: 8 }} onClick={handleBatchDelete}>批量删除</Button>
          <Button style={{ marginLeft: 8 }} onClick={handleExport}>全部导出</Button>
        </div>

        <SlickTable
          loading={loading}
          dataSource={dataSource}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: '手机号码',
              dataIndex: 'ACCESS_NUM',
              key: 'accessNum',
              width: 120,
            },
            {
              title: '客户名称',
              dataIndex: 'CUST_NAME',
              key: 'custName',
              width: 120,
            },
            {
              title: '集团编号',
              dataIndex: 'GROUP_ID',
              key: 'groupId',
              width: 140,
            },
            {
              title: '所属集团',
              dataIndex: 'GROUP_NAME',
              key: 'groupName',
              width: 150,
            },
            {
              title: '是否联系人',
              dataIndex: 'IS_LINK_MAN',
              key: 'isLinkMan',
              width: 120,
            },
            {
              title: '是否关键人物',
              dataIndex: 'IS_KEYMAN',
              key: 'isKeyman',
              width: 120,
            },
            {
              title: '是否普通成员',
              dataIndex: 'IS_MAN',
              key: 'isMan',
              width: 120,
            },
            {
              title: '用户类型',
              dataIndex: 'SUBSCRIBER_TYPE_DETAIL',
              key: 'subscriberTypeDetail',
              width: 100,
            },
            {
              title: '加入集团时间',
              dataIndex: 'JOIN_DATE',
              key: 'joinDate',
              width: 200,
            },
            {
              title: '职位',
              dataIndex: 'DUTY',
              key: 'duty',
              width: 120,
            },
            {
              title: '说明',
              dataIndex: 'REMARKS',
              key: 'remarks',
              width: 150,
            },
            {
              title: '用户状态',
              dataIndex: 'SUBSCRIBER_STATUS_DETAIL',
              key: 'subscriberStatusDetail',
              width: 100,
            },
            {
              title: '操作',
              key: 'action',
              fixed: 'right',
              width: 200,
              render: (text, record) => (
                <>
                  <a onClick={() => transInnToKeyPerson(record)}>转为关键人</a>
                  <a style={{ marginLeft: 8 }} onClick={() => modifyInnerData(record)}>修改</a>
                  <a style={{ marginLeft: 8 }} onClick={() => delInnerData(record)}>删除</a>
                </>
              ),
            },
          ]}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: total => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
        />
      </Card>
    </div>
  );
};

export default connect(({ groupCustView }) => ({
  currentCust: groupCustView.currentCust,
}))(Form.create()(InternalMembers));
