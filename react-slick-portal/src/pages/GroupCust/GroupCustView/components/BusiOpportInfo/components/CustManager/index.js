import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, Row, Col, Icon, Input, Button, Divider, Drawer, Select } from 'antd';
import classNames from 'classnames';
import { connect } from 'dva';
import { useBoolean, useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight } from '@/utils/utils';
import { getCustList } from '@/pages/GroupCust/GroupCustManage/services';
import CustomSpace from '@/components/CustomSpace';

/* const getTableData = ({ current, pageSize, ...rest }) =>
  getCustList({
    page: current,
    pageNum: current,
    pageSize,
    rowNum: pageSize,
    sortName: '',
    sortOrder: 'asc',
    ...rest,
  }).then(res => {
    if (Array.isArray(res.list)) {
      return {
        total: res.total,
        data: res.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  }); */
const getTableData = ({ current, pageSize }) =>
  // 直接返回模拟的数据，这里简单处理，实际情况可能需要根据current和pageSize参数过滤数据
  new Promise(resolve => {
    setTimeout(() => {
      resolve({
        total: 0,
        data: [
          [
            [
              {
                managerId: 'M001',
                managerName: '张三',
                phoneNumber: '13800138000',
                department: '销售部',
                city: '北京市',
                district: '朝阳区',
                area: '东城区',
                officePhone: '010-12345678',
              },
              {
                managerId: 'M002',
                managerName: '李四',
                phoneNumber: '13900139000',
                department: '市场部',
                city: '上海市',
                district: '浦东新区',
                area: '黄浦区',
                officePhone: '021-87654321',
              },
              {
                managerId: 'M003',
                managerName: '王五',
                phoneNumber: '13700137000',
                department: '技术部',
                city: '广州市',
                district: '天河区',
                area: '越秀区',
                officePhone: '020-56789012',
              },
            ],
          ],
        ],
      });
    }, 300); // 模拟异步操作
  });
const Index = ({ size: { height }, form }) => {
  const { state: expand, toggle } = useBoolean(false);
  const [mode, setMode] = useState('view');
  const [detail, setDetail] = useState({});
  const { state: drawerVisible, setTrue: showDrawer, setFalse: hideDrawer } = useBoolean(false);
  const tableRef = useRef(null);
  // card-body除rowHeight
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const { getFieldDecorator } = form;
  const {
    tableProps,
    search: { submit, reset },
  } = useAntdTable(
    params => {
      /**
       * params包含 分页和表单值，格式如下：
       * {current: 1,filters: {},pageSize: 2,sorter:{},bulletinTitle:''}
       * expand=true 表示高级搜索；高级搜索无视快捷搜索上的bulletinTitle值；反之一样
       */

      if (expand) {
        return getTableData({ ...params, pageSize: size });
      }
      return getTableData({ ...params, pageSize: size });
    },
    // 依赖变化会触发请求
    [height],
    {
      defaultPageSize: size,
      form,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  const openAddDrawer = () => {
    setMode('add');
    setDetail({});
    showDrawer();
  };

  /**
   * 编辑
   * @param {object} targetRow
   */
  const openEditDrawer = targetRow => {
    setMode('edit');
    setDetail(targetRow);
    showDrawer();
  };

  /**
   * 删除
   * @param targetRow
   */
  const handleDelete = targetRow => {
    console.log('handleDelete', targetRow);
  };

  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  return (
    <>
      <Card
        title="查询"
        className="cute"
        extra={(
          <>
            <Button type="default" className="inline-block margin-left" onClick={() => toggle()}>
              高级查询
              <Icon className={classNames('animated', { rotate180: expand })} type="down" />
            </Button>
          </>
        )}
        bordered
      >
        {expand ? (
          <Form className="flow fix-label">
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item label="经理名称/工号">{getFieldDecorator('manager')(<Input
                  placeholder="请输入"
                />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="经理类型">
                  {getFieldDecorator('managerRole')(
                    <Select allowClear placeholder="请选择">
                      <Select.Option value="1">主要服务</Select.Option>
                      <Select.Option value="2">辅助服务</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="手机号码">{getFieldDecorator('phone')(<Input
                  allowClear
                  placeholder="请输入"
                />)}
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item label="状态">
                  {getFieldDecorator('status')(
                    <Select allowClear placeholder="请选择">
                      <Select.Option value="1">当前客户经理</Select.Option>
                      <Select.Option value="2">历史客户经理</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="所属地市">{getFieldDecorator('city')(<Input
                  allowClear
                  placeholder="请输入"
                />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="所属区县">{getFieldDecorator('district')(<Input
                  allowClear
                  placeholder="请输入"
                />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="所属片区">{getFieldDecorator('area')(<Input
                  allowClear
                  placeholder="请输入"
                />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="所属部门">{getFieldDecorator('department')(<Input
                  allowClear
                  placeholder="请输入"
                />)}
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col offset={18} span={6} className="text-center">
                <Button type="primary" htmlType="submit" loading={tableProps.loading}>
                  查询
                </Button>
                <Button className="margin-left" onClick={reset}>
                  重置
                </Button>
                <Button className="margin-left" onClick={openAddDrawer}>
                  新增辅助客户经理
                </Button>
              </Col>
            </Row>
          </Form>
          ) : null}
        <SlickTable
          style={{ marginTop: 20 }}
          ref={tableRef}
          scroll={{ x: 'max-content' }}
          rowKey={record => record.index}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
          columns={[
            {
              title: '经理编号',
              dataIndex: 'managerId',
              key: 'managerId',
            },
            {
              title: '经理姓名',
              dataIndex: 'managerName',
              key: 'managerName',
            },
            {
              title: '手机号码',
              dataIndex: 'phoneN',
              key: 'phoneN',
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
            },
            {
              title: '所属部门',
              dataIndex: 'department',
              key: 'department',
            },

            {
              title: '地市',
              dataIndex: 'city',
              key: 'city',
            },
            {
              title: '区县',
              dataIndex: 'district',
              key: 'district',
            },
            {
              title: '片区',
              dataIndex: 'area',
              key: 'area',
            },
            {
              title: '上次变更操作员工号',
              dataIndex: 'lastChangeOperatorId',
              key: 'lastChangeOperatorId',
            },
            {
              title: '上次变更操作员工',
              dataIndex: 'lastChangeOperator',
              key: 'lastChangeOperator',
            },
            {
              title: '操作',
              align: 'center',
              key: 'action',
              width: 180,
              fixed: 'right',
              render: (_, record) => {
                const buttons = [<a onClick={() => openEditDrawer(record)}>编辑</a>,
                  <a onClick={() => handleDelete(record)}>删除</a>];
                return (
                  <CustomSpace align="center" wrap split={<Divider type="vertical" />}>
                    {buttons.map(item => item)}
                  </CustomSpace>
                );
              },
            },
          ]}
        />
      </Card>
      <Drawer
        title={mode === 'add' ? '新建' : '编辑'}
        destroyOnClose
        width={720}
        onClose={hideDrawer}
        visible={drawerVisible}
        bodyStyle={{ paddingBottom: 80 }}
      />
    </>
  );
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(Index));
