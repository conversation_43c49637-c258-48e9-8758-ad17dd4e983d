/* eslint-disable camelcase */
import React, { useEffect, useState, useMemo } from 'react';
import { Card, Col, Form, Input, InputNumber, message, Row, Select, Switch } from 'antd';
import { connect } from 'dva';
import styles from '@/pages/GroupCust/GroupCustManage/components/Edit/index.less';
import {
  ENTERPRISE_IMPORT_FLAG,
  // ENTERPRISE_SIZE_CODE,
  GROUP_CREDIT_LEVEL,
  GROUP_CUST_TYPE,
  // GROUP_SCOPE_TYPE,
  // GROUP_SERV_LEVEL,
  // GROUP_TYPE 接口映射,
  IS_BRANCH_VALUE,
  // IS_SMALLMICRO,
  // IS_UP_WLW_GROUP,
} from '@/utils/consts';
import { qryBusicompanyid, qryBusilocation, queryCommonRefl, queryCmiot, querySmallEnterprises } from '@/pages/GroupCust/GroupCustManage/services';
import { validateGroupName, validateMoney, validatePhone } from '@/utils/validator';
// import { MODE } from '../../../const';
// import LocationCascader from '@/components/LocationCascader';
import CustManager from './CustManager';
import RegionInfo from './RegionInfo';
import AsyncCascader from '@/components/AsyncCascader';

const FormItem = Form.Item;
const { Option } = Select;
const formItemLayout = {
  // labelAlign: 'left',
  // labelCol: {
  //   xs: { span: 24 },
  //   // sm: { span: 4 },
  // },
  // wrapperCol: {
  //   xs: { span: 24 },
  //   sm: { span: 16 },
  // },
};

const valiArea = () => (_, value = [], callback) => {
  if (value.length === 0) {
    callback('请选择生产经营所在省！');
    return;
  }
  if (value.length === 1) {
    callback('请选择生产经营所在地市！');
    return;
  }
  if (value.length === 2) {
    callback('请选择生产经营所在区县！');
  }
  callback();
};

const valiCalling = () => (_, value = [], callback) => {
  if (value.length === 0) {
    callback('请选择国标行业门类！');
    return;
  }
  if (value.length === 1) {
    callback('请选择国标行业大类！');
    return;
  }
  if (value.length === 2) {
    callback('请选择国标行业中类！');
  }
  callback();
};

const BaseInfo = props => {
  // eslint-disable-next-line no-unused-vars
  const { viewMode, form, groupInfo, isCmiot, customerInfo, dispatch, currentFormFieldState, formData, onValuesChange, ReflectionGroup } = props;

  const { getFieldDecorator } = form;
  // const [areas, setAreas] = useState([]);
  // const [locations, setLocations] = useState([]);
  // const [busiCounties, setBusiCounties] = useState([]);
  const [openCard, setOpenCard] = useState(viewMode !== 'edit'); // 是否展开非必填字段
  const [cmiotLoading, setCmiotLoading] = useState(false);

  /* 集团客户级别 */
  const [groupCustomerLevels, setGroupCustomerLevels] = useState([]);

  /* 行业类型 */
  const [industryTypes, setIndustryTypes] = useState([]);

  /* 经营范围 ENTERPRISE_SCOPE */
  const [enterpriseScopes, setEnterpriseScopes] = useState([]);

  // 行业类型
  const callingTypeStart = async () => {
    const resp = await queryCommonRefl('CALLING_AREA_CODE');
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.outData) {
      setIndustryTypes(resp.resultObject.rspParam.busiInfo.outData);
    }
  };

  // 集团客户级别
  const groupLevelNew = async () => {
    const resp = await queryCommonRefl('GROUP_CUST_LEVEL');
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.outData) {
      setGroupCustomerLevels(resp.resultObject.rspParam.busiInfo.outData);
    }
  };

  // 经营区域范围
  const enterpriseScope = async () => {
    const resp = await queryCommonRefl('ENTERPRISE_SCOPE');
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.outData) {
      setEnterpriseScopes(resp.resultObject.rspParam.busiInfo.outData);
    }
  };

  /**
   * 查询生产经营省份
   * @param {Array} selected - 级联选择的路径数组（selectedOptions）
   * @returns {Promise<Array>} - 区域数据（供 Cascader 使用）
   */
  const getLocation = (selected = []) => {
    const level = selected.length; // 当前层级
    let service;

    if (level === 0) {
      // 查询省（第一级）
      service = qryBusicompanyid();
    } else {
      // 查询市 / 区（第2级或第3级）
      const areaCode = selected[selected.length - 1].value;
      const areaType = String(level + 1); // 市：2，区：3
      service = qryBusilocation({ areaCode, areaType });
    }

    return service
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
          return resultObject.rspParam.busiInfo.outData.map(item => ({
            label: item.AREA_NAME,
            value: item.AREA_CODE,
            isLeaf: level === 2, // 第三级是叶子节点
          }));
        }
        message.error(resultMsg || '获取地区数据失败');
        return [];
      })
      .catch(() => {
        message.error('获取地区数据失败');
        return [];
      });
  };

  // 查询生产经营省份
  const getLocationIndustry = (selected = []) => {
    const level = selected.length; // 当前层级
    let service;

    if (level === 0) {
      // 查询省（第一级）
      service = queryCommonRefl('GROUP_ARCHIVES@TRADE_TYPE');
    }
    if (level === 1) {
      // （第2级或第3级）
      const areaCode = selected[selected.length - 1].value;
      const params = `GROUP_ARCHIVES@BIG_TRADE_TYPE@${areaCode}`;
      service = queryCommonRefl(params);
    }
    if (level === 2) {
      const areaCode = selected[selected.length - 2].value;
      const areaCodeLast = selected[selected.length - 1].value;
      const params = `GROUP_ARCHIVES@MIDDLE_TRADE_TYPE@${areaCode}@${areaCodeLast}`;
      service = queryCommonRefl(params);
    }

    // if (areaType === '1') {
    //   service = queryCommonRefl('GROUP_ARCHIVES@TRADE_TYPE');
    // } else if (areaType === '2') {
    //   const params = `GROUP_ARCHIVES@BIG_TRADE_TYPE@${areaCode[0]}`;
    //   service = queryCommonRefl(params);
    // } else {
    //   const params = `GROUP_ARCHIVES@MIDDLE_TRADE_TYPE@${areaCode[0]}@${areaCode[1]}`;
    //   service = queryCommonRefl(params);
    // }

    return service
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
          return (
            resultObject.rspParam.busiInfo.outData.map(item => ({
              label: item.STATIC_VALUE,
              value: item.STATIC_KEY,
              isLeaf: level === 2, // 第三级是叶子节点
            })) || []
          );
        }
        message.error(resultMsg || '获取行业数据失败');
        return [];
      })
      .catch(() => {
        // console.error(err); // 可选：打印错误以便调试
        message.error('获取行业数据失败');
        return [];
      });
  };

  useEffect(() => {
    callingTypeStart();
    enterpriseScope();
    // getBusicompanyid();
    // 获取列表映射值
    dispatch({
      type: 'ReflectionGroup/getReflectList',
      payload: 'GROUP_TYPE',
    });
    dispatch({
      type: 'ReflectionGroup/getReflectList',
      payload: 'ENTERPRISE_SIZE_CODE',
    });
    dispatch({
      type: 'ReflectionGroup/getReflectList',
      payload: 'GROUP_SCOPE_TYPE',
    });
    dispatch({
      type: 'ReflectionGroup/getReflectList',
      payload: 'IS_SMALLMICRO',
    });
    dispatch({
      type: 'ReflectionGroup/getReflectList',
      payload: 'GROUP_SERV_LEVEL',
    });
    groupLevelNew();
  }, []);

  const changeToCmiot = async val => {
    const address = form.getFieldValue('GROUP_LICENCE_ADDRESS');
    if (!address) {
      message.error('开启CMIOT,需要先补充"生产经营具体地址"');
      onValuesChange(
        {
          'Cmiot.IS_CMIOT': '0',
        },
        true
      );
      return;
    }
    if (!val) {
      const params = {
        CMIOT_STAFF_NUMBER: undefined,
        CMIOT_MOBILE_PHONE: undefined,
        ID_ADDR: undefined,
        CMIOT_ATTACHMENTS: undefined,
      };
      form.setFieldsValue(params);
      onValuesChange(
        {
          'Cmiot.IS_CMIOT': '0',
          'Cmiot.CMIOT_STAFF_NUMBER': '',
          'Cmiot.CMIOT_MOBILE_PHONE': '',
          'Cmiot.ID_ADDR': '',
          'Cmiot.CMIOT_ATTACHMENTS': '',
        },
        true
      );
      return;
    }
    setCmiotLoading(true);
    const staffInfo = Array.isArray(form.getFieldValue('custMgrInfo')) ? form.getFieldValue('custMgrInfo')[0] : form.getFieldValue('custMgrInfo');
    const resp = await queryCmiot(staffInfo.CUST_MGR_ID);
    setCmiotLoading(false);
    let isSwitchCmiot = false;
    if (resp.resultCode === 'TRUE' && resp?.resultObject?.rspParam?.busiInfo?.outData.length !== 0) {
      const cmitoStaffInfo = resp?.resultObject?.rspParam?.busiInfo?.outData[0];
      isSwitchCmiot = true;
      const params = {
        CMIOT_STAFF_NUMBER: cmitoStaffInfo.CMIOTSTAFFNUMBER,
        CMIOT_MOBILE_PHONE: cmitoStaffInfo.MOBILE,
        ID_ADDR: cmitoStaffInfo.ID_ADDR,
        CMIOT_ATTACHMENTS: cmitoStaffInfo.FILENAME,
      };
      // console.log('cmiot赋值params', params);
      form.setFieldsValue(params);
      onValuesChange(
        {
          'Cmiot.CMIOT_STAFF_NUMBER': cmitoStaffInfo.CMIOTSTAFFNUMBER,
          'Cmiot.CMIOT_MOBILE_PHONE': cmitoStaffInfo.MOBILE,
          'Cmiot.ID_ADDR': cmitoStaffInfo.ID_ADDR,
          'Cmiot.CMIOT_ATTACHMENTS': cmitoStaffInfo.FILENAME,
        },
        true
      );
    } else {
      message.error(resp.resultMsg);
    }
    if (val && isSwitchCmiot) {
      onValuesChange(
        {
          'Cmiot.IS_CMIOT': '1',
        },
        true
      );
    } else {
      onValuesChange(
        {
          'Cmiot.IS_CMIOT': '0',
        },
        true
      );
    }
  };

  // 禁用国标行业规则
  const disableFunc = current => opt => opt.value !== current;

  const disableOption = useMemo(() => {
    const BelongingAreaInfo = form.getFieldValue('CALLING_AREA_INFO');
    if (BelongingAreaInfo && BelongingAreaInfo.length !== 0) {
      return BelongingAreaInfo.map(item => disableFunc(item));
    }
    return [];
  }, [form.getFieldValue('CALLING_AREA_INFO')]);

  const handleGroupTypeChange = async e => {
    const groupName = form.getFieldValue('GROUP_NAME');
    const industryID = form.getFieldValue('CALLING_AREA_INFO')[1];

    if (groupName && industryID) {
      const params = {
        customerName: groupName,
        industryID,
        organizationTypeID: e,
      };
      const resp = await querySmallEnterprises(params);
      if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.outData) {
        form.setFieldsValue({ IS_SMALLMICRO: resp.resultObject.rspParam.busiInfo.outData.isSmallEnterprises });
        dispatch({
          type: 'AddGroupCust/updataFormStateAll',
          payload: {
            IS_SMALLMICRO: { disabled: true },
          },
        });
      } else {
        // 如果接口获取失败 允许用户修改
        dispatch({
          type: 'AddGroupCust/updataFormStateAll',
          payload: {
            IS_SMALLMICRO: { disabled: false },
          },
        });
      }
    }
  };
  return (
    <Card
      className="cute"
      title="基础信息"
      headStyle={{ background: '#E7F2FB', padding: '0 20px' }}
      bodyStyle={{ padding: '0' }}
      extra={!openCard ? <a onClick={() => setOpenCard(true)}>隐藏</a> : <a onClick={() => setOpenCard(false)}>展开</a>}
    >
      <div className={styles.warp}>
        <Form className="flow2 fix-label">
          <Row>
            <Col span={8} hidden>
              {/* hasPriv('56000051') */}
              <FormItem label="客户编号" {...formItemLayout}>
                {getFieldDecorator('custId', {
                  initialValue: formData?.CUST_ID,
                })(<Input className={styles.inputStyle} placeholder="请输入" disabled />)}
              </FormItem>
            </Col>
            <Col span={8}>
              {/* hasPriv('56000051') disabled */}
              <FormItem label="集团名称" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_NAME', {
                  initialValue: formData?.GROUP_NAME,
                  //  { validator: validateGroupName }
                  rules: [{ required: true, message: '集团名称不能为空！' }, { validator: validateGroupName }],
                })(
                  <Input
                    className={styles.inputStyle}
                    placeholder="请输入"
                    disabled={viewMode === 'view' || (viewMode === 'add' && currentFormFieldState?.GROUP_NAME?.disabled)}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="集团类型" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_TYPE', {
                  initialValue: formData?.GROUP_TYPE || '1',
                  rules: [{ required: true, message: '集团类型不能为空!' }],
                })(
                  <Select
                    placeholder="请选择"
                    onChange={e => handleGroupTypeChange(e)}
                    className={styles.inputStyle}
                    loading={ReflectionGroup?.GROUP_TYPE_LOADING}
                    disabled={viewMode === 'view'}
                  >
                    {ReflectionGroup.GROUP_TYPE.map(e => (
                      <Select.Option value={e.STATIC_KEY} key={e.STATIC_KEY}>
                        {e.STATIC_VALUE}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="集客企业类型" {...formItemLayout}>
                {getFieldDecorator('JK_GROUP_TYPE', {
                  initialValue: formData?.JK_GROUP_TYPE,
                })(<Input disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="联系电话" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_CONTACT_PHONE', {
                  initialValue: formData?.GROUP_CONTACT_PHONE,
                  rules: [{ required: true, message: '联系电话不能为空!' }, { validator: validatePhone }],
                })(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <FormItem label="集团规模" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('ENTERPRISE_SIZE_CODE', {
                  initialValue: formData?.ENTERPRISE_SIZE_CODE,
                  rules: [{ required: true, message: '集团规模不能为空!' }],
                })(
                  <Select
                    placeholder="请选择"
                    className={styles.inputStyle}
                    loading={ReflectionGroup?.ENTERPRISE_SIZE_CODE_LOADING}
                    disabled={viewMode === 'view'}
                  >
                    {ReflectionGroup.ENTERPRISE_SIZE_CODE.map(e => (
                      <Select.Option value={e.STATIC_KEY} key={e.STATIC_KEY}>
                        {e.STATIC_VALUE}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="跨省类型" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('MULTI_PROVINCE', {
                  initialValue: formData?.MULTI_PROVINCE || '1',
                  rules: [{ required: true, message: '跨省类型不能为空!' }],
                })(
                  <Select
                    placeholder="请选择"
                    className={styles.inputStyle}
                    loading={ReflectionGroup?.GROUP_SCOPE_TYPE_LOADING}
                    disabled={viewMode === 'view'}
                  >
                    {ReflectionGroup.GROUP_SCOPE_TYPE.map(e => (
                      <Select.Option value={e.STATIC_KEY} key={e.STATIC_KEY}>
                        {e.STATIC_VALUE}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="法人代表" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('JURISTIC_NAME', {
                  initialValue: formData?.JURISTIC_NAME,
                  rules: [{ required: true, message: '法人代表不能为空!' }],
                })(
                  <Input
                    className={styles.inputStyle}
                    placeholder="请输入"
                    disabled={viewMode === 'view' || currentFormFieldState?.JURISTIC_NAME?.disabled}
                  />
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <FormItem label="法人身份证号" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('JURISTIC_IDEN_NR', {
                  initialValue: formData?.JURISTIC_IDEN_NR,
                  rules: [{ required: true, message: '法人身份证号不能为空!' }],
                })(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="注册资金（万元）" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('REG_MONEY', {
                  initialValue: formData?.REG_MONEY,
                  rules: [{ required: true, message: '注册资金不能为空!' }, { validator: validateMoney }],
                })(
                  <Input
                    className={styles.inputStyle}
                    placeholder="请输入"
                    disabled={viewMode === 'view' || currentFormFieldState?.REG_MONEY?.disabled}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="集团客户级别" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_LEVEL_NEW', {
                  initialValue: groupInfo?.GROUP_LEVEL_NEW || '1',
                  rules: [{ required: true, message: '请选择集团客户级别!' }],
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view' || viewMode === 'edit'}>
                    {groupCustomerLevels.map(scene => (
                      <Option key={scene.STATIC_KEY} value={scene.STATIC_KEY}>
                        {scene.STATIC_VALUE}
                      </Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="是否升级华为云平台" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('IS_SEND_ManageOne', {
                  initialValue: '0',
                  // rules: [{ required: true, message: '是否升级华为云平台不能为空!' }],
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view'}>
                    {[].map(e => (
                      <Select.Option value={e.STATIC_KEY} key={e.STATIC_KEY}>
                        {e.STATIC_VALUE}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <FormItem label="小微企业" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('IS_SMALLMICRO', {
                  initialValue: formData?.IS_SMALLMICRO,
                  rules: [{ required: true, message: '小微企业不能为空!' }],
                })(
                  <Select
                    placeholder="请选择"
                    className={styles.inputStyle}
                    loading={ReflectionGroup?.IS_SMALLMICRO_LOADING}
                    disabled={viewMode === 'view' || currentFormFieldState?.IS_SMALLMICRO?.disabled}
                  >
                    {ReflectionGroup.IS_SMALLMICRO.map(e => (
                      <Select.Option value={e.STATIC_KEY} key={e.STATIC_KEY}>
                        {e.STATIC_VALUE}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="行业类型" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CALLING_TYPE', {
                  rules: [{ required: true, message: '请选择行业类型!' }],
                })(
                  <Select placeholder="请选择" disabled={viewMode === 'view'} className={styles.inputStyle}>
                    {industryTypes.map(type => (
                      <Option key={type.STATIC_KEY} value={type.STATIC_KEY}>
                        {type.STATIC_VALUE}
                      </Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              {/* 国标行业门类：CALLING_AREA 行业大类：CALLING_AREA_A 行业中类 CALLING_AREA_B */}
              <FormItem label="国标行业类型" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CALLING_AREA_INFO', {
                  initialValue: [],
                  rules: [
                    {
                      required: true,
                      message: '请选择国标行业类型!',
                    },
                    { validator: valiCalling(form.getFieldValue('CALLING_AREA_INFO')) },
                  ],
                })(
                  <AsyncCascader
                    title="国标行业类型"
                    loadData={getLocationIndustry}
                    placeholder="请选择国标行业类型"
                    displayRender={labels => labels.join('-')}
                    disableOption={currentFormFieldState?.CALLING_AREA_INFO?.disabled && disableOption}
                    className={styles.inputStyle}
                    allowClear={false}
                    disabledAll={viewMode === 'view'}
                  />
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <FormItem label="经营区域范围" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('ENTERPRISE_SCOPE', {
                  initialValue: '1',
                  rules: [{ required: true, message: '请选择经营区域范围!' }],
                })(
                  <Select placeholder="请选择" disabled={viewMode === 'view'} className={styles.inputStyle}>
                    {enterpriseScopes.map(e => (
                      <Option key={e.STATIC_KEY} value={e.STATIC_KEY}>
                        {e.STATIC_VALUE}
                      </Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              {/* 生产经营所在省 BUSICOMPANYID  生产经营所在地市 BUSILOCATION  生产经营所在区县BUSICOUNTYID */}
              <FormItem label="生产经营地址" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('BUSILOCATIONS', {
                  // initialValue: ['510000', '510100', '510101'], // 举例写死内蒙 ['150000']
                  rules: [
                    {
                      required: true,
                      message: '请选择生产经营地址!',
                    },
                    { validator: valiArea(form.getFieldValue('BUSILOCATIONS')) },
                  ],
                })(
                  <AsyncCascader
                    title="地址信息"
                    loadData={getLocation}
                    placeholder="请选择地址"
                    displayRender={labels => labels.join('-')}
                    className={styles.inputStyle}
                    disabledAll={viewMode === 'view'}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="生产经营具体地址" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_LICENCE_ADDRESS', {
                  initialValue: formData?.GROUP_LICENCE_ADDRESS,
                  rules: [{ required: true, message: '请输入生产经营具体地址!' }],
                })(
                  <Input
                    className={styles.inputStyle}
                    placeholder="请输入"
                    disabled={viewMode === 'view' || currentFormFieldState?.GROUP_LICENCE_ADDRESS?.disabled}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="是否升级物联网" {...formItemLayout}>
                {getFieldDecorator('syncWlwStatus', {
                  initialValue: formData?.SYNC_WLW_STATUS,
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view'}>
                    {Object.keys(ENTERPRISE_IMPORT_FLAG).map(key => (
                      <Select.Option value={key} key={key}>
                        {ENTERPRISE_IMPORT_FLAG[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            {/* 客户经理信息 */}
            <CustManager customerInfo={customerInfo} form={form} isCmiot={isCmiot} onValuesChange={onValuesChange} viewMode={viewMode} />
            {/* 归属区域  */}
            <RegionInfo form={form} onValuesChange={onValuesChange} viewMode={viewMode} />
            <Col span={8}>
              <FormItem label="年营业额（万元）" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('YEAR_GAIN', {
                  initialValue: formData?.YEAR_GAIN,
                  rules: [{ required: true, message: '年营业额（万元）不能为空!' }],
                })(<InputNumber className={styles.inputStyle} min={0} placeholder="请输入" disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <FormItem label="登记管理部门" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('REGMANADEPARTMENT', {
                  initialValue: formData?.REGMANADEPARTMENT,
                  rules: [{ required: true, message: '登记管理部门不能为空!' }],
                })(
                  <Input
                    className={styles.inputStyle}
                    placeholder="请输入"
                    disabled={viewMode === 'view' || currentFormFieldState?.REGMANADEPARTMENT?.disabled}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="同步CMIOT" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('IS_CMIOT', {
                  initialValue: formData?.IS_CMIOT === '1',
                  // valuePropName: 'checked',
                })(
                  <Switch
                    loading={cmiotLoading}
                    checked={formData?.IS_CMIOT === '1'}
                    onClick={changeToCmiot}
                    checkedChildren="是"
                    unCheckedChildren="否"
                    disabled={viewMode === 'view'}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden={openCard}>
              <FormItem label="企业员工数" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('ENT_EMPLOYEE_COUNT', {
                  initialValue: formData?.ENT_EMPLOYEE_COUNT || 100,
                  rules: [{ required: viewMode === 'edit', message: '企业员工数不能为空!' }],
                })(<InputNumber className={styles.inputStyle} min={0} placeholder="请输入" disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8} hidden={openCard}>
              <FormItem label="是否归属区重要集团" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('IMPORT_FLAG', {
                  initialValue: formData?.IMPORT_FLAG,
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view'}>
                    {Object.keys(ENTERPRISE_IMPORT_FLAG).map(key => (
                      <Select.Option value={key} key={key}>
                        {ENTERPRISE_IMPORT_FLAG[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden={openCard}>
              <FormItem label="是否战略客户" {...formItemLayout}>
                {getFieldDecorator('IS_STRATEGIC_CUSTOMER', {
                  initialValue: formData?.IS_STRATEGIC_CUSTOMER || '1',
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view'}>
                    {Object.keys(IS_BRANCH_VALUE).map(key => (
                      <Select.Option value={key} key={key}>
                        {IS_BRANCH_VALUE[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden={openCard}>
              <FormItem label="客户服务等级" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('SERV_LEVEL', {
                  initialValue: formData?.SERV_LEVEL || '4',
                })(
                  <Select
                    placeholder="请选择"
                    className={styles.inputStyle}
                    loading={ReflectionGroup?.GROUP_SERV_LEVEL_LOADING}
                    disabled={viewMode === 'view'}
                  >
                    {ReflectionGroup.GROUP_SERV_LEVEL.map(e => (
                      <Select.Option value={e.STATIC_KEY} key={e.STATIC_KEY}>
                        {e.STATIC_VALUE}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8} hidden={openCard}>
              <FormItem label="客户类型" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_CUST_TYPE', {
                  initialValue: formData?.GROUP_CUST_TYPE || '100000001',
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view'}>
                    {Object.keys(GROUP_CUST_TYPE).map(key => (
                      <Select.Option value={key} key={key}>
                        {GROUP_CUST_TYPE[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden={openCard}>
              <FormItem label="政企客户行业门类" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('buIndustry', {
                  initialValue: formData?.buIndustry,
                })(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8} hidden={openCard}>
              <FormItem label="政企客户行业类型" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('buIndustrySub', {
                  initialValue: formData?.buIndustry,
                })(
                  <Input
                    className={styles.inputStyle}
                    placeholder="请输入"
                    disabled={viewMode === 'view' || currentFormFieldState?.buIndustrySub?.disabled}
                  />
                )}
              </FormItem>
            </Col>
          </Row>
          {viewMode === 'edit' && (
            <Row>
              <Col span={8}>
                <FormItem label="集团总部编码" {...formItemLayout} className={styles.textAreaFormItem}>
                  {getFieldDecorator('PNATIONAL_GROUP_ID')(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="BOSS集团客户号" {...formItemLayout} className={styles.textAreaFormItem}>
                  {getFieldDecorator('EC_CODE')(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="直管客户编号" {...formItemLayout}>
                  {getFieldDecorator('DIRECT_CUST_ID', {
                    initialValue: formData?.DIRECT_CUST_ID,
                  })(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
                </FormItem>
              </Col>
            </Row>
          )}

          <Row>
            <Col span={8} hidden>
              <FormItem label="是否直管客户" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('IS_DIRECT', {
                  initialValue: formData?.IS_DIRECT || '0',
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view'}>
                    {Object.keys(ENTERPRISE_IMPORT_FLAG).map(key => (
                      <Select.Option value={key} key={key}>
                        {ENTERPRISE_IMPORT_FLAG[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="是否分支机构" {...formItemLayout}>
                {getFieldDecorator('IS_BRANCH', {
                  initialValue: formData?.IS_BRANCH,
                  // rules: [{ required: true, message: '是否分支机构不能为空!' }],
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view'}>
                    {Object.keys(IS_BRANCH_VALUE).map(key => (
                      <Select.Option value={key} key={key}>
                        {IS_BRANCH_VALUE[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8} hidden>
              <FormItem label="信用级别" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CREDIT_LEVEL', {
                  initialValue: formData?.CREDIT_LEVEL,
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view'}>
                    {Object.keys(GROUP_CREDIT_LEVEL).map(key => (
                      <Select.Option value={key} key={key}>
                        {GROUP_CREDIT_LEVEL[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8} hidden>
              <FormItem label="企业标准名称" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('ENT_NAME', {
                  initialValue: formData?.ENT_NAME,
                })(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="是否升级为全网集团" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('IS_SEND_BBOSS', {
                  initialValue: 1,
                  // rules: [{ validator: validateBoss(formData.EcCustLicenceInfo) }],
                })(
                  <Select placeholder="请选择" className={styles.inputStyle} disabled={viewMode === 'view'}>
                    {Object.keys(ENTERPRISE_IMPORT_FLAG).map(key => (
                      <Select.Option value={key} key={key}>
                        {ENTERPRISE_IMPORT_FLAG[key]}
                      </Select.Option>
                    ))}
                    <Select.Option value="type1">是</Select.Option>
                    {/* 其他选项 */}
                  </Select>
                )}
              </FormItem>
            </Col>
            {/* 下面是隐藏参数，请勿删除 */}
            {/* cmiot 客户经理 */}
            <Col span={8} hidden>
              <FormItem label="客户经理" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CMIOT_STAFF_NUMBER', {})(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="进出口客户" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('IS_EXPORT_AND_IMPORT', {
                  initialValue: formData?.IS_EXPORT_AND_IMPORT,
                })(<Input disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            {/* cmiot 组织机构 */}
            <Col span={8} hidden>
              <FormItem label="组织机构" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CMIOT_ORG_ID', {
                  initialValue: formData?.CMIOT_ORG_ID,
                })(<Input disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            {/* cmiot 电话 */}
            <Col span={8} hidden>
              <FormItem label="电话" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CMIOT_MOBILE_PHONE', {})(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            {/* cmiot证件地址 */}
            <Col span={8} hidden>
              <FormItem label="证件地址" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('ID_ADDR', {})(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            {/* cmiot业务附件 */}
            <Col span={8} hidden>
              <FormItem label="业务附件" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CMIOT_ATTACHMENTS')(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            {/* cmiot归属地市 */}
            <Col span={8} hidden>
              <FormItem label="归属地市" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CMIOT_REGION_ID', {
                  initialValue: formData?.CMIOT_REGION_ID,
                })(<Input disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="归属区县" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CMIOT_COUNTRY_ID', {
                  initialValue: formData?.CMIOT_COUNTRY_ID,
                })(<Input disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="cmiot归属区域" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('CmiotAreaInfo', { initialValue: [] })(<Input disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8} />
            <Col span={8} />
          </Row>
          <Row>
            {/* <Col span={8}>
              <FormItem label="生产经营所在省" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('BUSICOMPANYID', {
                  initialValue: formData?.BUSICOMPANYID,
                  rules: [{ required: true, message: '生产经营所在省不能为空!' }],
                })(
                  <Select placeholder="请选择" onChange={changeProvice} className={styles.inputStyle}>
                    {areas.map(({ AREA_NAME, AREA_CODE }) => (
                      <Select.Option value={AREA_CODE} key={AREA_CODE}>
                        {AREA_NAME}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col> */}
            {/* <Col span={8}>
              <FormItem label="生产经营所在地市" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('BUSILOCATION', {
                  initialValue: formData?.BUSILOCATION,
                  rules: [{ required: true, message: '生产经营所在地市不能为空!' }],
                })(
                  <Select placeholder="请选择" onChange={changeCity} className={styles.inputStyle}>
                    {locations.map(({ AREA_NAME, AREA_CODE }) => (
                      <Select.Option value={AREA_CODE} key={AREA_CODE}>
                        {AREA_NAME}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="生产经营所在区县" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('BUSICOUNTYID', {
                  initialValue: formData?.BUSICOUNTYID,
                  rules: [{ required: true, message: '生产经营所在区县不能为空!' }],
                })(
                  <Select placeholder="请选择" className={styles.inputStyle}>
                    {busiCounties.map(({ AREA_NAME, AREA_CODE }) => (
                      <Select.Option value={AREA_CODE} key={AREA_CODE}>
                        {AREA_NAME}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col> */}
          </Row>
        </Form>
      </div>
    </Card>
  );
};

export default connect(({ AddGroupCust, ReflectionGroup }) => ({
  currentFormFieldState: AddGroupCust.currentFormFieldState,
  formData: AddGroupCust.currentForm,
  ReflectionGroup,
}))(Form.create()(BaseInfo));
