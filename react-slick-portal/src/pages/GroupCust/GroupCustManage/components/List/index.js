import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Card, Form, Row, Col, Icon, Input, Button, Divider, Select, Dropdown, Menu, message, DatePicker, Modal } from 'antd';
import { connect } from 'dva';
import { useAntdTable } from '@umijs/hooks';
import moment from 'moment';
import classNames from 'classnames';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight, Portal } from '@/utils/utils';
import CustomSpace from '@/components/CustomSpace';
import style from './index.less';
import { infoKeys, queryFields } from '@/pages/GroupCust/GroupCustManage/const';
import {
  // checkUserOperationPermissions,
  // eslint-disable-next-line no-unused-vars
  // getList,
  // eslint-disable-next-line no-unused-vars
  // checkUserOperationPermissions,
  operGroupEnterprise,
  queryDisticts,
  queryQxArea,
  groupHalfLevel<PERSON><PERSON>,
  editPer<PERSON>pi,
  editFormPermission,
  updateGroupHalfYear, queryEnterpriseByOrgaEnterpriseId,
} from '@/pages/GroupCust/GroupCustManage/services';
import { CUSTGROUP_CLASSID, GROUP_STATUS, GROUP_TYPE } from '@/utils/consts';
import { disableEndDate, disableStartDate } from '@/utils/validator';
import VaultApprovalPop from '@/components/VaultApprovalPop';
import { queryIsVault } from '@/services/vaultApproval';
import request from '@/utils/request';
import {
  exportFileApi,
} from '@/pages/GroupCust/GroupCustView/components/OrderInfo/components/OrderList/components/OrderInstance/services';

/* const cellStyle = {
  maxWidth: 200,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  cursor: 'pointer',
}; */

const getTableData = ({ current, pageSize, ...other }) => {
  const { groupQueryType, ...restParams } = other;
  let apiName = '';

  switch (groupQueryType) {
    // 集团用户标识
    case 'subscriberInsId':
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseBySubscriberInsId.do';
      break;
    // 集团服务号码查询
    case 'accessNum':
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByAccessNum.do';
      break;
    // 个人号码查询
    case 'personNum':
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByPersonNum.do';
      break;
    // 宽带账号
    case 'widenetAccessNum':
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByWidenetAccessNum.do';
      break;
    // 集团客户经理编码
    case 'operatorCode':
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByOperatorCode.do';
      break;
    // 集团编码
    case 'groupId':
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByGroupId.do';
      break;
    // 集团账号
    case 'acctId':
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByAcctId.do';
      break;
    // 集团名称查询
    case 'groupName':
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByGroupName.do';
      break;
    // 成员服务号码
    case 'mebAccessNum':
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByMebAccessNum.do';
      break;
    // 默认列表查询
    default:
      apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByOperatorCode.do';
      break;
  }
  return request(apiName, {
    method: 'POST',
    data: {
      ...restParams,
      pageFlag: '1',
      pageInfo: {
        currentPage: current,
        pageSize,
      },
    },
  }).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
      const filterArr = resultObject?.rspParam?.busiInfo?.outData; // 过滤为 4 的
      return {
        total: parseInt(resultObject?.rspParam?.pageInfo?.recordCount, 10),
        data: filterArr,
      };
    }
    message.error(resultMsg);

    return {
      total: 0,
      data: [],
    };
  });
};

const InfoRow = React.memo(({ label, value }) => (
  <Col span={24}>
    <Row justify="center">
      <Col span={12} className="label">
        {label}
      </Col>
      <Col span={12} className="value">
        {value}
      </Col>
    </Row>
  </Col>
));
const Index = ({
  size: { height },
  form,
  dispatch,
  vaultApproval,
  user: {
    userInfo: { externalUserInfos },
  },
  ...otherProps
}) => {
  // eslint-disable-next-line no-unused-vars
  const { goToStep } = otherProps;
  const [exportLoading, setExportLoading] = useState(false);
  const [expandedRows, setExpandedRows] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [dsArea, setDsArea] = useState([]);
  const [qxArea, setQxArea] = useState([]);
  const [groupHalfLevel, setGroupHalfLevel] = useState([]);
  const tableRef = useRef(null);
  // card-body除rowHeight
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const [halfYearModalVisible, setHalfYearModalVisible] = useState(false);
  const [selectedHalfYear, setSelectedHalfYear] = useState(undefined);
  const [currentGroupId, setCurrentGroupId] = useState(null);
  const [groupInfoDetail, setGroupInfoDetail] = useState(null);


  const { getFieldDecorator } = form;
  const {
    tableProps,
    refresh,
    search: { submit, reset },
  } = useAntdTable(({ filters, sorter, ...other }) => getTableData(other), {
    defaultPageSize: size,
    form,
  });
  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    // eslint-disable-next-line no-console
    console.log('tableProps', tableProps);
  }, [tableProps]);

  // eslint-disable-next-line no-unused-vars
  const queryCity = async () => {
    const resp = await queryDisticts();
    const { resultCode, resultObject } = resp;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo.length !== 0) {
      setDsArea(resultObject?.rspParam?.busiInfo?.resultInfo);
    } else {
      message.warning('获取地市失败');
    }
  };

  // 半年集团映射
  const queryHalf = async () => {
    const resp = await groupHalfLevelApi();
    if (resp.resultCode === 'TRUE') {
      const result = resp.resultObject.rspParam.busiInfo.outData.reduce((acc, item) => {
        acc[item.STATIC_KEY] = item.STATIC_VALUE;
        return acc;
      }, {});
      setGroupHalfLevel(result);
    }
  };

  useEffect(() => {
    // queryCity(); // 获取地市
    queryHalf(); // 半年级别映射
  }, []);

  // eslint-disable-next-line no-unused-vars
  const handleSelectDs = () => {
    if (qxArea.length === 0) {
      message.warning('请选择地市');
    }
  };

  // 获取区县
  // eslint-disable-next-line no-unused-vars
  const handleQxArea = async e => {
    const resp = await queryQxArea(e);
    const { resultCode, resultObject } = resp;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo.length !== 0) {
      setQxArea(resultObject?.rspParam?.busiInfo?.resultInfo);
    } else {
      message.warning('获取区县失败');
    }
  };

  /**
   * 编辑 新增 详情
   * @param targetRow
   * @param viewMode view 详情 edit 编辑 add 新增
   */
  const openDrawer = (targetRow, viewMode) => {
    goToStep(2, {
      groupCode: targetRow.GROUP_ID,
      viewMode,
      groupName: targetRow.GROUP_NAME,
      editGroupId: targetRow.ORGA_ENTERPRISE_ID,
    });
  };

  const editPermission = async targetRow => {
    // 2.是否在审批状态 GROUP_STATUS
    const resp = await editFormPermission(targetRow.GROUP_ID);
    const { resultCode, resultObject } = resp;
    if (resultCode === 'TRUE' && resultObject.message === 'success') {
      const stateText = GROUP_STATUS[targetRow.GROUP_STATUS];
      if (stateText === '沉默') {
        message.error('错误信息!,暂停状态无法修改集团信息');
        return;
      }
    } else {
      message.error(resultObject.message);
      return;
    }
    openDrawer(targetRow, 'edit');
  };

  // eslint-disable-next-line no-unused-vars
  const onEdit = async targetRow => {
    if (targetRow === 'add') {
      goToStep(2, { viewMode: 'add' });
      return;
    }
    const params = {
      OPER_TYPE: 'edit',
      MGMT_DISTRICT: targetRow.MGMT_DISTRICT,
      MGMT_COUNTY: targetRow.MGMT_COUNTY,
      CUST_MGR_ID: targetRow.CUST_MGR_ID,
    };
    // 1.权限校验
    const resp = await editPerApi(params);
    if (resp.resultCode === 'TRUE' && resp.resultObject.result) {
      editPermission(targetRow);
    } else {
      message.error('您无权限修改此集团');
    }
  };

  const handleSubmit = async () => {
    form.validateFields(err => {
      if (!err) {
        submit();
      }
    });
  };

  /**
   * 导出
   */
  const handleExport = async (vaultIsPass = false) => {
    if (!vaultIsPass) {
      const params = {
        ACT_URL: 'GroupEnterpriseController?action=exportGroupEnterpriseExcelFile',
      };
      const resp = await queryIsVault(params);
      if (resp.resultCode === 'TRUE' && resp.resultObject.GOLDBANK_STATE !== 'false') {
        // 直接进入弹窗2 情况
        if (resp.resultObject.appOperJKStatus.next === 'auth') {
          const approvalCreate = {
            isShowSecond: true,
            isTimesPass: false,
            approvalCreate: resp.resultObject.appOperJKStatus,
            functionString: 'export', // 函数回调判断标识
          };
          dispatch({
            type: 'vaultApproval/VaultUpData',
            payload: { approvalCreate },
          });
          return;
        }
        // setExportLoading(false);
        const approvalOptionParams = {
          isShowfirst: true,
          isTimesPass: false,
          approvalOption: resp.resultObject,
          functionString: 'export', // 函数回调判断标识
        };
        dispatch({
          type: 'vaultApproval/VaultUpData',
          payload: { approvalOptionParams },
        });
        return;
      }
      if (!resp.success) {
        // setExportLoading(false);
        message.error(resp.resultMsg);
        return;
      }
    }
    // 导出逻辑
    form.validateFields((err, values) => {
      if (!err) {
        const { groupQueryType, ...other } = values;
        setExportLoading(true);
        const sendParams = {
          sceneName: 'GroupEnterprise',
          type: groupQueryType,
          ...other,
        };
        // eslint-disable-next-line
        console.log(sendParams);
        exportFileApi(sendParams).then(resp => {
          setExportLoading(false);
          if (resp.resultCode === 'TRUE' && resp.resultObject) {
            message.success(resp.resultObject.message);
          } else {
            message.error(resp.resultMsg);
          }
        });
        // exportExcelFile({
        //   url: 'portal/PortalExportController/createExportTask.do',
        //   title: `${moment().format('YYYY-MM-DD HH-mm-ss')}`,
        //   sendParams: {
        //     type: groupQueryType,
        //     ...other,
        //   },
        //   setLoading: setExportLoading,
        // });
      }
    });
  };

  /**
   * 渲染嵌套内容
   * @param record
   * @returns {Element}
   */
  const expandedRowRender = (record, index, indent, expanded) => (
    <div className={`${style.expandedRowRender} ${expanded ? style.visible : style.hidden}`}>
      <Row>
        {infoKeys.map((column, colIdx) => (
          <Col key={colIdx} span={8}>
            <Row type="flex" gutter={[0, 12]}>
              {column.map(({ label, key, options }) => {
                let value = record[key];
                if (options) {
                  value = options.find(item => item.value === value)?.format;
                }
                return <InfoRow label={`${label}`} value={value} />;
              })}
            </Row>
          </Col>
        ))}
      </Row>
    </div>
  );

  /**
   * 修改集团半年级别
   * @param record
   */
  const updateGroupHalfYearLevel = async record => {
    setCurrentGroupId(record.GROUP_ID);
    const groupInfoRes = await queryEnterpriseByOrgaEnterpriseId(record);
    if (groupInfoRes.resultCode === 'TRUE') {
      setGroupInfoDetail(groupInfoRes);
      setSelectedHalfYear(groupInfoRes.resultObject?.GROUP_HALF_LEVEL);
    } else {
      message.error(groupInfoRes.resultMsg || '查询集团详情异常');
    }
    setHalfYearModalVisible(true);
  };

  /**
   * 处理半年级别更新
   */
  const handleHalfYearUpdate = async () => {
    if (!selectedHalfYear) {
      message.error('请选择半年级别');
      return;
    }

    try {
      const response = await updateGroupHalfYear({
        GROUP_ID: currentGroupId,
        ORGA_ENTERPRISE_ID: groupInfoDetail.resultObject?.ORGA_ENTERPRISE_ID,
        GROUP_HALF_LEVEL: selectedHalfYear,
      });

      if (response.resultCode === 'TRUE' && response.resultObject === 'true') {
        message.success('修改半年级别成功');
        setHalfYearModalVisible(false);
        setSelectedHalfYear(undefined);
        setGroupInfoDetail(null);
        refresh(); // Refresh the table data
      } else {
        message.error(response.resultMsg || '修改半年级别失败');
      }
    } catch (error) {
      message.error('修改半年级别失败');
    }
  };

  /**
   * 更多操作
   * @param record
   * @param oper pause 暂停  recover 恢复 cancel 注销 del 删除
   */
  const handleOpera = async (record, oper) => {
    let textOper = '';
    if (oper === 'pause') {
      textOper = '暂停';
    }
    if (oper === 'recover') {
      textOper = '恢复';
    }
    if (oper === 'cancel') {
      textOper = '注销';
    }
    if (oper === 'del') {
      textOper = '删除';
    }
    const params = {
      OPER_TYPE: oper,
      MGMT_DISTRICT: record.MGMT_DISTRICT,
      MGMT_COUNTY: record.MGMT_COUNTY,
      CUST_MGR_ID: record.CUST_MGR_ID,
    };
    // 1.权限校验
    const resp = await editPerApi(params);
    if (resp.resultCode === 'TRUE' && !resp.resultObject.result) {
      message.error(`该用户无权限对该集团发起${textOper}操作`);
      return;
    }
    let opraName = '';
    const param = {
      operInfo: {
        ORGA_ENTERPRISE_ID: record.ORGA_ENTERPRISE_ID,
        GROUP_ID: record.GROUP_ID,
      },
    };
    if (oper === 'del') {
      param.operType = '6';
      param.operInfo = {
        ...param.operInfo,
        CREATE_OP_ID: '',
        CREATE_OP_NAME: '',
        CHECK_OP_ID: '',
        CHECK_OP_NAME: '',
      };
      opraName = '删除';
    } else if (oper === 'pause') {
      param.operType = '3';
      opraName = '暂停';
    } else if (oper === 'recover') {
      param.operType = '4';
      opraName = '恢复';
    } else if (oper === 'cancel') {
      param.operType = '5';
      opraName = '注销';
    }
    Modal.confirm({
      title: `确定执行${opraName}操作吗`,
      onOk() {
        operGroupEnterprise(param).then(res => {
          const { resultCode, resultMsg } = res;
          if (resultCode !== 'TRUE') {
            message.error(resultMsg);
          } else {
            message.success(resultMsg);
            refresh();
          }
        });
      },
    });
  };
  // 展开嵌套内容
  // eslint-disable-next-line no-unused-vars
  const handleExpand = key => {
    const newExpandedRows = [...expandedRows];
    if (newExpandedRows.includes(key)) {
      newExpandedRows.splice(newExpandedRows.indexOf(key), 1);
    } else {
      newExpandedRows.push(key);
    }
    setExpandedRows(newExpandedRows);
  };

  /* 跳转客户视图 */
  const openUrl = record => {
    // 将数字转为字符串
    const numberString = record.GROUP_ID.toString();
    // 将字符串进行Base64编码
    const encoded = btoa(numberString);

    Portal.open(`/GroupCust/GroupCustView?groupId=${encoded}`);
  };

  const sameSysUserId = useMemo(
    () =>
      form.getFieldValue('operatorCode') ===
        externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000')?.externalUserId || '',
    [form, externalUserInfos, form.getFieldValue('operatorCode')]
  );

  /* 子菜单 */
  const itemMenu = record => (
    <Menu>
      <Menu.Item>
        <a onClick={() => handleOpera(record, 'pause')}>暂停</a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={() => handleOpera(record, 'recover')}>恢复</a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={() => handleOpera(record, 'cancel')}>注销</a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={() => handleOpera(record, 'del')}>删除</a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={() => updateGroupHalfYearLevel(record)}>半年级别修改</a>
      </Menu.Item>
    </Menu>
  );
  const renderMoreConditions1 = () => {
    const groupQueryType = form.getFieldValue('groupQueryType');
    switch (groupQueryType) {
      // 查询条件-集团用户标识
      case 'subscriberInsId':
        return (
          <>
            <Col span={6}>
              <Form.Item label="集团用户标识">
                {getFieldDecorator('subscriberInsId', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );
      // 查询条件-集团服务号码
      case 'accessNum':
        return (
          <>
            <Col span={6}>
              <Form.Item label="服务号码">
                {getFieldDecorator('accessNum', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );
      // 查询条件-个人号码
      case 'personNum':
        return (
          <>
            <Col span={6}>
              <Form.Item label="服务号码">
                {getFieldDecorator('accessNum', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );

      // 查询条件-宽带账号
      case 'widenetAccessNum':
        return (
          <>
            <Col span={6}>
              <Form.Item label="宽带账号">
                {getFieldDecorator('accessNum', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );

      // 查询条件-集团客户经理编码
      case 'operatorCode':
        return (
          <Col span={6}>
            <Form.Item label="经理工号">
              {getFieldDecorator('operatorCode', {
                initialValue: externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000')?.externalUserId || '',
                rules: [{ required: true, message: '请输入' }],
              })(
                <Input
                  disabled
                  className={classNames(style.inputWithLink, {
                    [style.same]: sameSysUserId,
                    [style.notSame]: !sameSysUserId,
                  })}
                  allowClear
                />
              )}
            </Form.Item>
          </Col>
        );

      // 查询条件-集团编码
      case 'groupId':
        return (
          <Col span={6}>
            <Form.Item label="编码">
              {getFieldDecorator('groupId', {
                rules: [{ required: true, message: '请输入' }],
              })(<Input allowClear />)}
            </Form.Item>
          </Col>
        );

      // 查询条件-集团账号
      case 'acctId':
        return (
          <Col span={6}>
            <Form.Item label="集团账号">
              {getFieldDecorator('acctId', {
                rules: [{ required: true, message: '请输入' }],
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
        );

      // 查询条件-集团名称
      case 'groupName':
        return (
          <>
            <Col span={6}>
              <Form.Item label="名称">
                {getFieldDecorator('groupName', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );

      // 查询条件-成员服务号码
      case 'mebAccessNum':
        return (
          <>
            <Col span={6}>
              <Form.Item label="服务号码">
                {getFieldDecorator('accessNum', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );
      default:
        return null;
    }
  };

  // eslint-disable-next-line no-unused-vars
  const renderMoreConditions2 = () => {
    const groupQueryType = form.getFieldValue('groupQueryType');
    switch (groupQueryType) {
      case 'groupName':
        /* 查询条件-集团名称 */
        return (
          <>
            <Row gutter={24}>
              {/* todo:权限:managerPriv */}
              <Col span={6}>
                <Form.Item label="客户经理">{getFieldDecorator('groupManagerId')(<Input allowClear />)}</Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="起始创建时间">
                  {getFieldDecorator('createStartTime')(
                    <DatePicker
                      format="YYYY-MM-DD"
                      allowClear
                      style={{ width: '100%' }}
                      disabledDate={disableStartDate(form.getFieldValue('createEndTime'))}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="截止创建时间">
                  {getFieldDecorator('createEndTime')(
                    <DatePicker
                      format="YYYY-MM-DD"
                      allowClear
                      style={{ width: '100%' }}
                      disabledDate={disableEndDate(form.getFieldValue('createStartTime'))}
                    />
                  )}
                </Form.Item>
              </Col>
              {/* todo:权限:managerPriv && cityPriv */}
              <Col span={6}>
                <Form.Item label="归属地市">
                  {getFieldDecorator('regionCode')(
                    <Select allowClear>
                      <Select.Option key={1}>全部</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              {/* todo:权限:managerPriv && countyPriv */}
              <Col span={6}>
                <Form.Item label="归属区县">
                  {getFieldDecorator('countyCode')(
                    <Select allowClear>
                      <Select.Option key={1}>全部</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
              {/* todo:权限:managerPriv && regionDetailPriv */}
              <Col span={6}>
                <Form.Item label="归属片区">
                  {getFieldDecorator('chipCode')(
                    <Select allowClear>
                      <Select.Option key={1}>全部</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="集团类型">
                  {getFieldDecorator('groupType')(
                    <Select allowClear>
                      {Object.keys(GROUP_TYPE).map(key => (
                        <Select.Option key={key} value={key}>
                          {GROUP_TYPE[key]}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="集团级别">
                  {getFieldDecorator('groupLevel')(
                    <Select allowClear>
                      {Object.keys(CUSTGROUP_CLASSID).map(key => (
                        <Select.Option key={key} value={key}>
                          {GROUP_TYPE[key]}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={6}>
                <Form.Item label="集团状态">
                  {getFieldDecorator('groupStatus')(
                    <Select allowClear>
                      {Object.keys(GROUP_STATUS).map(key => (
                        <Select.Option key={key} value={key}>
                          {GROUP_TYPE[key]}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="VPMN编码">{getFieldDecorator('vpmnId')(<Input allowClear />)}</Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="产品名称">{getFieldDecorator('offerName')(<Input allowClear />)}</Form.Item>
              </Col>
              <Col span={6} />
            </Row>
          </>
        );

      case 'groupProd':
        /* 查询条件-集团产品 */
        return (
          <Row gutter={24}>
            {/* disInfos */}
            <Col span={6}>
              <Form.Item label="产品订购城市">
                {getFieldDecorator('prodRegion')(
                  <Select allowClear>
                    {Object.keys(GROUP_TYPE).map(key => (
                      <Select.Option key={key} value={key}>
                        {GROUP_TYPE[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={6} />
            <Col span={6} />
          </Row>
        );

      default:
        return null;
    }
  };

  useEffect(() => {
    renderMoreConditions1();
    // renderMoreConditions2();
  }, [form.getFieldValue('groupQueryType')]);

  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  // 金库成功后回调
  const handleVaultSuccess = (action, vaultIsPass) => {
    if (action === 'export') {
      // 导出功能
      handleExport(vaultIsPass);
    }
  };
  const handleChangeQueryType = () => {
    // 获取所有字段名
    const fields = form.getFieldsValue();
    const fieldNames = Object.keys(fields);
    // 排除字段[查询类型]
    const otherFields = fieldNames.filter(name => name !== 'groupQueryType');
    form.resetFields(otherFields);
  };
  return (
    <>
      <Card title="集团客户管理" className="cute" style={{ minHeight: height }} bordered>
        <Form className="flow fix-label">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label="查询类型">
                {getFieldDecorator('groupQueryType', {
                  initialValue: 'operatorCode', // 默认查询当前客户经理下的集团客户
                })(
                  <Select placeholder="请选择" onChange={handleChangeQueryType}>
                    {queryFields.map(({ disable, key, label }) => (
                      <Select.Option disabled={disable} key={key} value={key}>
                        {label}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            {renderMoreConditions1()}
            <Col span={12} className="text-right">
              <Button type="primary" className={style.buttonStyle1} style={{ marginRight: '10px' }} onClick={handleSubmit} loading={tableProps.loading}>
                查询
              </Button>
              <Button type="primary" className={style.buttonStyle1} onClick={() => onEdit('add')}>
                新增
              </Button>
              <Button className={`margin-left ${style.buttonStyle}`} onClick={reset}>
                重置
              </Button>
              <Button
                disabled={exportLoading}
                loading={exportLoading}
                type="default"
                className={`margin-left ${style.buttonStyle}`}
                onClick={() => handleExport()}
              >
                导出
              </Button>
              {/* <Button type="primary" className="margin-left" onClick={() => openDrawer({}, 'add')}>
                新增
              </Button> */}
            </Col>
          </Row>
          {/* {renderMoreConditions2()} */}
        </Form>
        <SlickTable
          style={{ marginTop: 20 }}
          ref={tableRef}
          scroll={{ x: 'max-content' }}
          expandedRowKeys={expandedRows}
          expandIcon={() => null} // 移除默认的第一列展开图标
          expandIconAsCell={false} // 设置展开图标在表格中不显示为列
          expandIconColumnIndex={1} // 设置展开图标的列索引为1（即序号列）
          rowKey={(record, idx) => `${record.ORGA_ENTERPRISE_ID}_${idx}`} // 使用ORGA_ENTERPRISE_ID和索引作为唯一键
          rowClassName={record => (expandedRows.includes(record.index) ? `${style.expandedRow}` : '')}
          {...restTableProps}
          expandedRowRender={expandedRowRender}
          data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
          columns={[
            {
              title: '序号',
              dataIndex: 'index',
              key: 'index',
              render: (text, record, index) => (
                <div>
                  <Icon
                    style={{ marginRight: '8px' }}
                    type={expandedRows.includes(record.GROUP_ID) ? 'caret-down' : 'caret-right'}
                    onClick={() => handleExpand(record.GROUP_ID)}
                  />
                  {index + 1}
                </div>
              ),
            },
            {
              title: '集团编码',
              dataIndex: 'GROUP_ID',
              key: 'groupId',
              align: 'center',
            },
            {
              title: '集团名称',
              dataIndex: 'GROUP_NAME',
              key: 'groupName',
              align: 'center',
            },
            {
              title: '集团半年级别',
              key: 'groupLevel',
              width: 120,
              align: 'center',
              render: (_, record) => (
                <span className={style.groupLevel}>{groupHalfLevel[record.GROUP_HALF_LEVEL] || `${record.GROUP_HALF_LEVEL}级`}</span>
              ),
            },
            {
              title: '集团状态',
              dataIndex: 'GROUP_STATUS',
              key: 'groupStatus',
              align: 'center',
              width: 100,
              render: (_, record) => {
                const status = ['1', '2'].includes(record.GROUP_STATUS) ? record.GROUP_STATUS : '0';
                const className = `${style.groupStatus} ${style[`status${status}`]}`;
                return <span className={className}>{GROUP_STATUS[record.GROUP_STATUS]}</span>;
              },
            },
            {
              title: '客户经理',
              dataIndex: 'STAFF_NAME',
              key: 'customerManager',
              align: 'center',
            },
            {
              title: '客户经理电话',
              dataIndex: 'BILL_ID',
              key: 'billId',
              width: 150,
              align: 'center',
            },

            /* {
              title: '名单类型',
              dataIndex: 'listType',
              key: 'listType',
              render: listType => {
                let result = { className: style.default, text: '无' };
                if (listType === 'red') {
                  result = { className: style.red, text: '红名单' };
                }
                if (listType === 'black') {
                  result = { className: style.black, text: '黑名单' };
                }

                return <span className={`${style.listType} ${result.className}`}>{result.text}</span>;
              },
            }, */
            {
              title: '创建日期',
              dataIndex: 'CREATE_DATE',
              align: 'center',
              key: 'createDate',
              width: 180,
              render: val => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
            },
            {
              title: '操作',
              align: 'center',
              key: 'action',
              fixed: 'right',
              width: 230,
              render: (_, record) => {
                const buttons = [
                  <a onClick={() => openUrl(record)}>客户视图</a>,
                  // <a onClick={() => openDrawer(record, 'view')}>详情</a>,
                  <a onClick={() => onEdit(record)}>修改</a>,
                  <Dropdown overlay={itemMenu(record)} trigger={['click']}>
                    <a>
                      更多 <Icon type="down" />
                    </a>
                  </Dropdown>,
                ];
                return (
                  <CustomSpace align="center" wrap split={<Divider type="vertical" />}>
                    {buttons.map(item => item)}
                  </CustomSpace>
                );
              },
            },
          ]}
        />
      </Card>
      {/* 金库审批弹窗组件 */}
      <VaultApprovalPop onVaultSuccess={(actionName, vaultIsPass) => handleVaultSuccess(actionName, vaultIsPass)} />
      <Modal
        title="半年级别修改"
        visible={halfYearModalVisible}
        onOk={handleHalfYearUpdate}
        onCancel={() => {
          setHalfYearModalVisible(false);
          setSelectedHalfYear(undefined);
          setGroupInfoDetail(null);
        }}
        okText="确定"
        cancelText="取消"
      >
        <Form layout="inline">
          <Form.Item label="半年级别" style={{ marginBottom: 0 }}>
            <Select
              value={selectedHalfYear}
              onChange={value => setSelectedHalfYear(value)}
              placeholder="请选择半年级别"
              style={{ width: 200 }}
            >
              {Object.entries(groupHalfLevel).map(([key, value]) => (
                <Select.Option key={key} value={key}>
                  {value}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default connect(({ login, setting, vaultApproval }) => ({
  size: setting.size,
  vaultApproval,
  user: login.user,
}))(Form.create()(Index));
