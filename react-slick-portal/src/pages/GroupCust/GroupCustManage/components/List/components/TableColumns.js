import React from 'react';
import { Icon } from 'antd';
import moment from 'moment';
import { GROUP_STATUS } from '@/utils/consts';
import TableActions from './TableActions';
import style from '../index.less';

// 创建表格列配置
export const createTableColumns = ({
  expandedRows,
  groupHalfLevel,
  onExpand,
  onViewCustomer,
  onEdit,
  onOperate,
  onUpdateHalfYear,
}) => [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    render: (text, record, index) => (
      <div>
        <Icon
          style={{ marginRight: '8px', cursor: 'pointer' }}
          type={expandedRows.includes(record.GROUP_ID) ? 'caret-down' : 'caret-right'}
          onClick={() => onExpand(record.GROUP_ID)}
        />
        {index + 1}
      </div>
    ),
  },
  {
    title: '集团编码',
    dataIndex: 'GROUP_ID',
    key: 'groupId',
    align: 'center',
    width: 120,
  },
  {
    title: '集团名称',
    dataIndex: 'GROUP_NAME',
    key: 'groupName',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '集团半年级别',
    key: 'groupLevel',
    width: 120,
    align: 'center',
    render: (_, record) => (
      <span className={style.groupLevel}>
        {groupHalfLevel[record.GROUP_HALF_LEVEL] || `${record.GROUP_HALF_LEVEL}级`}
      </span>
    ),
  },
  {
    title: '集团状态',
    dataIndex: 'GROUP_STATUS',
    key: 'groupStatus',
    align: 'center',
    width: 100,
    render: (_, record) => {
      const status = ['1', '2'].includes(record.GROUP_STATUS) ? record.GROUP_STATUS : '0';
      const className = `${style.groupStatus} ${style[`status${status}`]}`;
      return <span className={className}>{GROUP_STATUS[record.GROUP_STATUS]}</span>;
    },
  },
  {
    title: '客户经理',
    dataIndex: 'STAFF_NAME',
    key: 'customerManager',
    align: 'center',
    width: 120,
  },
  {
    title: '客户经理电话',
    dataIndex: 'BILL_ID',
    key: 'billId',
    width: 150,
    align: 'center',
  },
  {
    title: '创建日期',
    dataIndex: 'CREATE_DATE',
    align: 'center',
    key: 'createDate',
    width: 180,
    render: val => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
  },
  {
    title: '操作',
    align: 'center',
    key: 'action',
    fixed: 'right',
    width: 230,
    render: (_, record) => (
      <TableActions
        record={record}
        onViewCustomer={onViewCustomer}
        onEdit={onEdit}
        onOperate={onOperate}
        onUpdateHalfYear={onUpdateHalfYear}
      />
    ),
  },
];
