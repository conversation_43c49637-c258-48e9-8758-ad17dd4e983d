import React from 'react';
import { Form, Col, Input, Select } from 'antd';
import classNames from 'classnames';
import style from '../index.less';

// 查询条件配置
const QUERY_CONDITIONS = {
  subscriberInsId: {
    label: '集团用户标识',
    field: 'subscriberInsId',
    component: Input,
    rules: [{ required: true, message: '请输入' }],
  },
  accessNum: {
    label: '服务号码',
    field: 'accessNum',
    component: Input,
    rules: [{ required: true, message: '请输入' }],
  },
  personNum: {
    label: '服务号码',
    field: 'accessNum',
    component: Input,
    rules: [{ required: true, message: '请输入' }],
  },
  widenetAccessNum: {
    label: '宽带账号',
    field: 'accessNum',
    component: Input,
    rules: [{ required: true, message: '请输入' }],
  },
  groupId: {
    label: '编码',
    field: 'groupId',
    component: Input,
    rules: [{ required: true, message: '请输入' }],
  },
  acctId: {
    label: '集团账号',
    field: 'acctId',
    component: Input,
    rules: [{ required: true, message: '请输入' }],
    placeholder: '请输入',
  },
  groupName: {
    label: '名称',
    field: 'groupName',
    component: Input,
    rules: [{ required: true, message: '请输入' }],
  },
  mebAccessNum: {
    label: '服务号码',
    field: 'accessNum',
    component: Input,
    rules: [{ required: true, message: '请输入' }],
  },
};

// 客户经理查询条件组件
const OperatorCodeCondition = ({ getFieldDecorator, externalUserInfos, sameSysUserId }) => (
  <Col span={6}>
    <Form.Item label="经理工号">
      {getFieldDecorator('operatorCode', {
        initialValue: externalUserInfos.find(item => 
          item.systemNbr === 'CRM' && item.defaultFlag === '1000'
        )?.externalUserId || '',
        rules: [{ required: true, message: '请输入' }],
      })(
        <Input
          disabled
          className={classNames(style.inputWithLink, {
            [style.same]: sameSysUserId,
            [style.notSame]: !sameSysUserId,
          })}
          allowClear
        />
      )}
    </Form.Item>
  </Col>
);

// 通用查询条件组件
const GenericCondition = ({ getFieldDecorator, config }) => {
  const { label, field, component: Component, rules, placeholder, ...otherProps } = config;
  
  return (
    <Col span={6}>
      <Form.Item label={label}>
        {getFieldDecorator(field, { rules })(
          <Component 
            allowClear 
            placeholder={placeholder}
            {...otherProps}
          />
        )}
      </Form.Item>
    </Col>
  );
};

// 主查询条件组件
const QueryConditions = ({ 
  groupQueryType, 
  getFieldDecorator, 
  externalUserInfos, 
  sameSysUserId 
}) => {
  if (groupQueryType === 'operatorCode') {
    return (
      <OperatorCodeCondition 
        getFieldDecorator={getFieldDecorator}
        externalUserInfos={externalUserInfos}
        sameSysUserId={sameSysUserId}
      />
    );
  }

  const config = QUERY_CONDITIONS[groupQueryType];
  if (!config) return null;

  return (
    <GenericCondition 
      getFieldDecorator={getFieldDecorator}
      config={config}
    />
  );
};

export default QueryConditions;
