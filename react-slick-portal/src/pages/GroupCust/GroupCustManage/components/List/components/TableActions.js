import React from 'react';
import { Dropdown, Menu, Icon, Divider } from 'antd';
import CustomSpace from '@/components/CustomSpace';

// 操作类型配置
const OPERATION_TYPES = {
  pause: { text: '暂停', operType: '3' },
  recover: { text: '恢复', operType: '4' },
  cancel: { text: '注销', operType: '5' },
  del: { text: '删除', operType: '6' },
};

// 更多操作菜单
const MoreActionsMenu = ({ record, onOperate, onUpdateHalfYear }) => (
  <Menu>
    {Object.entries(OPERATION_TYPES).map(([key, { text }]) => (
      <Menu.Item key={key}>
        <a onClick={() => onOperate(record, key)}>{text}</a>
      </Menu.Item>
    ))}
    <Menu.Item key="halfYear">
      <a onClick={() => onUpdateHalfYear(record)}>半年级别修改</a>
    </Menu.Item>
  </Menu>
);

// 表格操作列组件
const TableActions = ({ 
  record, 
  onViewCustomer, 
  onEdit, 
  onOperate, 
  onUpdateHalfYear 
}) => {
  const buttons = [
    <a key="view" onClick={() => onViewCustomer(record)}>客户视图</a>,
    <a key="edit" onClick={() => onEdit(record)}>修改</a>,
    <Dropdown 
      key="more"
      overlay={
        <MoreActionsMenu 
          record={record}
          onOperate={onOperate}
          onUpdateHalfYear={onUpdateHalfYear}
        />
      } 
      trigger={['click']}
    >
      <a>
        更多 <Icon type="down" />
      </a>
    </Dropdown>,
  ];

  return (
    <CustomSpace align="center" wrap split={<Divider type="vertical" />}>
      {buttons}
    </CustomSpace>
  );
};

export default TableActions;
export { OPERATION_TYPES };
