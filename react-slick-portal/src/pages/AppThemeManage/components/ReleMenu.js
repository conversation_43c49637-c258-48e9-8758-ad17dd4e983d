import React, { useState, useEffect, createContext ,useRef} from 'react';
import { connect } from 'dva';
import { Row, Col, Card, Form, Input, Icon, Button, Table, Modal, message,Divider, Select,Spin,Avatar,Checkbox,Radio } from 'antd';
import SlickTable from '@/components/SlickTable';
import useSlickTable from '@/hooks/UseSlickTable';
import Cropper from 'react-cropper';
import SlickUpload from '@/components/SlickUpload';
import styles from '../styles.less';
import AddReleMenu from './AddReleMenu'
import PubSub from 'pubsub-js';
import request from '@/utils/request';
import { result } from 'lodash';
const Search = Input.Search;
const InputGroup = Input.Group;
const { Option } = Select;
const CheckboxGroup = Checkbox.Group;
const RadionGroup=Radio.Group;
function ReleMenu(props) {

  const systemEl = useRef();
  const [flag,setFlag]=useState(false)

  //图标列表
  const [iconList,setIconList]=useState([])
  const [selectRows, setSelectRows] = useState([]);
  const [params, setParams] = useState({
    themeCode:props.tableData.themeCode,
    filterVal: '',
  });
  const [actionType,setActionType]=useState("");
  const [modalName,setModalName]=useState("");
  const [currentId,setCurrentId]=useState("");

  const handleSelectRows = value => {
    setSelectRows(value);
  };
  const { tableProps } = useSlickTable(
    { pageSize: 5 },
    'portal/AppThemeController/selectAppThemeDetailGrid.do',
    params
  );
  //新增关联菜单
  const [visible,setVisible]=useState(false);
  //添加图标
  const [iconVisible,setIconVisible]=useState(false);
 //图标预览
  const [imgVisible,setImgVisible]=useState(false);
  //图标url
  const [imgUrl,setImgUrl]=useState("");

  //图标添加
  const [iconNbr,setIconNbr]=useState("")
// 空对象校验
  const judgeObj = obj => {
    if (Object.keys(obj).length == 0) return true;
    return false;
  };
  const [variable, setVariable] = useState(false);

  useEffect(() => {
    setVariable(true);
  }, []);
  useEffect(() => {
    if (!judgeObj(props.tableData) && variable) {
      setParams({
        ...params,
        themeCode: props.tableData.themeCode,
      });
      setSelectRows([]);
    }
  }, [props.tableData]);

  useEffect(()=>{
    queryByObjId()
  },[props.tableData,props.imgReloading])

  //新增关联提交
  const handleOk = () => {

    if (systemEl.current.getdataSource().length == '0') {
      message.error('请先选择待关联菜单');
      return
    }
    else{
      systemEl.current.handleAdd().then(res => {
        if (res.resultCode === '0') {
          message.success('新增成功')
        }else{
          message.success(res.resultMsg);
        }
      })
      setVisible(false);
      PubSub.publish('', flush());
    };
  };

  const flush=()=>{
    setTimeout(()=>{
      setParams({
        ...params
      })
    },1000);

  }

  //主题可选图标获取
  const queryByObjId=()=>{
    request('portal/FileStoreController/queryByObjId.do?objId='+props.tableData.appThemeId+'&objType=2000',{
     method:'get'
    }).then((res)=>{
      setIconList(res);
    })
  }

  const deleteThemeAttr=(record)=>{
    Modal.confirm({
      content: '确认进行删除？',
      onOk() {
        request('portal/AppThemeAttrController/deleteAppThemeAttrsByIds.do', {
          data: record,
          method: 'delete'
        }).then((res) => {
          if (res.resultCode == '0') {
            message.success('删除成功');
            PubSub.publish('', flush());
            setSelectRows([]);
          } else {
            message.error(res.resultMsg);
          }
        })
      }
    })
  }
  const columns=[
    {
      title:'菜单名称',
      dataIndex:'menuName',
    },
    {
      title:'菜单编码',
      dataIndex:'menuCode',
    },
    {
      title:'状态',
      render:(record, text)=>{
        return(
          record.menuStatusCd=='1000'?<span>有效</span>:<span>无效</span>
        )
      }
    },
    {
      title:'菜单描述 ',
      dataIndex:'menuDesc'
    },
    {
      title:'主题图标',
      render:(record,text)=>{
        return(
          <div>
            {record.mainIcon != null ?
              ( <div><a onClick={() => {
                if (iconList.length == 0) {
                  message.error("该主题尚未添加图标，请先上传主题图标");
                  return
                }
                setIconVisible(true);
                setCurrentId(record.appThemeAttrId);
                setModalName('修改主题图标');
                setActionType("main")
              }}>修改图标</a>
              < Divider type = "vertical" />
                < a onClick={()=>{checkIcon(record,'main');setCurrentId(record.appThemeAttrId);setActionType("main")}}>查看</a>
              </div>
                ):(
                <div>
                  <a onClick={() => {
                    if (iconList.length == 0) {
                      message.error("该主题尚未添加图标，请先上传主题图标");
                      return
                    }
                    setIconVisible(true);
                    setCurrentId(record.appThemeAttrId);
                    setModalName('添加主题图标');
                    setActionType("main")
                  }}>添加图标</a>
                  {/* <a onClick={()=>{checkIcon(record,'main');setCurrentId(record.appThemeAttrId);setActionType("main")}}>查看</a>*/}
                </div>
              )
            }
          </div>
        )
      }
    },
    {
      title:'默认图标',
      render:(record,text)=>{
        return(
          <div>
          {record.defaultIcon!=null?(
            <div>
              <a onClick={()=>{ if (iconList.length == 0) {
                message.error("该主题尚未添加图标，请先上传主题图标");
                return
              }
              setIconVisible(true);setCurrentId(record.appThemeAttrId);setModalName('修改默认图标');setActionType("default")}}>修改图标</a>
              <Divider type="vertical" />
              <a onClick={()=>{checkIcon(record,'default');setCurrentId(record.appThemeAttrId);setActionType("default")}}>查看</a>
            </div>
          ):(
            <div>
              <a onClick={()=>{ if (iconList.length == 0) {
                message.error("该主题尚未添加图标，请先上传主题图标");
                return
              }
              setIconVisible(true);setCurrentId(record.appThemeAttrId);setModalName('添加默认图标');setActionType("default")}}>添加图标</a>
            </div>
          )}
          </div>
        )
      }
    }
  ]

  //添加菜单图标
  const addIcon=(nbr)=>{
    request('portal/AppThemeAttrController/addIcon.do',{data:{
        iconNbr:nbr,
        id:currentId,
        type:actionType,
      }}).then((res)=>{
       if(res.resultCode=='0'){
         setIconVisible(false);
         message.success('添加成功');
         PubSub.publish('', flush());
       }
    })
  }
  //查看菜单图标
  const checkIcon=(record,type)=>{
    request('portal/AppThemeAttrController/checkIcon.do',{data:{
        id:record.appThemeAttrId,
        type:type,
      }}).then((res)=>{
      if(res!='null'){
         setImgVisible(true);
         let urlArr = res.split('?');
         if(urlArr.length>1){
          setImgUrl('portal/FileStoreController/download.do?'+urlArr[1])
         }else{
          setImgUrl(res)
         }
      }else{
        message.error("尚未添加图标，请先添加")
      }
    })
  }

  //删除菜单图标
  const deleteIcon=(type)=>{
    request('portal/AppThemeAttrController/addIcon.do',{
      data:{
        id:currentId,
        type:type,
        actionType:actionType,
      }
    }).then((res)=>{
      if(res.resultCode=='0'){
       setImgVisible(false);
       message.success("图标删除成功")
      }
    })
  }

  return(
    <div>
      <div className={styles.releRoleExtra}>
        <div className={styles.firstDiv}>
          <InputGroup compact>
            <Search
              placeholder="请选择"
              style={{ width: '60%' }}
              placeholder="菜单名称、编码搜索"
              onSearch={value=>{setParams({
                ...params,
                filterVal:value,
              })}}
            />
          </InputGroup>
        </div>
        <div>
          <Button type="primary" onClick={()=>{setVisible(true)}} className="margin-left">
            新增
          </Button>
        </div>
      </div>

      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        pick="checkbox"
        {...tableProps}
        extra={
          selectRows.length > 0 ? (
            <div>
              <Button type="danger" ghost onClick={()=>{deleteThemeAttr(selectRows)}}>
                删除
              </Button>
            </div>
          ) : null
        }
        onSelectRow={handleSelectRows}
        selectedRows={selectRows}
      />

      {/*  <Card.Grid style={{textAlign:'center',width:'10'}}
                 onClick={()=> {
                   Modal.confirm({
                     content: '确认添加该图标？',
                     onOk() {
                       addIcon(item.docNbr)
                     }
                   })
                 }}
            >*/}
      <Modal
        title={modalName}
        visible={iconVisible}
        onOk={()=>{if(iconNbr==''){message.error("请先选中图标");return}else{ addIcon(iconNbr);setIconNbr('')}}}
        onCancel={()=>{setIconVisible(false);setIconNbr('')}}
        width="900px"
        destroyOnClose>
        <Card  bordered={false} >
          <Radio.Group  onChange={(e)=>{setIconNbr(e.target.value)}}
          >
            {iconList.map(item=>{
              return (
                <div style={{width:'840px'}}>
                <Card.Grid className={styles.cardGrid} >
                  <div className={styles.test} data-title={item.fileName}>
                    <Radio value={item.docNbr}>
                      <img  style={{width:'50px',height:'50px'}} src={item.fileGetUrl}/>
                    </Radio>
                  </div>
                </Card.Grid>
                </div>
              )
            })}
          </Radio.Group>
        </Card>
      </Modal>

      <Modal
      title="新增关联菜单"
      visible={visible}
      onOk={handleOk}
      onCancel={()=>{setVisible(false)}}
      width="700px"
      destroyOnClose
      >
        <AddReleMenu ref={systemEl} cRef={systemEl} themeCode={props.tableData.themeCode}/>

      </Modal>

      <Modal
        style={{textAlign:'center'}}
        title="预览图"
        visible={imgVisible}
        onCancel={()=>{setImgVisible(false)}}
        footer={[
          null,
          null,
        ]}
        width='300px'
        destroyOnClose
      >
        <div style={{background:'#E8E8E8'}}>
          <img alt="preview" style={{ width: '70%',height:'50%', padding: 16 }} src={imgUrl} />
        </div>
         {/* <br/>
          <Icon type="delete" onClick={() =>deleteIcon("delete")} />*/}
      </Modal>

    </div>

  )
}
export default connect(({ themeManage }) => ({
  tableData: themeManage.tableData,
  imgReloading:themeManage.imgReloading
}))(ReleMenu);
