import React, { useState, useEffect, createContext } from 'react';
import { connect } from 'dva';
import { Row, Col, Card, Form, Input, Icon, Button, Table, Modal, message,Divider, Select,Spin } from 'antd';
import SlickTable from '@/components/SlickTable';
import useSlickTable from '@/hooks/UseSlickTable';
import SlickUpload from '@/components/SlickUpload';
import  styles from '../styles.less';
import PubSub from 'pubsub-js';
import request from '@/utils/request';
import { result } from 'lodash';
const Search = Input.Search;
const InputGroup = Input.Group;
const FormItem = Form.Item;

const { TextArea } = Input;
const { Option } = Select;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const textAreaFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const namespace = 'themeManage';

const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  else return false;
};

function AppThemeInfo(props) {
  const {
    form: { getFieldDecorator },
  } = props;

  const [loading, setLoading] = useState(false);

  const saveBehavior = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveBehavior`,
      payload: params,
    });
  };

  useEffect(() => {
    if (props.behavior === 'add') {
      props.form.resetFields();
      // props.form.setFieldsValue({ searchLimit: '9' });
    }
  }, [props.behavior]);

  //菜单基本信息保存
  const handleKeep = () => {
    props.form.validateFields((err, fieldsValue) => {
    if(err) return;
    //修改
     if(props.behavior=='edit'){
        const params_edit={
          id:props.tableData.id,
          themeCode:props.tableData.themeCode,
          ...fieldsValue,
        }
        setLoading(true);
       request('portal/AppThemeController/updateAppTheme.do',{
         data:params_edit,
         method: 'PUT',
       }).then((res)=>{
         if(res.resultCode=='0'){
           message.success(res.resultMsg);
           setLoading(false);
           saveBehavior('disabled');
           props.form.setFieldsValue({
             statusCd: fieldsValue.statusCd,
             themeDesc: fieldsValue.themeDesc,
             themeName: fieldsValue.themeName
           });
           PubSub.publish(`themeManageTable.editUpdate`);
         }else{
           setLoading(false);
           message.error(res.resultMsg);
         }
       })
       //新增
     }else{
       const params_add={
         themeName:fieldsValue.themeName,
         themeCode:fieldsValue.themeCode,
         statusCd:fieldsValue.statusCd,
         themeDesc:fieldsValue.themeDesc,
       }
       setLoading(true);
       request('portal/AppThemeController/addTheme.do',{
         data:params_add
       }).then((res)=>{
         if(res.resultCode=='0'){
           message.success(res.resultMsg);
           setLoading(false);
           saveBehavior('disabled');
           PubSub.publish(`themeManageTable.addUpdate`);
         }else{
           setLoading(false);
           message.error(res.resultMsg);
         }
       })
     }
    }
    )

  }

  const handleCancel = () => {
    saveBehavior('disabled');
  };
return(
  <Spin spinning={loading}>
    <Form>
      <Row>
        <Col span={6}>
          <FormItem label="主题名称" {...formItemLayout}>
            {getFieldDecorator('themeName', {
              rules: [
                {
                  required: true,
                  message: '主题名称不能为空',
                },
              ],
            })(
              <Input
                placeholder="请输入"

                disabled={props.behavior === 'disabled' ? true : false}

              />
            )}
          </FormItem>
        </Col>

        <Col span={6}>
          <FormItem label="主题编码" {...formItemLayout}>
            {getFieldDecorator('themeCode', {
              rules: [
                {
                  required: true,
                  message: '主题编码不能为空',
                },
              ],
            })(
              <Input
                placeholder="请输入"
                disabled={
                  props.behavior === 'disabled' || props.behavior === 'edit' ? true : false
                }

              />
            )}
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="状态" {...formItemLayout}>
            {getFieldDecorator('statusCd', {
              rules: [
                {
                  required: true,
                  message: '状态不能为空',
                },
              ],
            })(
              <Select
                disabled={props.behavior === 'disabled' ? true : false}
              >
                <Option value="1000">有效</Option>
                <Option value="1100">无效</Option>
              </Select>
            )}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          <FormItem label="主题描述" {...textAreaFormItemLayout}>
            {getFieldDecorator('themeDesc')(
              <TextArea
                rows={4}
                placeholder="请输入"
                disabled={props.behavior === 'disabled' ? true : false}

              />
            )}
          </FormItem>
        </Col>
      </Row>

      {props.behavior !== 'disabled' ? (
        <div style={{ textAlign: 'center' }}>
          <Button type="primary" className="margin-right" onClick={handleKeep}>
            保存
          </Button>
          <Button type="default" onClick={handleCancel}>
            取消
          </Button>
        </div>
      ) : null}
    </Form>
  </Spin>
)
    }
export default connect(({ themeManage }) => ({
  behavior: themeManage.behavior,
  tableData: themeManage.tableData,
}))(
  Form.create({
    mapPropsToFields(props) {
      if (!judgeObj(props.tableData)) {
        if (props.behavior !== 'add') {
          return {
            themeName: Form.createFormField({ value: props.tableData.themeName }),
            themeCode: Form.createFormField({ value: props.tableData.themeCode }),
            statusCd: Form.createFormField({ value: props.tableData.statusCd }),
            themeDesc: Form.createFormField({ value: props.tableData.themeDesc }),
          };
        }
      }
    },
  })(AppThemeInfo)
);
