/*
@import '~antd/lib/style/themes/default.less';
.extra {
  display: table;
  div:first-child {
    :global {
      .ant-input-group-compact {
        width: 450px;
      }
    }
  }
  div:nth-child(2) {
    display: table-cell;
  }
}

.systemExtra {
  width: 100%;
  text-align: right;
  margin-bottom: 8px;
}

.releRoleExtra {
  text-align: right;
  width: 100%;
  margin-bottom: 8px;
  div:first-child {
    :global {
      .ant-input-group-compact {
        width: 300px;
      }
    }
  }
  div:nth-child(2),
  .firstDiv {
    vertical-align: middle;
    display: inline-block;
  }
}

.resize_transfer {
  :global {
    .ant-transfer-list-header > .ant-transfer-list-header-selected {
      span:first-child {
        display: none;
      }
      span:nth-child(2) {
        position: relative;
        // left: 12px;
        right: 0px;
      }
    }
  }
}

.grant_perList{
  margin-top: 8px;
  :global{
    .ant-form-item-label{
      text-overflow: ellipsis;
    }
  }
  .remark{
    margin-bottom: 0;
    :global{
      .ant-form-item-label{
        width: 40px;
      }
      .ant-form-item-control-wrapper{
        width:calc(100% - 40px);
      }
    }
  }
  .operType,.manageClass{
    margin-bottom: 0;
    // :global{
    //   .ant-form-item-label{
    //     width: 100px;
    //   }
    //   .ant-form-item-control-wrapper{
    //     width:calc(100% - 100px);
    //   }
    // }
  }

}

.clickRowStyle {
  background-color: #e6f7ff;
}
.cardGrid {
  width: 12.5%;
  height:83px;
  .done {
    text-align: center;
    background-color: @blue-5;
  }
  .undo {
    color: @text-color-secondary;
    text-align: center;
    background-color: #e8e8e8;
  }
  .actions {
    visibility: hidden;
    i {
      cursor: pointer;
      :hover {
        color: @blue-6;
      }
    }
  }
  .img{
    visibility: visible;
  }
  &:hover {
    .actions {
      visibility: visible;
    }
    .img{
      opacity:0.2
    }
  }
}


.cardGrid:after{
  content: attr(data-title);
  position: absolute;
  width:126%;
  top:90px;
  left:-10px;
  text-align: center;
  padding: 0.7em 0;
  color: #fff;
  background-color: rgba(52,47,41,.5);
  -webkit-transition:all .3s ease-in-out;
  transition:all .3s ease-in-out;
  opacity: 0;
}
.cardGrid:hover:after{
  opacity: 1;
  -webkit-transform:translate(0,-170%);
  transform:translate(0,-170%);
}



.test{
  position: relative;
  width:130px;
  height:83px;
  overflow: hidden;
}
.test:after{
  content: attr(data-title);
  position: absolute;
  width:100%;
  top:90px;
  left:12px;
  text-align: center;
  padding: 0.7em 0;
  color: #fff;
  background-color: rgba(52,47,41,.5);
  -webkit-transition:all .3s ease-in-out;
  transition:all .3s ease-in-out;
  opacity: 0;
}
.test:hover:after{
  opacity: 1;
  -webkit-transform:translate(0,-170%);
  transform:translate(0,-170%);

}
*/

@import '~antd/lib/style/themes/default.less';
.extra {
  display: table;
  div:first-child {
    :global {
      .ant-input-group-compact {
        width: 450px;
      }
    }
  }
  div:nth-child(2) {
    display: table-cell;
  }
}

.systemExtra {
  width: 100%;
  text-align: right;
  margin-bottom: 8px;
}

.releRoleExtra {
  text-align: right;
  width: 100%;
  margin-bottom: 8px;
  div:first-child {
    :global {
      .ant-input-group-compact {
        width: 300px;
      }
    }
  }
  div:nth-child(2),
  .firstDiv {
    vertical-align: middle;
    display: inline-block;
  }
}

.resize_transfer {
  :global {
    .ant-transfer-list-header > .ant-transfer-list-header-selected {
      span:first-child {
        display: none;
      }
      span:nth-child(2) {
        position: relative;
        // left: 12px;
        right: 0px;
      }
    }
  }
}

.grant_perList{
  margin-top: 8px;
  :global{
    .ant-form-item-label{
      text-overflow: ellipsis;
    }
  }
  .remark{
    margin-bottom: 0;
    :global{
      .ant-form-item-label{
        width: 40px;
      }
      .ant-form-item-control-wrapper{
        width:calc(100% - 40px);
      }
    }
  }
  .operType,.manageClass{
    margin-bottom: 0;
    // :global{
    //   .ant-form-item-label{
    //     width: 100px;
    //   }
    //   .ant-form-item-control-wrapper{
    //     width:calc(100% - 100px);
    //   }
    // }
  }

}

.clickRowStyle {
  background-color: #e6f7ff;
}
.cardGrid {
  width: 12.5%;
  height:93px;
  .done {
    text-align: center;
    background-color: @blue-5;
  }
  .undo {
    color: @text-color-secondary;
    text-align: center;
    background-color: #e8e8e8;
  }
  .actions {
    visibility: hidden;
    i {
      cursor: pointer;
      :hover {
        background-color: #e8e8e8;
      }
    }
  }
  .img{
    visibility: visible;
  }

  .test{
    position: relative;
    width:120px;
    height:100px;
    overflow: hidden;
  }
  .test:after{
    content: attr(data-title);
    position: absolute;
    width:100%;
    top:100px;
    left:0;
    padding: 0.7em 0;
    text-align: center;
    color: #fff;
    background-color: rgba(52,47,41,.5);
    -webkit-transition:all .3s ease-in-out;
    transition:all .3s ease-in-out;
    opacity: 0;
  }
  .test:hover:after{
    opacity: 1;
    -webkit-transform:translate(0,-100%);
    transform:translate(0,-100%);

  }


  &:hover {
    .actions {
      visibility: visible;
    }
    .img{
      opacity:0.2
    }
  }
}
.cardGrid:hover{
  background-color: grey;
}

