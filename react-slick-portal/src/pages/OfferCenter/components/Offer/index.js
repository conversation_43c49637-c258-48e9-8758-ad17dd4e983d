import { connect } from 'dva';
import React, { useEffect, useState } from 'react';
import { Empty, Pagination, Spin, Card, Input, Popover } from 'antd';
import classnames from 'classnames';
import OfferList from '../OfferList';
import { pageCondByHome } from '@/services/offer';
import { queryProdListByKey, querySearchHis } from '@/services/search';
import style from './Offer.module.less';
import usePageTable from '@/hooks/usePageTable';

const Offer = props => {
  const { offerMenuTree, current, subCurrent, subCurrentType, collectChange, dispatch } = props;
  const [hoverCurrent, setHoverCurrent] = useState('');
  const [hoverCurrentStyle, setHoverCurrentStyle] = useState('');
  const [currentType, setCurrentType] = useState('');
  const [searchKey, setSearchKey] = useState('');
  const [searchHis, setSearchHis] = useState([]);
  const [visible, setVisible] = useState(false);

  const {
    pagination, list, loading, getTableData, form, setForm, setPageNo,
  } = usePageTable({
    method: pageCondByHome,
    defaultStatusCd: '2',
    pageParamsKey: { index: 'pageNum', size: 'pageSize' },
    pageSize: 8,
    pageType: 1,
  });

  useEffect(() => {
    getTableData();
  }, [form, collectChange]);

  useEffect(() => {
    const req = {};
    if (current !== 0) {
      req.catalogId = current;
    }
    if (subCurrentType === '1000') {
      req.catalogItemId = subCurrent;
    }
    // req.catalogId = '';
    req.offerName = searchKey;
    req.statusCd = '2';
    setForm(req);
  }, [current, subCurrent]);

  const onSearch = (e, trigger) => {
    setPageNo(1);
    setSearchKey(e);
    const req = {};
    if (current !== 0) {
      req.catalogId = current;
    }
    if (subCurrentType === '1000') {
      req.catalogItemId = subCurrent;
    }
    req.statusCd = '2';
    req.offerName = e;
    setForm(req);
    if (trigger === 'type' && e !== '') {
      queryProdListByKey(e).then(() => {
        querySearchHis().then(res => {
          setSearchHis(res.resultObject);
        })
      });
    }
    setVisible(false);
  };

  useEffect(() => {
    querySearchHis().then(res => {
      setSearchHis(res.resultObject);
    })
  }, [])

  return (
    <div className={style.wrap}>
      <Card
        title={
          <div className={style.nav}>
            <ul className={style.menu}>
              <li
                onClick={() => {
                  dispatch({
                    type: 'offer/setCurrent',
                    payload: 0,
                  });
                  dispatch({
                    type: 'offer/setSubCurrentType',
                    payload: '',
                  });
                  setCurrentType('');
                  setPageNo(1);
                }}
                className={classnames(current === 0 ? style.currentMenuItem : undefined,
                  hoverCurrentStyle === 0 ? style.hoverCurrentItem : undefined)
                }
                onMouseEnter={() => {
                  setHoverCurrent(0);
                  setHoverCurrentStyle(0);
                  dispatch({
                    type: 'offer/setSubCurrentType',
                    payload: '',
                  });
                }}
                onMouseLeave={() => {
                  setHoverCurrent(current);
                  setHoverCurrentStyle('');
                  dispatch({
                    type: 'offer/setSubCurrentType',
                    payload: currentType,
                  });
                }}
              >
                <span className={style.catName}>全部</span>
              </li>
              {
                offerMenuTree.map(item => (
                  <li
                    key={item.catalogId}
                    onClick={() => {
                      dispatch({
                        type: 'offer/setCurrent',
                        payload: item.catalogId,
                      });
                      dispatch({
                        type: 'offer/setSubCurrentType',
                        payload: item.catalogItemList[0]?.catalogItemType,
                      });
                      setCurrentType(item.catalogItemList[0]?.catalogItemType);
                      if (item.catalogItemList && item.catalogItemList.length > 0 && subCurrentType === '1000') {
                        dispatch({
                          type: 'offer/setSubCurrent',
                          payload: item.catalogItemList[0].catalogItemId,
                        });
                      }
                      setPageNo(1);
                    }}
                    className={classnames(current === item.catalogId ? style.currentMenuItem : undefined,
                      hoverCurrentStyle === item.catalogId ? style.hoverCurrentItem : undefined)
                    }
                    onMouseEnter={() => {
                      setHoverCurrent(item.catalogId);
                      setHoverCurrentStyle(item.catalogId);
                      dispatch({
                        type: 'offer/setSubCurrentType',
                        payload: item.catalogItemList[0].catalogItemType,
                      });
                    }}
                    onMouseLeave={() => {
                      setHoverCurrent(current);
                      setHoverCurrentStyle('');
                      dispatch({
                        type: 'offer/setSubCurrentType',
                        payload: currentType,
                      });
                    }}
                  >
                    <span className={style.catName}>{item.catalogName}</span>
                  </li>
                ))
              }
            </ul>
          </div>
        }
        extra={
          <Popover
            placement="bottomLeft"
            visible={visible}
            trigger="click"
            overlayClassName={style.searchHis}
            overlayStyle={{width: 372, paddingTop: 0}}
            onVisibleChange={v => setVisible(v)}
            content={searchHis.map(item =>
              <div
                className={style.historyItem}
                key={item.id}
                onClick={() => onSearch(item.recentContent, 'click')}
              >
                {item.recentContent}
              </div>
            )}
          >
            <Input.Search
              placeholder="产品搜索"
              onSearch={e => onSearch(e, 'type')}
              value={searchKey}
              onChange={e => setSearchKey(e.target.value)}
              allowClear
            />
          </Popover>
        }
      >
        <ul className={style.submenu}>
          {
            subCurrentType === '1000' && offerMenuTree.map(item => (
              hoverCurrent === item.catalogId && (
                item.catalogItemList && item.catalogItemList.map(catalogItem => (
                  <li
                    key={catalogItem.catalogItemId}
                    onClick={() => {
                      dispatch({
                        type: 'offer/setCurrent',
                        payload: item.catalogId,
                      });
                      dispatch({
                        type: 'offer/setSubCurrent',
                        payload: catalogItem.catalogItemId,
                      });
                      setPageNo(1);
                    }}
                    className={subCurrent === catalogItem.catalogItemId ? style.subCurrentMenuItem : undefined}
                  >
                    {catalogItem.catalogItemName}
                  </li>
                ))
              )
            ))
          }
        </ul>
        <div className={style.offers}>
          {
            list && list.length > 0 ? (
              <Spin spinning={loading}>
                <OfferList list={list} getTableData={getTableData} />
                <Pagination
                  {...pagination}
                  size="small"
                  showQuickJumper
                  showSizeChanger={false}
                />
              </Spin>
            ) : <Empty />
          }
        </div>
      </Card>
      {/* <div className={style.nav}>
        <ul className={style.menu}>
          {
            offerMenuTree.map(item => (
              <li
                key={item.catalogId}
                onClick={() => {
                  dispatch({
                    type: 'offer/setCurrent',
                    payload: item.catalogId,
                  });
                  dispatch({
                    type: 'offer/setSubCurrentType',
                    payload: item.catalogItemList[0]?.catalogItemType,
                  });
                  setCurrentType(item.catalogItemList[0]?.catalogItemType);
                  if (item.catalogItemList && item.catalogItemList.length > 0 && subCurrentType === '1000') {
                    dispatch({
                      type: 'offer/setSubCurrent',
                      payload: item.catalogItemList[0].catalogItemId,
                    });
                  }
                  setPageNo(1);
                }}
                className={classnames(current === item.catalogId ? style.currentMenuItem : undefined,
                  hoverCurrentStyle === item.catalogId ? style.hoverCurrentItem : undefined)
                }
                onMouseEnter={() => {
                  setHoverCurrent(item.catalogId);
                  setHoverCurrentStyle(item.catalogId);
                  dispatch({
                    type: 'offer/setSubCurrentType',
                    payload: item.catalogItemList[0].catalogItemType,
                  });
                }}
                onMouseLeave={() => {
                  setHoverCurrent(current);
                  setHoverCurrentStyle('');
                  dispatch({
                    type: 'offer/setSubCurrentType',
                    payload: currentType,
                  });
                }}
              >
                {item.catalogName}
              </li>
            ))
          }
        </ul>
      </div>
      <ul className={style.submenu}>
        {
          subCurrentType === '1000' && offerMenuTree.map(item => (
            hoverCurrent === item.catalogId && (
              item.catalogItemList && item.catalogItemList.map(catalogItem => (
                <li
                  key={catalogItem.catalogItemId}
                  onClick={() => {
                    dispatch({
                      type: 'offer/setCurrent',
                      payload: item.catalogId,
                    });
                    dispatch({
                      type: 'offer/setSubCurrent',
                      payload: catalogItem.catalogItemId,
                    });
                    setPageNo(1);
                  }}
                  className={subCurrent === catalogItem.catalogItemId ? style.subCurrentMenuItem : undefined}
                >
                  {catalogItem.catalogItemName}
                </li>
              ))
            )
          ))
        }
      </ul>
      <div className={style.offers}>
        {
          list && list.length > 0 ? (
            <Spin spinning={loading}>
              <OfferList list={list} getTableData={getTableData} />
              <Pagination
                {...pagination}
                size="small"
                showQuickJumper
                showSizeChanger={false}
              />
            </Spin>
          ) : <Empty />
        }
      </div> */}
    </div>
  )
}

export default connect(({ offer }) => ({
  offerMenuTree: offer.offerMenuTree,
  current: offer.current,
  subCurrent: offer.subCurrent,
  subCurrentType: offer.subCurrentType,
  collectChange: offer.collectChange,
}))(Offer);
