import React, { useState, useEffect, createContext } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Icon,
  Button,
  Table,
  Modal,
  message,
  Select,
  Spin,
} from 'antd';
import SlickTable from '@/components/SlickTable';
import useSlickTable from '@/hooks/UseSlickTable';
import styles from '../styles.less';
import PubSub from 'pubsub-js';
import request from '@/utils/request';
const { Option } = Select;
const FormItem = Form.Item;
const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const textAreaFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

// 判断空对象
const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  else return false;
};

const namespace = 'areaManage';

function areaInfo(props) {
  const {
    form: { getFieldDecorator },
  } = props;

  const [valueList, setValueList] = useState([]);
  const [loading, setLoading] = useState(false);

  const saveBehavior = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveBehavior`,
      payload: params,
    });
  };

  useEffect(() => {
    getValuesList();
  }, []);

  async function getValuesList() {
    setLoading(true);
    await Promise.all([
      request('portal/DomainDataController/getValuesList.do', {
        data: {
          busiNbr: 'Organization',
          propertyName: 'orgType',
        },
      }),
      request('portal/DomainDataController/getValuesList.do', {
        data: { busiNbr: 'Organization', propertyName: 'statusCd' },
      }),
    ]).then(res => {
      setValueList(res);
      setLoading(false);
    });
  }

  const handleCancel = () => {
    saveBehavior('disabled');
  };

  const handleKeep = () => {
    // saveBehavior('disabled');
    props.form.validateFields((err, fieldsValue) => {
      if (err) return;
      let obj={};
      props.behavior==="edit" ? (
        obj={
          ...fieldsValue,
          createStaff:"3044336",
          entityAttrs:props.passData.entityAttrs,
          commonRegionId:props.passData.commonRegionId,
          parRegionId:props.passData.parRegionId,
         // partyId:props.passData.partyId
        }
      ):(
        obj={
          ...fieldsValue,
          createStaff:"3044336",
          entityAttrs:props.passData.entityAttrs,
          parRegionId:props.passData.commonRegionId,
         // partyId:props.passData.partyId ? props.passData.partyId : 0 ,
          pathName:`${props.passData.pathName}/${fieldsValue.pathName}`
        }
      )
      setLoading(true);

      request('orgauth/CommonRegionController/regionSave.do', { data: obj }).then(res => {
        if (res) {
          message.success('保存成功！');
          saveBehavior('disabled');


          if(props.behavior==="edit"){
            props.form.setFieldsValue({
              regionName: res.regionName,
              regionType: res.regionType,
              regionLevel: res.regionLevel,
            });
          }
          PubSub.publish(`areaManage_AreaList.Update`);
          PubSub.publish(`areaManage_AreaMenu.Update`)
        } else {
          message.error('失效失败！');

        }
      }).always(() => {
        setLoading(false)
      });


    });
  };

  return (
    <Spin spinning={loading}>
      <Form>
        <Row>
          <Col span={6}>
            <FormItem label="区域名称" {...formItemLayout}>
              {getFieldDecorator('regionName', {
                rules: [
                  {
                    required: true,
                    message: '区域名称不能为空',
                  },
                ],
              })(
                <Input
                  placeholder="请输入"
                  disabled={props.behavior === 'disabled' ? true : false}
                />
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="区域编码" {...formItemLayout}>
              {getFieldDecorator('regionNbr')(<Input placeholder="请输入" disabled={true} />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="区域类型" {...formItemLayout}>
              {getFieldDecorator('regionType', {
                rules: [
                  {
                    required: false,
                    message: '区域类型不能为空',
                  },
                ],
              })(
                <Select
                  placeholder="请选择"
                  disabled={props.behavior === 'disabled' ? true : false}
                >
                  {valueList.length
                    ? valueList[0].map(item => {
                        return (
                          <Option value={item.value} key={item.value}>
                            {item.name}
                          </Option>
                        );
                      })
                    : null}
                </Select>
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="上级区域" {...formItemLayout}>
              {getFieldDecorator('parRegionName', {
                rules: [
                  {
                    required: true,
                    message: '上级区域不能为空',
                  },
                ],
              })(<Input placeholder="请输入" disabled={true} />)}
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span={6}>
            <FormItem label="区域层级" {...formItemLayout}>
              {getFieldDecorator('regionLevel')(
                <Input
                  placeholder="请输入"
                  disabled={props.behavior === 'disabled' ? true : false}
                />
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="状态" {...formItemLayout}>
              {getFieldDecorator('statusCd')(
                <Select placeholder="请选择" disabled={true}>
                  {valueList.length
                    ? valueList[1].map(item => {
                        return (
                          <Option value={item.value} key={item.value}>
                            {item.name}
                          </Option>
                        );
                      })
                    : null}
                </Select>
              )}
            </FormItem>
          </Col>
        </Row>

        <Row>
          <Col span={12}>
            <FormItem label="区域路径" {...textAreaFormItemLayout}>
              {getFieldDecorator('pathName')(<Input placeholder="请输入" disabled={true} />)}
            </FormItem>
          </Col>
        </Row>

        {props.behavior !== 'disabled' ? (
          <div style={{ textAlign: 'center', marginTop: '8px' }}>
            <Button type="primary" className="margin-right" onClick={handleKeep}>
              保存
            </Button>
            <Button type="default" onClick={handleCancel}>
              取消
            </Button>
          </div>
        ) : null}
      </Form>
    </Spin>
  );
}

export default connect(({ areaManage }) => ({
  passData: areaManage.passData,
  behavior: areaManage.behavior,
}))(
  Form.create({
    mapPropsToFields(props) {
      if (!judgeObj(props.passData)) {
        return {
          regionName: Form.createFormField({
            value: props.behavior !== 'add' ? props.passData.regionName : '',
          }),
          regionNbr: Form.createFormField({
            value: props.behavior !== 'add' ? props.passData.regionNbr : '',
          }),
          regionType: Form.createFormField({
            value: props.behavior !== 'add' ? props.passData.regionType : '',
          }),
          parRegionName: Form.createFormField({
            value: props.behavior !== 'add' ? props.passData.parRegionName : props.passData.regionName,
          }),
          regionLevel: Form.createFormField({
            value: props.behavior !== 'add' ? props.passData.regionLevel : props.passData.regionLevel + 1,
          }),
          statusCd: Form.createFormField({ value: props.passData.statusCd }),
          pathName: Form.createFormField({
            value: props.behavior !== 'add' ? props.passData.pathName : '',
          }),
          // id: Form.createFormField({ value: props.passData.id }),
        };
      }
    },
  })(areaInfo)
);
