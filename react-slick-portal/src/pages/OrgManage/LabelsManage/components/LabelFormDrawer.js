import React, { useState, useEffect } from 'react';
import { Input, Button, Select, Form, Drawer, Row, Col, Divider, Modal, message } from 'antd';
import styles from '../styles.less';
import { saveLabel } from '../services/commonServices';

function LabelFormDrawer({ record = {}, form, categoryOpts = [], ...rest }) {
  const { getFieldDecorator } = form;

  const [categoryCodeDisable, setCategoryDisable] = useState(true);

  const [_categoryOpts, setCategoryOpts] = useState([]);

  const [_categoryMap, setCategoryMap] = useState({});

  const [newCategoryName, setNewCategoryName] = useState('');

  const [modalVisible, setModalVisible] = useState(false);

  const [newCategoryValid, setNewCategoryValid] = useState({ validateStatus: '', help: '' });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (record) {
      // form.setFieldsValue({ ...record });
      form.setFieldsValue({
        ...record,
        category: record.categoryCode,
      });
    } else {
      form.setFieldsValue({
        labelId: null,
        category: undefined,
        categoryCode: undefined,
        subcategory: undefined,
        subcategoryCode: undefined,
      });
    }
  }, [record]);

  useEffect(() => {
    const categoryMap = {};
    for (let i = 0; i < categoryOpts.length; i++) {
      categoryMap[categoryOpts[i].value] = categoryOpts[i];
    }
    setCategoryOpts(categoryOpts);
    setCategoryMap(categoryMap);
  }, [categoryOpts]);

  const onConfirm = () => {
    form.validateFields((err, values) => {
      if (!err) {
        let diffObj = {};
        if (values.labelId) {
          const keys = Object.keys(values);
          for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            if (key !== 'category' && record[key] !== values[key]) {
              diffObj[key] = values[key];
            }
          }
          if (JSON.stringify(diffObj) === '{}') {
            Modal.info({ title: '标签配置没有发生变化' });
            return;
          }
          diffObj.labelId = record.labelId;
        } else {
          diffObj = values;
          // 新增标签大类不用修改，非新增标签大类需要重新设置值，因为下拉框选中项的值为categoryCode，而非category
          if (!_categoryMap[diffObj.category]?.isNew) {
            diffObj.category = _categoryMap[diffObj.categoryCode].label;
          }
        }
        setLoading(true);
        saveLabel(diffObj, () => setLoading(false)).then(res => {
          if (!res.success) {
            message.error(res.resultMsg);
            return;
          }
          if (rest.onClose) {
            if (_categoryMap[values.category]?.isNew) {
              rest.onClose('confirm', { label: values.category, value: values.categoryCode, labelId: record?.labelId });
            } else {
              rest.onClose('confirm', { labelId: record?.labelId });
            }
          }
        });
      }
    });
  };

  const onCancel = () => {
    if (rest.onClose) {
      rest.onClose('cancel');
    }
  };

  const onCategoryChange = e => {
    const srcOpt = _categoryMap[e];
    form.setFieldsValue({
      categoryCode: srcOpt?.isNew ? '' : e,
    });
    if (srcOpt?.isNew) {
      setCategoryDisable(false);
    } else {
      setCategoryDisable(true);
    }
  };

  const addNewCategory = () => {
    setModalVisible(true);
    setNewCategoryName('');
  };

  const addNewCategoryFinish = () => {
    if (!newCategoryName) {
      setNewCategoryValid({
        validateStatus: 'error',
        help: '请输入标签大类名称',
      });
      return;
    }
    for (let i=0; i < _categoryOpts.length; i++) {
      if (_categoryOpts[i].label == newCategoryName) {
        setNewCategoryValid({
          validateStatus: 'error',
          help: '标签大类名称已存在',
        });
        return;
      }
    }
    setNewCategoryValid({
      validateStatus: 'success',
      help: '',
    });
    const fieldsValue = form.getFieldsValue();
    form.setFieldsValue({
      ...fieldsValue,
      category: newCategoryName,
    });
    setCategoryDisable(false);
    const categoryOpts = [..._categoryOpts];
    const categoryMap = { ..._categoryMap };
    const newOpt = { label: newCategoryName, value: newCategoryName, isNew: true };
    categoryOpts.push(newOpt);
    categoryMap[newCategoryName] = newOpt;
    setCategoryOpts(categoryOpts);
    setCategoryMap(categoryMap);
    message.success('新增标签成功');
    setModalVisible(false);
  };

  const cancelAddCategory = () => {
    setModalVisible(false);
  };

  return (
    <Drawer title={record?.labelId ? '更新标签参数配置' : '新增标签参数配置'} {...rest}>
      <Form>
        <Row style={{ display: 'none' }}>
          <Form.Item>{getFieldDecorator('labelId', {})(<Input disabled />)}</Form.Item>
        </Row>
        <Row className="margin-bottom">
          <Col span={11}>
            <span>标签大类</span>
          </Col>
          <Col offset={2} span={11}>
            <span>标签大类编码</span>
          </Col>
        </Row>
        <Row>
          <Col span={11}>
            <Form.Item>
              {getFieldDecorator('category', {
                rules: [{ required: true, message: '请选择标签大类' }],
              })(
                <Select
                  showSearch
                  filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                  allowClear
                  placeholder="请选择"
                  onChange={onCategoryChange}
                  disabled={record?.labelId}
                  dropdownRender={menu => (
                    <div>
                      {menu}
                      <Divider style={{ margin: '0' }} />
                      <div
                        // style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '4px 6px' }}
                        onMouseDown={e => e.preventDefault()}
                      >
                        <div style={{ padding: '4px 8px', cursor: 'pointer' }} onClick={addNewCategory}>
                          <Button type="default">新增</Button>
                        </div>
                      </div>
                    </div>
                  )}
                >
                  {_categoryOpts.map(e => <Select.Option key={e.value}>{e.label}</Select.Option>)}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col offset={2} span={11}>
            <Form.Item>
              {getFieldDecorator('categoryCode', { rules: [{ required: true, message: '请输入标签大类编码' }] })(
                <Input disabled={categoryCodeDisable} />
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row className="margin-bottom">
          <Col span={11}>
            <span>标签小类</span>
          </Col>
          <Col offset={2} span={11}>
            <span>标签小类编码</span>
          </Col>
        </Row>
        <Row>
          <Col span={11}>
            <Form.Item>
              {getFieldDecorator('subcategory', { rules: [{ required: true, message: '请输入标签小类' }] })(
                <Input placeholder="请输入" allowClear />
              )}
            </Form.Item>
          </Col>
          <Col offset={2} span={11}>
            <Form.Item>
              {getFieldDecorator('subcategoryCode', { rules: [{ required: true, message: '请输入标签小类编码' }] })(
                <Input placeholder="请输入" allowClear />
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <div className={styles['form-drawer-bottom']}>
        <Button
          loading={loading}
          style={{
            marginRight: 8,
          }}
          onClick={onConfirm}
          type="primary"
        >
          {record?.labelId ? '更新' : '创建'}
        </Button>
        <Button onClick={onCancel}>取消</Button>
      </div>

      <Modal visible={modalVisible} title="新增标签大类" onOk={addNewCategoryFinish} onCancel={cancelAddCategory}>
        <Form>
          <Form.Item
            validateStatus={newCategoryValid.validateStatus}
            help={newCategoryValid.help}
            label="大类名称"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
          >
            <Input value={newCategoryName} onChange={e => setNewCategoryName(e.target.value)} placeholder="请输入" allowClear />
          </Form.Item>
        </Form>
      </Modal>
    </Drawer>
  );
}

export default Form.create()(LabelFormDrawer);
