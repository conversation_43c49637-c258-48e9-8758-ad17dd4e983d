import React, { useImperativeHandle, useState } from 'react';
import { Card, Form, message, Row, Col, Select, Radio, Switch, Modal } from 'antd';
import { connect } from 'dva';
import styles from './index.less';
import AmountSelector from '@/components/MoneySelect';

const PaymentInfo = props => {
  const { form, cRef, goToStep } = props;
  const { getFieldDecorator, validateFields } = form;
  const [loading, setLoading] = useState(false);

  // 表单布局
  const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };
  useImperativeHandle(cRef, () => ({
    next: () =>
      new Promise(resolve => {
        validateFields((err, values) => {
          if (!err) {
            Modal.confirm({
              centered: true,
              zIndex: 10001, // root container是10000，所以必须大于
              title: `缴费金额：${values.payAmount},确认完成？`,
              onOk() {
                // todo： 接口
                goToStep(3);
              },
            });
          }
        });
      }),
    prev: () => goToStep(1),
  }));

  return (
    <div className={styles.container}>
      <Card title="缴费信息" bordered={false}>
        <Form>
          <Row>
            <Col span={8}>
              <Form.Item label="缴费金额" {...formItemLayout}>
                {getFieldDecorator('payAmount', {
                  initialValue: '200',
                  rules: [{ required: true, message: '缴费金额不能为空' }],
                })(<AmountSelector />)}
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <Form.Item label="付款方式" {...formItemLayout}>
                {getFieldDecorator('paymentType', {
                  initialValue: '1',
                })(
                  <Select placeholder="请选择">
                    <Select.Option value="1">现金</Select.Option>
                    <Select.Option value="2">银行卡</Select.Option>
                    <Select.Option value="3">微信支付</Select.Option>
                    <Select.Option value="4">支付宝</Select.Option>
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={8}>
              <Form.Item label="打印收据" {...formItemLayout}>
                {getFieldDecorator('receiptType', {
                  initialValue: 'electronic',
                  rules: [{ required: true, message: '请选择收据类型' }],
                })(
                  <Radio.Group>
                    <Radio.Button value="electronic">电子收据</Radio.Button>
                    <Radio.Button value="paper">纸质收据</Radio.Button>
                    <Radio.Button value="none">无需收据</Radio.Button>
                  </Radio.Group>
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={8}>
              <Form.Item label="新收据样式" {...formItemLayout}>
                {getFieldDecorator('newReceiptStyle', {
                  initialValue: true,
                  rules: [{ required: true, message: '请选择收据样式' }],
                })(<Switch checkedChildren="是" unCheckedChildren="否" defaultChecked />)}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default connect()(Form.create()(PaymentInfo));
