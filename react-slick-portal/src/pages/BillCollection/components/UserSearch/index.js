import React, { useState, useEffect, useRef } from 'react';
import { Empty, Input, Select, message, List } from 'antd';
import style from './index.less';
import request from '@/utils/request';

const InputGroup = Input.Group;
const queryFields = [
  { key: 'subscriberInsId', label: '集团用户标识', type: 'input' },
  { key: 'accessNum', label: '集团服务号码', type: 'input' },
  { key: 'personNum', label: '个人号码', type: 'input' },
  { key: 'widenetAccessNum', label: '宽带账号', type: 'input' },
  { key: 'operatorCode', label: '客户经理编码', type: 'input' },

  { key: 'groupId', label: '集团编码', type: 'input' },
  { key: 'acctId', label: '集团账号', type: 'input' },
  { key: 'groupName', label: '集团名称', type: 'input' },
  { key: 'mebAccessNum', label: '成员服务号码', type: 'input' },
];
const UserSearch = props => {
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [customerList, setCustomerList] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [searchType, setSearchType] = useState('groupId');
  const [pageNum, setPageNum] = useState(1);
  const inputRef = useRef(null);
  const searchBoxRef = useRef(null);
  const onClickOutsideHandle = e => {
    if (!searchBoxRef.current.contains(e?.target)) {
      setShowSearch(false);
    }
  };
  useEffect(() => {
    window.addEventListener('click', onClickOutsideHandle);
    return () => {
      window.removeEventListener('click', onClickOutsideHandle);
    };
  }, []);
  useEffect(() => {}, []);

  const fetchCustomerList = value => {
    if (!value) {
      setCustomerList([]);
      return;
    }
    setLoading(true);
    let apiName = '';
    let keyword = '';
    switch (searchType) {
      // 集团用户标识
      case 'subscriberInsId':
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByGroupId.do';
        keyword = 'subscriberInsId';
        break;
      // 集团服务号码查询
      case 'accessNum':
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByAccessNum.do';
        keyword = 'accessNum';
        break;
      // 个人号码查询
      case 'personNum':
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByPersonNum.do';
        keyword = 'accessNum';
        break;
      // 宽带账号
      case 'widenetAccessNum':
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByWidenetAccessNum.do';
        keyword = 'accessNum';
        break;
      // 集团客户经理编码
      case 'operatorCode':
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByOperatorCode.do';
        keyword = 'operatorCode';
        break;
      // 集团编码
      case 'groupId':
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByGroupId.do';
        keyword = 'groupId';
        break;
      // 集团账号
      case 'acctId':
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByAcctId.do';
        keyword = 'acctId';
        break;
      // 集团名称查询
      case 'groupName':
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByGroupName.do';
        keyword = 'groupName';
        break;
      // 成员服务号码
      case 'mebAccessNum':
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByMebAccessNum.do';
        keyword = 'accessNum';
        break;
      // 默认列表查询
      default:
        apiName = 'portal/GroupEnterpriseController/queryGroupEnterpriseByOperatorCode.do';
        keyword = 'operatorCode';
        break;
    }

    request(apiName, {
      method: 'POST',
      data: {
        [keyword]: value,
        pageFlag: '1',
        pageInfo: {
          currentPage: pageNum,
          pageSize: 5,
        },
      },
    })
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
          setCustomerList(resultObject?.rspParam?.busiInfo?.outData?.map((item, idx) => ({
            ...item,
            _key: `${item.ORGA_ENTERPRISE_ID}_${idx}`,
          })));
          setTotal(parseInt(resultObject?.rspParam?.pageInfo?.recordCount, 10));
          setLoading(false);
        } else {
          message.error(resultMsg);
          setLoading(false);
          setTotal(0);
          setCustomerList([]);
        }
      })
      .catch(() => {
        setLoading(false);
        setCustomerList([]);
        setTotal(0);
        message.error('查询失败');
      })
      .always(() => {
        setLoading(false);
      });
  };

  const handleSelect = record => {
    setShowSearch(false);
    setSearchValue(record.GROUP_NAME || '');
    props.callback?.(record);
  };

  return (
    <div ref={searchBoxRef} className={style.custSearchContainer}>
      <InputGroup compact className={style.searchGroup}>
        <Select value={searchType} placeholder="请选择" onChange={setSearchType} className={style.customeSelect}>
          {queryFields.map(({ disable, key, label }) => (
            <Select.Option disabled={disable} key={key} value={key}>
              {label}
            </Select.Option>
          ))}
        </Select>
        <Input.Search
          ref={inputRef}
          value={searchValue}
          allowClear
          className={style.customeInput}
          onSearch={fetchCustomerList}
          onFocus={() => {
            setShowSearch(true);
          }}
          onChange={e => {
            setSearchValue(e?.target?.value || '');
          }}
          onPressEnter={e => {
            const getValue = e.target.value;
            fetchCustomerList(getValue);
            inputRef.current?.blur();
          }}
        />
      </InputGroup>
      {showSearch && (
        <div className={style.customerList}>
          <List
            size="small"
            bordered
            dataSource={customerList}
            loading={loading}
            locale={{
              emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无数据" />,
            }}
            renderItem={item => (
              <List.Item className={style.customerItem} onClick={() => handleSelect(item)} key={item._key}>
                【 {item.GROUP_NAME}】({item.ORG_ID})
              </List.Item>
            )}
            pagination={{
              onChange: page => {
                setPageNum(page);
                fetchCustomerList(searchValue);
              },
              pageSize: 5,
              size: 'small',
              hideOnSinglePage: false,
              showSizeChanger: false,
              className: style.pagination,
              total,
              position: 'bottom',
              align: 'center',
            }}
          />
        </div>
      )}
    </div>
  );
};

export default UserSearch;
