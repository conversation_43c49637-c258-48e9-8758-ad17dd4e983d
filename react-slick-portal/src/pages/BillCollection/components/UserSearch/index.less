.custSearchContainer {
  position: relative;
}

.searchGroup {
  display: flex;
  width: 100%;
  border-radius: 4px;

  :global {
    .ant-select {
      width: 40% !important;
    }

    .ant-input-group-compact {
      display: flex;
      width: 100%;
    }

    .ant-input-search {
      width: 60% !important;
    }
  }
}

.customeSelect {
  border-radius: 4px 0 0 4px !important;

  :global {
    .ant-select-selection {
      height: 32px !important;
      background: rgba(93, 171, 230, 0.08) !important;
      border: 1px solid #5DABE6 !important;
      border-right: none !important;
      font-weight: 400 !important;
      font-size: 13px !important;
      color: #5DABE6 !important;

      .ant-select-selection-item {
        line-height: 30px !important;
        color: #5DABE6 !important;
      }
    }

    .ant-select-selection__placeholder {
      font-size: 13px !important;
      color: #5DABE6 !important;
    }

    .ant-select-selection__rendered {
      height: 32px !important;
      line-height: 32px !important;
      font-weight: 400 !important;

      .ant-select-selection-selected-value {
        height: 32px !important;
        line-height: 32px !important;
        font-weight: 400 !important;
      }

    }

    .ant-select-arrow {
      color: #5DABE6 !important;
    }
  }
}

.selectDropdown {
  :global {
    .ant-select-item-option {
      padding: 8px 12px;
      font-size: 13px;

      &:hover {
        background-color: rgba(93, 171, 230, 0.1);
      }

      &-active:not(.ant-select-item-option-disabled) {
        background-color: rgba(93, 171, 230, 0.1);
      }

      &-selected {
        background-color: rgba(93, 171, 230, 0.2);
        color: #5DABE6;
        font-weight: 400;
      }
    }
  }
}

.customeInput {
  border-radius: 0 4px 4px 0 !important;

  :global {
    .ant-input {
      height: 32px !important;
      background: rgba(93, 171, 230, 0.08) !important;
      border: 1px solid #5DABE6 !important;
      border-left: none !important;
      font-weight: 400 !important;
      font-size: 13px !important;
      color: #5DABE6 !important;

      &::placeholder {
        color: rgba(93, 171, 230, 0.6) !important;
      }
    }

    .ant-input-search-icon {
      color: #0085D0;
      border-radius: 0px 0px 0px 0px;
    }

    .ant-input-search-button {
      height: 32px !important;
      border-radius: 0 4px 4px 0 !important;
      background-color: #5DABE6 !important;
      border-color: #5DABE6 !important;
      color: white !important;

      &:hover {
        background-color: #4A99D4 !important;
        border-color: #4A99D4 !important;
      }
    }
  }
}

.customerList {
  position: absolute;
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
  background-color: #fff;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 8px;
  border-radius: 4px;

  :global {
    .ant-list {
      border-radius: 4px;
      overflow: hidden;
    }

    .ant-list-item {
      padding: 10px 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #f0f7ff;
      }
    }

    .ant-list-pagination {
      margin: 8px 16px;
      text-align: center;
      display: flex;
      justify-content: center;
    }

    .ant-pagination-item {
      border-color: #5DABE6;

      a {
        color: #5DABE6;
      }

      &-active {
        background-color: #5DABE6;
        border-color: #5DABE6;

        a {
          color: white;
        }
      }

      &:hover {
        border-color: #4A99D4;

        a {
          color: #4A99D4;
        }
      }
    }

    .ant-pagination-prev, .ant-pagination-next {
      .ant-pagination-item-link {
        color: #5DABE6;
        border-color: #5DABE6;

        &:hover {
          color: #4A99D4;
          border-color: #4A99D4;
        }
      }
    }

    .ant-spin-container {
      min-height: 120px;
    }

    .ant-empty {
      margin: 24px 0;
    }

    .ant-list-header {
      padding: 12px 16px;
      background-color: #f7f7f7;
      font-weight: 500;
    }
  }
}

.listHeader {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.customerItem {
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.itemContent {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.itemName {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.nameLabel {
  color: #666;
  font-weight: normal;
  margin-right: 4px;
}

.itemValue {
  color: #666;
  font-size: 13px;
}

.valueLabel {
  color: #999;
  margin-right: 4px;
}

.pagination {
  text-align: center;

  :global {
    .ant-pagination-item-active {
      background-color: #1890ff;
      border-color: #1890ff;

      a {
        color: white;
      }
    }
  }
}
