import React, { useImperativeHandle, useState } from 'react';
import { Card, Result, Button, Descriptions, Skeleton } from 'antd';
import { connect } from 'dva';
import styles from './index.less';

const Complete = props => {
  const { goToStep, cRef, paymentData } = props;
  const [loading, setLoading] = useState(true);
  useImperativeHandle(cRef, () => ({
    back: () => goToStep(1),
  }));
  const handleDownload = () => {};
  const handlePush = () => {};
  return (
    <div className={styles.container}>
      <Card>
        <Result
          status="success"
          title="缴费成功"
          subTitle="您的缴费已经成功处理"
          extra={[
            <Button type="primary" key="console" onClick={handleDownload}>
              下载
            </Button>,
            <Button type="primary" key="console" onClick={handlePush}>
              推送
            </Button>,
            <Button type="primary" key="console" onClick={() => goToStep(1)}>
              继续缴费
            </Button>,
          ]}
        >
          <Skeleton loading={loading} paragraph={{ rows: 2 }}>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="缴费流水">{paymentData?.serial}</Descriptions.Item>
              <Descriptions.Item label="缴费金额">{paymentData?.amount}</Descriptions.Item>
              <Descriptions.Item label="客户号码">{paymentData?.customerId}</Descriptions.Item>
              <Descriptions.Item label="推送号码">{paymentData?.pushId}</Descriptions.Item>
            </Descriptions>
          </Skeleton>
        </Result>
      </Card>
    </div>
  );
};

export default connect()(Complete);
