import React from 'react';
import dynamic from 'umi/dynamic';
import StepWizard from '@/components/StepWizard';
import PageLoading from '@/components/PageLoading';
import Edit from '@/pages/GroupCertManage/components/Edit';

const List = dynamic({
  loader: () => import('./components/List'),
  loading: PageLoading,
});

function Index() {
  return (
    <StepWizard isLazyMount>
      <List />
      <Edit destroy />
    </StepWizard>
  );
}

export default Index;
