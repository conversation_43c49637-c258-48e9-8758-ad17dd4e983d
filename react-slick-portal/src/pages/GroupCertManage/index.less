.formContent {
  position: relative;
  height: 500px; /* 父容器需有明确高度 */
  overflow: auto; /* 允许父容器滚动 */
  transform: translateZ(0); /* 关键：创建层叠上下文 */

}

.formDrawerBottom {
  padding: 10px 16px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}

.textAreaFormItem {
  :global {
    .ant-form-item-control {
      .ant-form-item-children {
        height: 100% !important;
      }

      textarea {
        height: auto;
      }
    }
  }
}

.inputStyle{
  width: 80% !important;
}

