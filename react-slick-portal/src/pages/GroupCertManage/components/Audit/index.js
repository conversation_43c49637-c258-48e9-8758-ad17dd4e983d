import React, { useState } from 'react';
import { Button, Col, Form, Input, Modal, Row, Select, Spin } from 'antd';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};
const Audit = props => {
  const { visible, onOk, onCancel, form } = props;
  const { getFieldDecorator } = form;
  const [loading, setLoading] = useState(false);
  const [auditList, setAuditList] = useState([]);
  const getNextAuditList = () => {};
  const handleChange = val => {
    const currentSelect = auditList.find(item => item.AUDIT_STAFF_ID === val)?.AUDIT_STAFF_NAME;
    form.setFieldsValue({
      auditStaffName: currentSelect,
    });
  };
  const handleOk = () => {
    form.validateFields((errors, values) => {
      if (!errors) {
        onOk({ ...values });
      }
    });
  };
  return (
    <Modal title="审批" visible={visible} footer={null} onOk={handleOk} onCancel={onCancel} width="500px" destroyOnClose>
      <Spin spinning={loading}>
        <Form>
          <Row>
            <Col span={24}>
              <Form.Item label="审核人" {...formItemLayout}>
                {getFieldDecorator('AUDIT_STAFF_ID', {
                  rules: [
                    {
                      required: true,
                      message: '审核人不能为空',
                    },
                  ],
                })(
                  <Select style={{ width: '100%' }} allowClear showSearch placeholder="请选择" onChange={handleChange}>
                    {auditList.map(item => (
                      <Select.Option key={item.CHECK_OP_ID} value={item.CHECK_OP_ID}>
                        {item.CHECK_OP_NAME}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={24} hidden>
              <Form.Item label="审核人姓名" {...formItemLayout}>
                {getFieldDecorator('AUDIT_STAFF_NAME')(<Input disabled />)}
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col className="text-center">
              <Button onClick={onCancel} className="margin-right">
                取消
              </Button>
              <Button type="primary" onClick={handleOk}>
                确定
              </Button>
            </Col>
          </Row>
        </Form>
      </Spin>
    </Modal>
  );
};
export default Form.create()(Audit);
