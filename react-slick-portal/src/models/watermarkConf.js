import request from '@/utils/request';

export async function getWatermarkConfig() {
  return request('portal/SystemWatermarkConfController/getDefaultWatermark.do', {
    method: 'get',
  });
}

export async function getWatermarkMark() {
  return request('portal/NmgDataDictController/getValidValueByCode.do', {
    method: 'get',
    data: {
      groupCode: 'SYSTEM_WATERMARK_CONF',
      paramCode: 'ENABLE_WATERMARK',
      defValue: '0',
    },
  });
}

export default {
  namespace: 'watermarkConf',

  state: {
    config: {
      coordinateX: -100,
      coordinateY: 30,
      watermarkText: null,
      fontColor: 'red',
      fontSize: '13px',
      transparency: 0.06,
      inclination: 15,
    },
    enableWatermark: false,
    initCompleted: false,
  },

  effects: {
    *init(_, { call, put }) {
      const watermarkMark = yield call(getWatermarkMark);
      yield put({
        type: 'updateStatus',
        payload: { paramValue: watermarkMark },
      });
      if (watermarkMark === '1') {
        const watermarkConfig = yield call(getWatermarkConfig);
        yield put({
          type: 'updateConfig',
          payload: { watermarkConfig: watermarkConfig.resultObject },
        });
      }
      yield put({ type: 'initializationCompleted' });
    },
  },

  reducers: {
    updateStatus(state, { payload }) {
      const { paramValue } = payload;
      return {
        ...state,
        enableWatermark: paramValue === '1',
      };
    },
    updateConfig(state, { payload }) {
      const { watermarkConfig } = payload;
      return {
        ...state,
        config: { ...state.config, ...watermarkConfig },
      };
    },
    initializationCompleted(state) {
      return {
        ...state,
        initCompleted: true,
      };
    },
    reset(state) {
      return {
        ...state,
        initCompleted: false,
      };
    },
  },
};
