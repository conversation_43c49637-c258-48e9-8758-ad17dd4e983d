/* eslint-disable no-unused-vars */
export default {
  namespace: 'vaultApproval',
  state: {
    data: {
      isShowfirst: false, // 金库弹窗1
      isShowSecond: false, // 金库弹窗2
      functionString: '', // 回调函数的名称
      approvalOption: { // 金库审批选项
        BUSI_SCENE_ID: '',
        OPER_CODE: '',
        RES_NUM: '',
        appOperJKStatus: {
            supportApplyType: '',
            authMode: [],
            approverList: [],
        },
      },
      approvalCreate: {}, // 金库审批认证 回填
    },
  },

  effects: {
    // 更新状态
    *VaultUpData({ payload }, { call, put }) {
      yield put({
        type: 'update',
        payload: { ...payload },
      });
    },
    // 重置弹窗状态1
    *VaultResetDataOne({ payload }, { put }) {
      yield put({
        type: 'resetDataPopOne',
        payload: { ...payload },
      });
    },
    // 重置弹窗2状态
    *VaultResetDataTwo({ payload }, { put }) {
      yield put({
        type: 'resetDataPopTwo',
        payload: { ...payload },
      });
    },
    *refreshDataPop({ payload }, { put }) {
      yield put({
        type: 'refreshDataPopAll',
        payload: { ...payload },
      });
    },
    *TimesPass({ payload }, { put }) {
      yield put({
        type: 'timePass',
        payload: { ...payload },
      });
    },
  },

  reducers: {
    update(state, { payload }) {
      console.log('展开赋值==', payload.approvalOptionParams, payload.approvalCreate);
      // const params = payload;
      return {
        ...state,
        data: { ...state.data, ...payload.approvalOptionParams, ...payload.approvalCreate },
      };
    },
    resetDataPopOne(state, { payload }) {
      const params = {
        isShowfirst: false, // 金库弹窗1
        functionString: '', // 回调函数名称
        approvalOption: { // 金库审批选项
          BUSI_SCENE_ID: '',
          OPER_CODE: '',
          RES_NUM: '',
          appOperJKStatus: {
              supportApplyType: '',
              authMode: [],
              approverList: [],
          },
      },
      };
      return {
        ...state,
        data: { ...state.data, ...params },
      };
    },
    resetDataPopTwo(state, { payload }) {
      const params = {
        isShowSecond: false, // 金库弹窗2
        functionString: '', // 回调函数名称
        approvalCreate: {}, // 金库审批选项
      };
      return {
        ...state,
        data: { ...state.data, ...params },
      };
    },
    // 次数申请 更改
    timePass(state, { payload }) {
      return {
        ...state,
        data: { ...state.data, ...payload },
      };
    },
    refreshDataPopAll(state, { payload }) {
      const params = {
        isShowfirst: false, // 金库弹窗1
        isShowSecond: false, // 金库弹窗2
        functionString: '', // 回调函数名称
        approvalOption: { // 金库审批选项
          BUSI_SCENE_ID: '',
          OPER_CODE: '',
          RES_NUM: '',
          appOperJKStatus: {
              supportApplyType: '',
              authMode: [],
              approverList: [],
          },
        },
        approvalCreate: {}, // 金库审批选项
      };
      return {
        ...state,
        data: params,
      };
    },
  },
};
