import {notification} from 'antd';
import forEach from 'lodash/forEach';
import PubSub from 'pubsub-js';
import {
  getAggregationInfo,
  getAggregationThreeeInfo,
  getAggregationConcernfo,
  AggregationConcernControllerSave,
} from '@/services/toDoItems';

// 首页待办模块
export default {
  namespace: 'toDoItems',

  state: {

  },

  effects: {
    * setToDoItemsState({ payload }, { put }) {
      yield put({
        type: 'save',
        payload: {
          ...payload,
        },
      });
    },

    * getAggregationInfo({ payload }, { call }) {
      const res = yield call(getAggregationInfo, payload);
      return res;
    },

    * getAggregationConcernfo({ payload }, { call }) {
      const res = yield call(getAggregationConcernfo, payload);
      return res;
    },

    * getAggregationThreeeInfo({ payload }, { call }) {
      const res = yield call(getAggregationThreeeInfo, payload);
      return res;
    },

    * AggregationConcernControllerSave({ payload }, { call }) {
      const res = yield call(AggregationConcernControllerSave, payload);
      return res;
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    clean(state) {
      return {
        ...state,
      };
    },
  },
};
