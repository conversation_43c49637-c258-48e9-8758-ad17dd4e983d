import { getTempletModule, qrySystemInfoLinks } from '@/services/dashboard';

// 首页公告列表以及公告管理模块
export default {
  namespace: 'dashboard',

  state: {
    current: [],
    systemLinks: [],
  },

  effects: {
    *getSystemLink(_, { call, put }) {
      const res = yield call(qrySystemInfoLinks);
      if (res && Array.isArray(res.resultObject)) {
        yield put({
          type: 'saveSystemLinks',
          payload: { systemLinks: res.resultObject },
        });
      }
    },
    *getTempletModule(_, { call, put }) {
      const res = yield call(getTempletModule, {
        confType: '1', // 是工作台配置.
        terminalType: 1000, // 1000表示PC端
      });
      //  moduleRels表示当前模板包含的portles
      const { resultObject = {}, resultCode } = res;
      const { templetId, moduleRels } = resultObject;
      let current = [];
      if (resultCode === '0' && templetId && Array.isArray(moduleRels)) {
        // 兼容原来没有分组的数据，如果没有分组，则全部放到“未分组”中
        let newData = [];
        if (!moduleRels[0]?.jsonContent) {
          newData = [{ groupName: '未分组', groupShow: true, groupDesc: '', groupIndex: 0, moduleList: [...moduleRels] }];
        } else {
          newData = JSON.parse(moduleRels[0].jsonContent);
      }
        current = newData;
      }

      yield put({
        type: 'save',
        payload: { templetId, current },
      });
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveSystemLinks(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
