export default {
  namespace: 'micro',

  state: {
    microNameMap: {}, // 当前开启的micro页签们
  },

  effects: {},

  reducers: {
    add(state, { payload }) {
      return {
        ...state,
        microNameMap: {
          ...state.microNameMap,
          [payload]: 'open',
        },
      };
    },
    hide(state, { payload }) {
      return {
        ...state,
        microNameMap: {
          ...state.microNameMap,
          [payload]: 'hidden',
        },
      };
    },
    // 唤醒被hide的micro
    awaken(state, { payload }) {
      return {
        ...state,
        microNameMap: {
          ...state.microNameMap,
          [payload]: 'open',
        },
      };
    },
    remove(state, { payload }) {
      return {
        ...state,
        microNameMap: {
          ...state.microNameMap,
          [payload]: undefined,
        },
      };
    },
  },
};
