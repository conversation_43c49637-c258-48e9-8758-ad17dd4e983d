import { notification } from 'antd';
import PubSub from 'pubsub-js';
import forEach from 'lodash/forEach';
import {
  getUnReadBulletin,
  getValueByCode,
  queryByObjId,
  getBulletinRcvObjRels,
  getValuesList,
} from '@/services/bulletin';

/**
 * 1. 首页公告模块
 * - 获取公告数据、允许展示的条数和轮询间隔，并开启轮询定时更新列表
 * - 获取重要信息的轮询间隔和允许弹窗的数量，然后开始轮询重要公告，若存在则右下角弹出提示
 *
 * 2. 公告管理模块
 * - 公告详情初始化
 */
export default {
  namespace: 'newBulletin',

  state: {
    list: [], // 首页公告数据
    isTopMap: [],
    bulletinTypeMap: [],
    bulletinlevelMap: [],
    rcvTypeMap: [],
    rcvObjNames: [],
    fileList: [],
  },

  effects: {
    *getInitData({ payload }, { call, put }) {
      // eslint-disable-next-line prefer-const
      let [arr, popupInterval, popupCount, bulletinInterval, bulletinShowCount] = yield [
        // 查询公告数据，预期返回格式，如：
        //   [{
        //     "id": 5188731,
        //     "createDate": "2019-11-03 20:21:31",
        //     "createStaff": 1,
        //     "updateDate": "2019-11-05 19:52:09",
        //     "updateStaff": 1,
        //     "bulletinId": 344494,
        //     "rcvType": "1200",
        //     "rcvId": 1,
        //     "statusCd": "1100",
        //     "bulletin": {
        //         "id": 344494,
        //         "createDate": "2019-11-03 20:21:31",
        //         "createStaff": 1,
        //         "updateDate": "2019-11-03 20:21:31",
        //         "bulletinId": 344494,
        //         "bulletinTitle": "火麒麟系统演示通知",
        //         "bulletinContent": "系统将于2019/11/04日进行功能演示，请确保各系统环境运行稳定。",
        //         "bulletinType": "3000",
        //         "bulletinLevel": "1200",
        //         "isTop": 0,
        //         "launchStaff": 1,
        //         "launchOrg": 10008,
        //         "effDate": "2019-11-03 20:20:52",
        //         "expDate": "2037-01-01 00:00:00",
        //         "statusCd": "1000",
        //         "launchStaffName": "管思坤",
        //         "launchOrgName": "四川省",
        //         "realTime": false,
        //         "bulletinTypeName": "系统公告",
        //         "bulletinLevelName": "低",
        //         "statusCdName": "有效"
        //     },
        //     "bulletinRcvObjRelId": 5188731,
        //     "rcvTypeName": "用户",
        //     "statusCdName": "已读"
        // }]
        call(getUnReadBulletin, {
          bulletinType: null,
          bulletinLevel: null,
          statusCd: null,
        }),
        // 返回公告弹窗轮询间隔 预期格式 如："600000"
        call(getValueByCode, {
          groupCode: 'BULLETIN_GROUP_CODE',
          paramCode: 'getPopupBulletinCycle',
        }),
        // 返回右下角公告弹窗展示个数 预期格式 如："3" 表示展示3个
        call(getValueByCode, {
          groupCode: 'BULLETIN_GROUP_CODE',
          paramCode: 'popupBulletinCount',
        }),
        // 返回公告轮询间隔 预期格式 如："600000"
        call(getValueByCode, {
          groupCode: 'BULLETIN_GROUP_CODE',
          paramCode: '11getUnReadBulletinCycle',
        }),
        // 返回公告展示个数 预期格式 如："3" 表示展示3个
        call(getValueByCode, {
          groupCode: 'BULLETIN_GROUP_CODE',
          paramCode: 'unReadBulletinShowCount',
        }),
      ];

      if (!Array.isArray(arr) || arr.length === 0) {
        return;
      }

      bulletinShowCount = parseInt(bulletinShowCount, 10);
      bulletinInterval = parseInt(bulletinInterval, 10);
      popupInterval = parseInt(popupInterval, 10);
      popupCount = parseInt(popupCount, 10);

      yield put({
        type: 'save',
        payload: {
          list: arr.length > bulletinShowCount ? arr.slice(0, 5) : arr,
          popupInterval: popupInterval || payload.defaultPopupInterval,
          popupCount: popupCount || payload.defaultPopupCount,
          bulletinShowCount: bulletinShowCount || payload.defaultBulletinShowCount,
          bulletinInterval: bulletinInterval || payload.defaultBulletinInterval,
        },
      });
    },
    *pollBulletin(_, { call, put, select }) {
      // 轮询，定时更新公告列表。
      const arr = yield call(getUnReadBulletin, {
        bulletinType: null,
        bulletinLevel: null,
        statusCd: null,
      });
      const bulletinShowCount = yield select(state => state.bulletin.bulletinShowCount);

      if (!Array.isArray(arr) || arr.length === 0) {
        return;
      }

      yield put({
        type: 'save',
        payload: {
          list: arr.length > bulletinShowCount ? arr.slice(0, bulletinShowCount) : arr,
        },
      });
    },
    *pollImportant(_, { call, select }) {
      // 轮询，若有重要信息则右下角弹窗。
      const arr = yield call(getUnReadBulletin, {
        bulletinType: 3000,
        bulletinLevel: 1000,
        statusCd: 1000,
      });
      const popupCount = yield select(state => state.bulletin.popupCount);

      if (!Array.isArray(arr) || arr.length > 0) {
        arr.forEach((item, i) => {
          if (i < popupCount) {
            notification.open({
              message: item.bulletin.bulletinTitle,
              description: item.bulletin.bulletinContent,
              placement: 'bottomRight',
              onClick: () => {
                // 触发弹窗
                PubSub.publish('showNotification', item);
              },
            });
          }
        });
      }
    },

    /**
     * 公告管理 - 表单元素码表值
     */
    *getMap(_, { call, put }) {
      try {
        const [resp1, resp2, resp3, resp4] = yield [
          call(getValuesList, { busiNbr: 'Bulletin', propertyName: 'isTop' }),
          call(getValuesList, { busiNbr: 'Bulletin', propertyName: 'bulletinType' }),
          call(getValuesList, { busiNbr: 'Bulletin', propertyName: 'bulletinLevel' }),
          call(getValuesList, { busiNbr: 'BulletinRcvObjRel', propertyName: 'rcvType' }),
        ];

        yield put({
          type: 'save',
          payload: {
            isTopMap: Array.isArray(resp1) ? resp1 : [],
            bulletinTypeMap: Array.isArray(resp2) ? resp2 : [],
            bulletinlevelMap: Array.isArray(resp3) ? resp3 : [],
            rcvTypeMap: Array.isArray(resp4) ? resp4 : [],
          },
        });
      } catch (e) {
        // 获取初始数据失败
      }
    },

    /**
     * 公告详情中 接收对象和附件信息
     * @params {object} payload {viewMode,detail:{ bulletinId, rcvType}}
     */
    *getFileListAndRcvObjNames({ payload }, { call, put }) {
      try {
        const {
          detail: { bulletinId, rcvType },
        } = payload;
        const [resp1, resp2] = yield [
          call(getBulletinRcvObjRels, { bulletinId, rcvType }),
          call(queryByObjId, { objId: bulletinId, objType: '1000' }),
        ];

        // 拼写接收对象. 1000直接写'所有人'
        const names = [];
        if (Array.isArray(resp1)) {
          if (rcvType !== '1000') {
            // IMPROVE: 神仙设计
            // item.xxx 这里的xxx对应不同的接收对象类型。比如'组织'就是item.organization
            // 在选择接收对象是，也会根据不同的rcvType 挂载不同的key
            forEach(resp1, item => {
              if (item.organization) {
                names.push(item.organization);
              } else if (item.systemUser) {
                names.push(item.systemUser);
              } else if (item.systemRoles) {
                names.push(item.systemRoles);
              } else if (item.commonRegion) {
                names.push(item.commonRegion);
              }

              // names = `${names +
              //   (!item.organization ? '' : item.organization.orgName) +
              //   (!item.systemUser ? '' : item.systemUser.userName) +
              //   (!item.systemRoles ? '' : item.systemRoles.sysRoleName) +
              //   (!item.commonRegion ? '' : item.commonRegion.regionName)},`;
            });
          }
        }

        yield put({
          type: 'save',
          payload: {
            rcvObjNames: names,
            // 必须补上uid 否则ant upload组件会报错
            fileList: Array.isArray(resp2)
              ? resp2.map(item => {
                const { docId, docNbr, fileName, fileGetUrl } = item;
                return {
                  uid: docId,
                  name: fileName,
                  status: 'done',
                  url: fileGetUrl,
                  docId,
                  fileName,
                  fileGetUrl,
                  docNbr,
                };
              })
              : [],
          },
        });
      } catch (e) {
        // 获取初始数据失败
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    clean(state) {
      return {
        ...state,
        rcvObjNames: [],
        fileList: [],
      };
    },
  },
};
