import {notification} from 'antd';
import forEach from 'lodash/forEach';
import PubSub from 'pubsub-js';
import {
  getSceneMenuInfo,
  getSceneNodeTableInfo,
  getSceneMenuQuantity,
  selectList,
  doBusiSystemTempTransform,
} from '@/services/dictCommonNav';

// 根据sceneId聚合场景
const dealSceneNode = (data) => {
  const nodeCodes = [];
  data.forEach(item => {
    const {
      childrenNodeList,
    } = item;
    // console.log(item);
    if (childrenNodeList && childrenNodeList.length > 0) {
      childrenNodeList.forEach(i => {
        if (i.childSceneNodeInfo && i.childSceneNodeInfo.length) {
          i.childSceneNodeInfo.forEach(n => {
            if (n.sceneNodeCode) nodeCodes.push(n.sceneNodeCode);
          });
        }
        if (i.sceneNodeCode) nodeCodes.push(i.sceneNodeCode);
      });
    }
  });
  return nodeCodes;
};
// 首页通用导航模块
export default {
  namespace: 'dictCommonNav',

  state: {
    sceneList: [], // 场景数据原子能力二级环节数据集合
    sceneNodeTableInfo: {}, // 智能表格数据
    // sceneNodeSubrelList: [], // 二级环节
    sceneNodeQuantity: {},
  },

  effects: {
    * setDictCommonNavState({ payload }, { put }) {
      yield put({
        type: 'save',
        payload: {
          ...payload,
        },
      });
    },

    * getInitData({ payload }, { call, put, select }) {
      // eslint-disable-next-line prefer-const

      const menuInfo = yield call(getSceneMenuInfo, payload);

      if (!Array.isArray(menuInfo) || menuInfo.length === 0) {
        return;
      }
      const nodeCodes = dealSceneNode(menuInfo);
      const { resultObject: sceneNodeQuantity } = yield call(getSceneMenuQuantity, {
        codes: nodeCodes,
        param: {},
      });

      yield put({
        type: 'save',
        payload: {
          sceneList: menuInfo,
          sceneNodeQuantity,
        },
      });
      return menuInfo;
    },

    * getSceneMenuQuantity({ payload }, { call, put, select }) {
      const sceneList = yield select(state => state.dictCommonNav.sceneList);

      const nodeCodes = dealSceneNode(sceneList);

      const { resultObject: sceneNodeQuantity } = yield call(getSceneMenuQuantity, {
        codes: nodeCodes,
        ...payload,
      });

      yield put({
        type: 'save',
        payload: {
          sceneNodeQuantity,
        },
      });
    },

    * getSceneNodeTableInfo({ payload }, { call, put }) {
      const sceneNodeTableInfo = yield call(getSceneNodeTableInfo, payload);

      yield put({
        type: 'save',
        payload: {
          sceneNodeTableInfo,
        },
      });
    },

    * doBusiSystemTempTransform({ payload }, { call, put, select }) {
      const { resultObject } = yield call(doBusiSystemTempTransform, payload);
      const sceneNodeTableInfo = yield select(state => state.dictCommonNav.sceneNodeTableInfo);

      yield put({
        type: 'save',
        payload: {
          sceneNodeTableInfo: { ...sceneNodeTableInfo, tableParams: resultObject },
        },
      });
      return resultObject;
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    clean(state) {
      return {
        ...state,
        rcvObjNames: [],
        fileList: [],
      };
    },
  },
};
