import {
  getTaskInfo,
  getMessageInfo,
  getBulletinInfo,
  getTaskSubTypeInfo,
} from '@/services/unifiedTodo';

// 统一待办
export default {
  namespace: 'unifiedTodo',

  state: {
    todoTypeList: [],
    taskMenuList: [],
    messageTypeList: [],
    bulletinTypeList: [],
    freshInterval: null, // 刷新定时器实例
  },

  effects: {
    *todoInit(_, { call, put }) {
      const typeList = [];
      const [response1, response2, response3] = yield [
        call(getTaskInfo),
        call(getMessageInfo),
        call(getBulletinInfo),
      ];

      // 待办任务
      if (response1?.resultObject) {
        const { menuName, totalCount = 0, item = [] } = response1.resultObject;
        const taskObj = {
          typeCode: 'task',
          typeName: menuName,
          todoNum: totalCount,
        };
        typeList.push(taskObj);

        const list = item.map(i => ({ ...i, value: i.menuCode, name: i.menuName, count: i.count }));
        yield put({
          type: 'save',
          payload: {
            taskMenuList: list,
          },
        });
      }

      // 待阅消息
      if (response2?.resultObject) {
        const { menuName, totalCount = 0, item = [] } = response2.resultObject;
        const messageObj = {
          typeCode: 'message',
          typeName: menuName,
          todoNum: totalCount,
        };
        typeList.push(messageObj);

        const list = item.map(i => ({ ...i, count: i.msgTypeCount }));
        yield put({
          type: 'save',
          payload: {
            messageTypeList: list,
          },
        });
      }

      // 待读公告
      if (response3?.resultObject) {
        const { menuName, totalCount = 0, item = [] } = response3.resultObject;
        const bulletinObj = {
          typeCode: 'bulletin',
          typeName: menuName,
          todoNum: totalCount,
        };
        typeList.push(bulletinObj);

        const list = item.map(i => ({ ...i, count: i.bulletinTypeCount }));
        yield put({
          type: 'save',
          payload: {
            bulletinTypeList: list,
          },
        });
      }
      yield put({
        type: 'save',
        payload: {
          todoTypeList: typeList,
        },
      });
    },

    // 获取待办任务子菜单，只获取一次，若已经有值了则不再处理
    *getTaskSubType({ payload }, { select, call, put }) {
      const { menuCode } = payload;
      const taskMenuList = yield select(state => state.unifiedTodo.taskMenuList);
      const result = yield call(getTaskSubTypeInfo, menuCode);
      if (Array.isArray(result?.resultObject)) {
        const newList = taskMenuList.map(item => {
          if (item.menuCode === menuCode) {
            return { ...item, subTypeList: result.resultObject };
          }
          return item;
        });
        yield put({
          type: 'save',
          payload: {
            taskMenuList: newList,
          },
        });
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveFreshInterval(state, { payload }) {
      return {
        ...state,
        freshInterval: payload,
      };
    },
    clearFreshInterval(state) {
      return {
        ...state,
        freshInterval: null,
      };
    },
  },
};
