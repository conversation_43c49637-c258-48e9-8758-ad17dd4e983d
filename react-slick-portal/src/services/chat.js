import request from '@/utils/request';

// 查询用户状态
export async function queryUserStatus(params) {
  return request('ids-busi-service-isupport/governmentController/queryUserStatus', {
    data: params,
  });
}

// 用户在线离线更新
export async function updateUserStatus(params) {
  return request('ids-busi-service-isupport/governmentController/updateUserStatus', {
    data: params,
  });
}

// 查询是否有在线离线按钮
export async function queryServiceUser(params) {
  return request('ids-busi-service-isupport/governmentController/queryServiceUser', {
    data: params,
  });
}
