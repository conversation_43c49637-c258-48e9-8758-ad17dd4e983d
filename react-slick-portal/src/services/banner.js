import request from '@/utils/request';

// 查询
export async function getHomePageTemplate(params) {
  return request('portal/HomePageTemplateController/getHomePageTemplate.do', {
    data: params,
  });
}

// 修改
export async function updateHomePageTemplate(params) {
  return request('portal/HomePageTemplateController/saveTemplateContent', {
    data: {
      templateId: 2000,
      templateName: '产品大厅',
      ...params,
    },
  });
}

// 轮播图新增
export async function creatSysImageConf(params) {
  return request('orgauth/SystemImageConfController/creatSysImageConf.do', {
    data: params,
  });
}

/**
 * 轮播图生失效
 * @param {*} params
 * @statusCd 1000 有效，1100 无效
 * @returns
 */
export async function updateSysImageConf(params) {
  return request('orgauth/SystemImageConfController/updateSysImageConf.do', {
    data: params,
  });
}

// 轮播图删除
export async function deleteImageConfById(confId) {
  return request(`orgauth/SystemImageConfController/deleteImageConfById.do?confId=${confId}`);
}

// 系统图片列表查询
export async function selectSysImageConfGrid(params) {
  return request('orgauth/SystemImageConfController/selectSysImageConfGrid.do', {
    data: params,
  });
}

// 工作台查询轮播图数据
export async function qrySysBannerImgs(confType) {
  return request(`orgauth/SystemImageConfController/qrySysBannerImgs.do?confType=${confType}`);
}
