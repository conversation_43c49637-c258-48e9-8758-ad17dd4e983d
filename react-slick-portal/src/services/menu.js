import request from '@/utils/request';
import { getItem, transformToArray } from '@/utils/utils';

const mapping = {

  /**
   * 菜单级别
   */
  MENU_LEVEL_FIST: 0, // 一级
  MENU_LEVEL_SECOND: 1, // 二级
  MENU_LEVEL_THIRD: 2, // 三级
  MENU_LEVEL_CUSTOMER_VIEW: 1300, // 客户视图
  MENU_LEVEL_CUSTOMER_WORK_BENCH: 1400, // 客户视图工作台
  MENU_LEVEL_WORK_BENCH: -1, // 工作台菜单
  /**
   * STF-C-0013    功能菜单类型    1000    目录菜单
   * STF-C-0013    功能菜单类型    1100    叶子菜单
   */
  MENU_TYPE_DIR: 1000, // 目录
  MENU_TYPE_LEAF: 1100, // 叶子菜单
  /**
   * 菜单加密方式
   */
  MENU_ENCYPT_TYPE_NUll: '1000', // 不加密
  MENU_ENCYPT_TYPE_MD5: '1001', // 加密
  /**
   * 菜单打开方式
   */
  MENU_OPEN_MODE_WORK_BENCH_LABEL: '1', // 工作台标签页打开
  MENU_OPEN_MODE_POPUP: '2', // 弹出框打开
  MENU_OPEN_MODE_NEW_BROWSER: '3', // 新开浏览器打开
  MENU_OPEN_MODE_BROWSER_LABEL: '4', // 浏览器新标签打开
  /**
   * 有效状态
   */
  COMMON_STATUS_VALIDATE: '1000',

  /**
   * 收藏夹菜单配置
   * @type {number}
   */
  COLLECT_MENU_SIZE_DEFAULT: 10,
  COLLECT_MENU_SIZE: 'COLLECT_MENU_SIZE',
  COLLECT_GROUP_CODE: 'COLLECT_GROUP_CODE',

  /**
   *收藏夹菜单左侧显示个数
   */
  COLLECT_MENU_SHOW_SIZE: 'COLLECT_MENU_SHOW_SIZE',
  COLLECT_MENU_SHOW_SIZE_DEFAULT: 5,
};

export default mapping;

/**
 * 获取全部菜单
 * @return {object[]}
 *
 * 预期回参
 * [
      {
      menuId: 5240502,
      menuName: '页面样例-react',
      menuLevel: 0,
      // 菜单类型, 1000：目录菜单，1100：叶子菜单
      menuType: '1000',
      menuTypeName: '目录菜单',
      // 父节点menuid。0表示根节点
      parMenuId: 0,
      // 菜单排序
      menuIndex: 0,
      menuDesc: 'REACT',
      urlAddr: 'Views',
      statusCd: '1000',
      firstLetter: 'YMYLREACT',
      iconUrl: 'icon-gene-man-manager',
      menuCode: 'TYMH_MENU_YMYLREACT',
      // 打开方式 1：工作台标签页；2:弹窗窗口; 3:新开浏览器; 4: 浏览器新标签
      menuOpenMode: '1',
      // 加密方式 1000:不加密  1001：MD5加密
      paramEncryptType: '1000'
    },
 * ]
 */
export async function getAllMenu() {
  return request('orgauth/FuncMenuController/selectMenuFromSession.do', {
    method: 'get',
  });
}

export async function getRecentMenuInfo() {
  return request('orgauth/SysuserRecentMenuController/getRecentMenuInfo.do', {
    method: 'get',
  });
}

export async function getSysCodeBySysNbr(params) {
  return request('orgauth/SystemInfoController/getSysCodeBySysNbr.do', {
    method: 'get',
    data: params,
  });
}

export async function selectMenuByLetter() {
  return request('orgauth/FuncMenuController/selectMenuByLetter.do', {
    method: 'get',
  });
}

// 根据菜单id查找菜单信息
export async function selectMenuInfoById(params) {
  return request('orgauth/FuncMenuController/selectMenuInfoById.do', {
    method: 'get',
    data: params,
  });
}

// 更新历史菜单列表，更新前先查找
export async function updateRecentMenu(menuId, success) {
  return request(`orgauth/SysuserRecentMenuController/updateRecentMenu.do?menuId=${menuId}`, {
    method: 'put',
    success,
  });
}

/**
 * 获取收藏菜单列表
 * 预期回参（contentId对应all中的menuId）
 * [
    {
        "id": 54583,
        "createDate": "2019-08-06 17:38:48",
        "createStaff": 3044336,
        "updateDate": "2019-08-06 17:38:48",
        "collectId": 54583,
        "sysUserId": 1,
        "collectStaff": 3044336,
        "contentType": "1001",
        "contentId": 276116,
        "collectSort": 1,
        "statusCd": "1000",
        "menuName": "菜单管理",
        "urlAddr": "modules/menu/views/MenuManagementView",
        "iconUrl": "icon-gene-radio-button-un",
        "paramEncryptType": "1000",
        "menuOpenMode": "1",
        "parMenuId": 276729,
        "menuCode": "TYMH_MENU_022"
    }
 ]
 */

export async function getCollectionMenu(params, success) {
  return request('orgauth/SysuserCollectionController/getCollectionMenu.do', {
    method: 'get',
    success,
  });
}

// 清空历史菜单列表
export async function clearRecentMenu(params, success) {
  return request('orgauth/SysuserRecentMenuController/deleteRecentMenu.do', {
    method: 'remove',
    success,
  });
}

// 更新菜单收藏夹
export async function updateCollectionMenu(params) {
  return request('orgauth/SysuserCollectionController/updateUserCollectMenu.do', {
    data: params,
  });
}

// 刷新菜单缓存,预期回参 true or false
export async function fetchMenu() {
  return request('orgauth/FuncMenuController/fetchMenu.do', {
    method: 'get',
  });
}

// 角色高频搜索记录内容查询
export async function getRoleHighSearchList(sysRoleId) {
  return request(`portal/CommonSearchLogController/getHighCommonLog?sysRoleId=${sysRoleId}`, {
    method: 'get',
  }).then(res => transformToArray(res?.resultObject));
}

// 上传用户搜索记录
export async function uploadSearchContent(text) {
  return request(`portal/CommonSearchLogController/createCommonLog?searchType=1000&searchContent=${text}`, {
    method: 'post',
  });
}

// 查询用户搜索记录
export async function getUserSearchHistoryList() {
  return request('portal/CommonSearchLogController/getCommonLog', {
    method: 'get',
  }).then(res => transformToArray(res?.resultObject));
}

// 查询高频推荐菜单
export async function getHighRecommendMenus(sysRoleId) {
  return request(`portal/HighMenuSearchController/getHighMenu?sysRoleId=${sysRoleId}`, {
    method: 'get',
  }).then(res => transformToArray(res?.resultObject));
}

// 根据菜单ID查询菜单关联功能推荐信息
export async function getRecommendMenusByMenuId(sourceId) {
  return request('portal/FuncRecommendController/selectBySysRoleId.do', {
    data: {
      sourceId,
    },
  }).then(res => transformToArray(res?.resultObject));
}

// 获取导航节点相关的菜单信息
export function getNavigateMenus(funcNavigationAttrId) {
  if (!funcNavigationAttrId) {
    return [];
  }
  return request('portal/FuncNavigationController/selectMenu.do', {
    data: {
      funcNavigationAttrId,
    },
  }).then(res => transformToArray(res?.resultObject));
}

// 获取待办任务列表
export function getWaitMenuList() {
  return request('portal/UnifiedTodoController/qryTask.do', {
    method: 'GET',
  }).then(res => res?.resultObject?.taskInfos);
}

// 获取待办任务数量
export function getWaitMenuCount(menuCodeList) {
  return request('portal/LocalWorkbenchController/qryTasksNumbers.do', {
    method: 'post',
    data: {
      menuCodeList,
      headers: {
        bss3SessionId: getItem('user').sessionId,
      },
    },
  }).then(res => res?.resultObject?.[0]);
}

// 获取工作台名称
export function getWorktableName() {
  return request('portal/DataDictController/getWorktableName.do', {
    method: 'get',
  }).then(res => res?.resultObject);
}

// 获取归属系统列表
export async function getSystemInfoList() {
  return request('orgauth/SystemInfoController/getSystemInfoList.do', {
    method: 'GET',
  });
}

// 获取需单点登录所属系统
export function getSsoLoginSystem(params) {
  return request('portal/NmgDataDictController/getDataDictByCode.do', {
    method: 'GET',
    data: params,
  }).then(res => res?.paramValue);
}
