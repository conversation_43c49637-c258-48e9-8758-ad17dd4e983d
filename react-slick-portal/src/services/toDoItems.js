import request from '@/utils/request';

/**
 * 待办业务
 */
export async function getAggregationInfo(params) {
  return request('portal/AggregationFirstInfoController/getAggregationInfo.do', {
    method: 'post',
    data: params,
  });
}

/**
 * 待办业务单个接口查询
 */
export async function getAggregationThreeeInfo(params) {
  return request('portal/AggregationFirstInfoController/getAggregationThreeeInfo.do', {
    method: 'post',
    data: params,
  });
}

/**
 * 待办业务我的关注单个接口查询
 */
export async function getAggregationConcernfo(params) {
  return request('portal/AggregationFirstInfoController/getAggregationConcernfo.do', {
    method: 'post',
    data: params,
  });
}

/**
 * 待办业务我的关注新增修改
 */
export async function AggregationConcernControllerSave(params) {
  return request('portal/AggregationConcernController/save.do', {
    method: 'post',
    data: params,
  });
}
