import request from "@/utils/request";

/**
 * 收藏商品列表接口
 * @returns
 */
export async function qryOfferCollectList() {
  return request('portal/OfferController/qryOfferCollectList.do', {
    method: 'post'
  })
}

/**
 * 批量取消收藏商品接口
 * @param {[]} 商品ID数组
 * @returns
 */
export async function cancelCollectOfferByIds(params) {
  return request('portal/OfferController/cancelCollectOfferByIds.do', {
    method: 'post',
    data: params,
  })
}

/**
 * 批量收藏商品（全量）
 * @param {[]} 商品ID数组
 * @returns
 */
export async function collectOfferBatch(params) {
  return request('portal/OfferController/collectOfferBatch.do', {
    method: 'post',
    data: params,
  })
}
/**
 * 批量收藏商品接口（增量）
 * @param {[]} 商品ID数组
 * @returns
 */
export async function collectOfferByIds(params) {
  return request('portal/OfferController/collectOfferByIds.do', {
    method: 'post',
    data: params,
  })
}
