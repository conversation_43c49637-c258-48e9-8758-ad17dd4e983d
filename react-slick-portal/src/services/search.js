import request from "@/utils/request";

/**
 * 商品分类列表搜索接口
 * @param {*} params
 * @returns
 */
export async function queryProdListByKey(key) {
  return request('portal/OfferController/queryProdListByKey.do', {
    method: 'get',
    data: {
      key
    }
  })
};

/**
 * 搜索历史记录接口
 * @returns
 */
export async function querySearchHis() {
  return request('portal/OfferController/querySearchHis.do', {
    method: 'get',
  })
}
