import request from '@/utils/request';

/**
 * 提供场景和原子能力查询
 * @param {object} params  {"sceneId":3,"sceneName":"12","sceneCode":"13"}
 */
export async function getSceneMenuInfo(params) {
  return request('portal/SceneInfoController/getSceneMenuInfo.do', {
    method: 'post',
    data: params,
  });
}

/**
 * 提供原子数量查询
 */
export async function getSceneMenuQuantity(params) {
  return request('portal/SceneInfoController/getSceneMenuQuantity.do', {
    method: 'post',
    data: params,
  });
}

/**
 * 根据原子能力获取二级环节
 */
export async function selectList(params) {
  return request('portal/SceneSubnodeRelController/selectList.do', {
    method: 'post',
    data: params,
  });
}

/**
 * 智能表格-获取智能表格配置表明细
 */
export async function getSceneNodeTableInfo(params) {
  return request('portal/SceneNodeTableController/getSceneNodeTableInfo', {
    method: 'get',
    data: params,
  });
}


/**
 * 智能表格-通过模版转换调用业务系统接口
 */
export async function doBusiSystemTempTransform(params) {
  return request('portal/BusiSystemInvokeController/doBusiSystemTempTransform', {
    method: 'post',
    data: params,
  });
}
