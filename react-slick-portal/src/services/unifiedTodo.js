import request from '@/utils/request';

// 获取待办任务一级菜单
export async function getTaskInfo() {
  return request('orgauth/UnifiedTodoController/getMenuInfo.do', {
    method: 'get',
  });
}

// 获取待阅消息
export async function getMessageInfo() {
  return request('portal/RemindMessageController/qryRemindMessageCount.do', {
    method: 'get',
  });
}

// 获取待读公告
export async function getBulletinInfo() {
  return request('orgauth/BulletinRcvObjRelController/qryBulletinRcvObjRelCount.do', {
    method: 'get',
  });
}

// 获取待办任务子类型
export async function getTaskSubTypeInfo(menuCode) {
  return request('orgauth/UnifiedTodoController/getTaskTypeInfo.do', {
    method: 'get',
    data: {
      menuCode,
    },
  });
}

// 查询待办任务列表数据
export async function queryTaskList(params) {
  return request('orgauth/UnifiedTodoController/qryTaskInfo.do', {
    data: params,
  });
}

// 获取枚举值
export async function getValuesList(params) {
  request('portal/DomainDataController/getValuesList.do', {
    data: params,
  });
}

// 获取待阅消息列表
export async function queryMessageList(params) {
  return request('portal/RemindMessageController/selectMessageGridData.do', {
    method: 'post',
    data: params,
  });
}

// 获取待读公告列表
export async function queryBulletinList(params) {
  return request('orgauth/BulletinRcvObjRelController/selectBulletinGridData.do', {
    method: 'post',
    data: params,
  });
}
