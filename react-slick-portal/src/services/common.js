import request from '@/utils/request';
import { isEmptyArray, transformToArray } from '@/utils/utils';

function getValuesList(params) {
  return request('portal/DomainDataController/getValuesList.do', {
    data: params,
  });
}

/**
 * 获取终端类型
 * 预期回参：[{"value":"1000","name":"PC端","desc":"PC端","nodeList":[]},{"value":"1100","name":"PAD端","desc":"PAD端","nodeList":[]},{"value":"1200","name":"手机端","desc":"手机端","nodeList":[]}]
 */
export async function getTerminalType() {
  return getValuesList({ busiNbr: 'UserPostTempletRel', propertyName: 'terminalType' });
}

// 缓存刷新权限判断
export async function checkFreshCachePriv() {
  return request('portal/LoginController/checkFreshCachePriv.do');
}

// 获取地市列表
export async function qryRegionList() {
  return request('orgauth/CommonRegionController/qryRegionByProvince.do')
    .then(res => res);
}

/**
 * 获取角色列表
 */
export async function qryRoleList() {
  return request('orgauth/SystemRolesController/selectSystemRolesGridData.do', {
    data: {
      isTYMHsys: 'y',
      page: 1,
      rowNum: 200,
      sortName: 'sysRoleId',
      sortOrder: 'asc',
      pageNum: 1,
      pageSize: 200,
      filterCol: 'sysRoleName,sysRoleCode',
    },
  }).then(res => transformToArray(res?.list));
}

export const getDictData = ({ groupCode, paramCode, ...rest }) => {
  if (paramCode) {
    return request('portal/DataDictController/getValueByCode.do', {
      method: 'get',
      data: {
        groupCode,
        paramCode,
        ...rest,
      },
    }).then(res => Array.isArray(res) ? res : []);
  }

  return request('portal/DataDictController/getDataDictByGroup.do', {
    method: 'get',
    data: {
      groupCode,
      ...rest,
    },
  }).then(res => Array.isArray(res) ? res : []);
};

export const newGetDictData = ({ groupCode, paramCode, ...rest }) => {
  if (paramCode) {
    return request('portal/DataDictController/getValueByCode.do', {
      method: 'get',
      data: {
        groupCode,
        paramCode,
        ...rest,
      },
    });
  }

  return request('portal/DataDictController/getDataDictByGroup.do', {
    method: 'get',
    data: {
      groupCode,
      ...rest,
    },
  });
};

export const getCallService = ({ url }) => request(url, {
  method: 'get',
});

export const loginoutResService = ({ loginoutResSerList }) => {
  if (!isEmptyArray(loginoutResSerList)) {
    loginoutResSerList.forEach(ser => {
      getCallService({ url: ser.paramValue });
    });
  }
};

export async function getDataDictByCode(params) {
  return request('portal/DataDictController/getDataDictByCode.do', {
    method: 'get',
    data: params,
  });
}
