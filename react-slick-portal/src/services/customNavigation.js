import request from '@/utils/request';

export async function getCustomConfigData(params) {
  return request('portal/NavigationConfigController/selectNavigation.do', {
    method: 'GET',
    data: params,
  });
}

export async function saveCustomNavigation(params) {
  return request('portal/NavigationConfigController/addNavigationConfig.do', {
    method: 'POST',
    data: params,
  });
}

export async function getSceneSelectData() {
  return request('portal/NavigationConfigController/selectMenuInfos.do', {
    method: 'GET',
  });
}
