import request from '@/utils/request';

/**
 * dashboard页面 获取模板数据
 * TODO:
 * 1、每个模块funcMenu字段中需要添加x,y,w,h,minW,minH,maxW,maxH字段（后四个是用来约束resize的 非必填）
 * 2、urlAddr字段值必须以[portlet]开头，如：[portlet]Bulletin，这里的Bulletin对应dashboard/components/下的模块（注意驼峰和大小写）
 * @params {object} params  例如：{confType: "1",terminalType: "1000"}
 * 如果没有配置模板则返回空对象，如果配置过了，预期回参：
 * {
      id: 68045,
      templetName: '管思坤-一线营销-PC卡片',
      layoutType: 2, // 1：两列，五五开；2：一列；3：两列,左占2/3，右1/3；4：两列，左占1/3 右占2/3
      sysPostId: 92550,
      sysUserId: 1,
      moduleRels: [
        {
          id: 714709,
          moduleId: 11721174,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1', // 在两列模式下 1：表示左边那列；2表示右边
          moduleIndex: 0,
          module: {
            id: 11721174,
            moduleName: '系统公告-68045',
            moduleType: '1', // '1': 普通的模块； '2': 组合模块
            moduleLevel: 1, // 排序
            details: [
              {
                id: 11750994,
                moduleId: 11721174,
                moduleIndex: 0,
                funcMenu: {
                  id: 276109,
                  menuId: 276109,
                  menuName: '系统公告',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 7,
                  parMenuId: 276103,
                  menuDesc: '公告',
                  urlAddr: '[portlet]Bulletin',
                  sysCode: '727001',
                  firstLetter: 'XTGG',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_001',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750994,
              },
            ],
            moduleId: 11721174,
          },
          templetModuleRelId: 714709,
        },
        {
          id: 714710,
          moduleId: 11721175,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 1,
          module: {
            id: 11721175,
            moduleName: '待办-68045',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750995,
                moduleId: 11721175,
                moduleIndex: 0,
                funcMenu: {
                  id: 276108,
                  menuId: 276108,
                  menuName: '待办',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 9,
                  parMenuId: 276103,
                  menuDesc: '待办',
                  urlAddr: '[portlet]undo',
                  sysCode: '727001',
                  firstLetter: 'DB',
                  iconUrl: 'icon-gene-redpacket',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_005',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750995,
              },
            ],
            moduleId: 11721175,
          },
          templetModuleRelId: 714710,
        },
        {
          id: 714712,
          moduleId: 11721449,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 3,
          module: {
            id: 11721449,
            moduleName: '任务概述',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11751269,
                moduleId: 11721449,
                moduleIndex: 0,
                funcMenu: {
                  id: 2770201,
                  menuId: 2770201,
                  menuName: '任务概述',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 137,
                  parMenuId: 276103,
                  menuDesc: '任务概述',
                  urlAddr: '[portlet]CustomerDistribution',
                  sysCode: '727001',
                  firstLetter: 'RWGS',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_0109',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11751269,
              },
            ],
            moduleId: 11721449,
          },
          templetModuleRelId: 714712,
        },
      ],
      terminalType: '1000',
      confType: '1',
      templetId: 68045,
    });
  }
 */
// eslint-disable-next-line import/prefer-default-export
export async function getTempletModule(params) {
  return request('portal/TempletController/getTempletModuleReact.do', { data: params });
}

// 获取系统链接
export async function qrySystemInfoLinks() {
  return request('orgauth/SystemInfoController/qrySystemInfoLinks.do', {
    method: 'get',
  });
}
