import request from '@/utils/request';

// 获取枚举值
export async function getValuesList(params) {
  return request('portal/DomainDataController/getValuesList.do', {
    data: params,
  })
}

// 详情
export async function detailById(id) {
  return request(`portal/OfferController/detailById/${id}`, {
    method: 'get',
  })
}

// 上架
export async function effById(id) {
  return request(`portal/OfferController/effById/${id}`, {
    method: 'get',
  })
}

// 下架
export async function expById(id) {
  return request(`portal/OfferController/expById/${id}`, {
    method: 'get',
  })
}

/**
 * 商品目录树接口
 * @returns
 */
export async function getOfferInfoFromCache() {
  return request('portal/OfferController/getOfferInfoFromCache.do', {
    method: 'get'
  })
}

/**
 * 商品列表分页接口
 * @returns
 */
export async function pageCondByHome(params) {
  return request('portal/OfferHomePageController/pageCondByHome.do', {
    method: 'post',
    data: params,
  })
}

/**
 * 商品订购生成订购跳转地址接口
 * @param {*} offerId 商品ID
 * @returns
 */
export async function getOfferOrderUrlById(offerId) {
  return request('portal/OfferExtInfoController/getOfferOrderUrlById.do', {
    method: 'get',
    data: {
      offerId,
    },
  }).then(res => {
    if(res.resultCode === "true"){
      window.open(res.resultObject);
    }
  })
}

// 编辑
export async function steEdit(params) {
  return request('portal/OfferController/saveOrUpdate.do', {
    method: 'post',
    data: params,
  })
}
