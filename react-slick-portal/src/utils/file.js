import { Modal } from 'antd';
import { getAttachmentList, addKnowledgeBaseDownloadCount } from '@/services/detail';

const clickDownload = (param) => {
  const { download, href } = param;
  if (download && href) {
    const a = document.createElement('a');
    a.href = href;
    a.download = download;
    // a.target = target || '_blank';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  } else {
    console.error("缺少参数");
  }
};
const handlerDownload = async (data) => {
  const propsData = {
    objInst: data.id,
    objType: "6000",
    attachment: { fileSubType: "6000" }
  }
  const res = await Promise.all([getAttachmentList(propsData), addKnowledgeBaseDownloadCount(data)]);
  if (res[0]) {
    if (res[0][0]) {
      const { fileName } = res[0][0].attachment;
      const attachIds = res[0][0].attachId;
      clickDownload({
        download: fileName,
        href: `portal/KnowledgeDownloadController/knowledgeDownload.do?attachIds=${attachIds}&name=${fileName}`
      });
    } else {
      Modal.info({
        title: '提示信息',
        content: '该方案暂无附件',
      });
    }
  }
}
export default handlerDownload
