# PubSubHelper

轻量级发布订阅工具库，提供优雅的事件管理方案，支持防抖批量更新和命名空间隔离（仅订阅端）

## 安装
```bash
npm install pubsub-helper
# 或
yarn add pubsub-helper
```

## 核心类说明

### Publisher（发布者）
**构造函数参数**
```typescript
new Publisher(defaultEvent?: string, options?: { debounce?: number })
```
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| defaultEvent | string | 'GLOBAL_FORM_UPDATE' | 默认事件名称 |
| options.debounce | number | 0 | 防抖时间（毫秒） |

**方法说明**
- `publish(data: Object, eventName?: string)`: 立即发布事件
- `batchUpdate(updates: Array<Object>, eventName?: string)`: 批量发布（自动防抖）
- `destroy()`: 清除待处理更新

### Subscriber（订阅者）
**构造函数参数**
```typescript
new Subscriber({
  defaultEvent?: string,
  handlers?: Record<string, Function>,
  namespace?: string,
  defaultHandler?: Function
})
```
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| defaultEvent | string | - | 默认订阅事件 |
| handlers | Object | {} | 事件处理映射表（actionType => handler） |
| namespace | string | '' | 命名空间前缀 |
| defaultHandler | Function | - | 未匹配事件处理函数 |

---

## 使用示例

### 基础使用
```javascript
// 初始化发布者（无命名空间）
const publisher = new Publisher('FORM_EVENT', { debounce: 150 });

// 初始化订阅者（带命名空间）
const subscriber = new Subscriber({
  defaultEvent: 'FORM_EVENT',
  handlers: {
    INPUT_CHANGE: (data) => console.log('字段变更:', data),
    FORM_SUBMIT: (data) => console.log('表单提交:', data)
  },
  namespace: 'userModule',
  defaultHandler: (data) => console.warn('未知事件:', data)
});

// 发布事件（实际触发事件名为 "userModule.FORM_EVENT"）
publisher.publish({ 
  actionType: 'INPUT_CHANGE',
  field: 'email',
  value: '<EMAIL>' 
});
```

### 防抖批量处理
```javascript
// 高频触发场景
const logger = new Publisher('ANALYTICS', { debounce: 300 });

// 快速连续调用
[1,2,3,4,5].forEach(i => {
  logger.publish({ event: `click_${i}` }, 'CLICK_EVENT');
});

// 实际合并为一次处理（事件名为 CLICK_EVENT）
```

### 多模块隔离
```javascript
// 模块A订阅者
const moduleA = new Subscriber({
  handlers: {
    UPDATE: (data) => console.log('模块A更新:', data)
  },
  namespace: 'moduleA'
});

// 模块B订阅者
const moduleB = new Subscriber({
  handlers: {
    UPDATE: (data) => console.log('模块B更新:', data)
  },
  namespace: 'moduleB'
});

// 通用发布者
const pub = new Publisher();

// 发布到不同命名空间
pub.publish({ actionType: 'UPDATE' }, 'moduleA.UPDATE_EVENT');
pub.publish({ actionType: 'UPDATE' }, 'moduleB.UPDATE_EVENT');
```

## 最佳实践

### 命名空间规范
```javascript
// 正确用法（订阅者端控制命名空间）
const paymentSubscriber = new Subscriber({
  namespace: 'payment',
  handlers: {
    SUCCESS: (data) => processPayment(data)
  }
});

// 发布时需显式包含命名空间
paymentPublisher.publish({ status: 'paid' }, 'payment.TRANSACTION');
```

### 错误处理
```javascript
// 统一错误监控
const safeSubscriber = new Subscriber({
  handlers: {
    FETCH_DATA: (data) => {
      try {
        fetchData(data);
      } catch (error) {
        Sentry.captureException(error);
      }
    }
  },
  defaultHandler: (data) => {
    console.error('未处理的事件类型:', data?.actionType);
  }
});
```

### 内存管理
```javascript
// React 示例
useEffect(() => {
  const subscriber = new Subscriber({
    handlers: { /* ... */ }
  });

  return () => {
    subscriber.unsubscribeAll();
    publisher.destroy();
  };
}, []);
```

## 调试技巧

### 全局事件监听
```javascript
// 监听所有事件（开发环境）
if (process.env.NODE_ENV === 'development') {
  PubSub.subscribe('*', (msg, data) => {
    console.debug('[PubSub]', msg, data);
  });
}
```

### 性能监控
```javascript
// 记录事件处理耗时
const monitoredSubscriber = new Subscriber({
  handlers: {
    PROCESS_DATA: (data) => {
      const start = performance.now();
      processData(data);
      console.log(`处理时间: ${performance.now() - start}ms`);
    }
  }
});
```

## 版本说明
v1.0.0 核心功能：
- 发布者支持防抖批量处理
- 订阅者支持命名空间隔离
- 提供完整生命周期管理
- 支持TypeScript类型推断
