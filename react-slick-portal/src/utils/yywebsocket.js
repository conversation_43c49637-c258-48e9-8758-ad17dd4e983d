/* eslint-disable no-console */
/* eslint-disable no-use-before-define */
const heartbeatInterval = 180000; // 3分钟
let pingTimeout;
let reconnectTimeout;
let websocket = null;

// WebSocket心跳检测
function startHeartbeat(ws) {
  pingTimeout = setTimeout(() => {
    console.log('发送ping消息');
    ws.send('ping');

    // 如果在指定时间内未收到pong响应，则关闭连接
    pingTimeout = setTimeout(() => {
      console.warn('未收到pong响应，关闭连接');
      ws.close();
    }, heartbeatInterval);
  }, heartbeatInterval);
}

// 重新连接websocker(WebSocket连接地址)
export function wsRecontent() {
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
  }
  console.log('尝试重新连接...');
  reconnectTimeout = setTimeout(() => {
    wsCreate();
  }, 5000); // 5秒后重连
}

// WebSocket 事件创建
function wsEvent(ws, url) {
  ws.onopen = () => {
    console.log('WebSocket已连接');
    startHeartbeat(ws);
  };

  ws.onclose = event => {
    console.log('WebSocket连接已关闭', event);

    if (websocket) {
      clearTimeout(pingTimeout);
      // 重新连接WebSocket
      wsRecontent();
    }
  };

  ws.onerror = event => {
    console.log('WebSocket错误：', event);

    clearTimeout(pingTimeout);
    // 重新连接WebSocket
    wsRecontent(url);
  };

  ws.onmessage = event => {
    console.log('接收消息event：', event);

    if (event.data.includes('pong')) {
      console.log('收到pong响应');
      clearTimeout(pingTimeout);
      startHeartbeat(ws);
    } else if (event.data) {
      try {
        const result = JSON.parse(event.data);
        if (result.success && result.resultObject) {
          const { callBackType, notifications } = result.resultObject;
          // 消息处理，打开副驾变更页面
          window.YXPlugin?.trigger({
            pointCode: 'zhengqi#WorkstationAssistant#AIHelpCheck',
            params: {
              watchType: 'notice',
              notification: {
                callBackType,
                notifications: [notifications],
              },
            },
          });
          console.log('收到合同状态变更消息，发送AIHelpCheck指令');
        } else {
          console.log('websocket接收失败结果：', event);
        }
      } catch (error) {
        console.log('websocket接收出错：', error);
      }
    }
  };
}
// 创建WebSocket
export async function wsCreate() {
  // WebSocket连接地址
  const serverIP = window.location.hostname;
  const serverPort = window.location.port;
  const wsUrl = `ws:${serverIP}:${serverPort}/portal-react/websocket/smartBot`;
  // const wsUrl = 'ws:*************:8888/portal-react/websocket/smartBot';

  try {
    // 判断是否支持 WebSocket
    if (typeof (WebSocket) === 'undefined') {
      console.log('您的浏览器不支持WebSocket');
    } else {
      console.log('您的浏览器支持WebSocket');
      // 连接WebSocket
      websocket = new WebSocket(wsUrl);
    }
    wsEvent(websocket, wsUrl);
  } catch (e) {
    // 重新连接WebSocket
    wsRecontent();
    console.log(e);
  }
}

// 关闭连接
export function wsClose() {
  if (websocket) {
    websocket.close();
    websocket = null;
  }
}
