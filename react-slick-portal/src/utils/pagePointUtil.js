/* eslint-disable no-console */
/* eslint-disable class-methods-use-this */
import moment from 'moment';

class PagePointUtil {
  constructor(paramsObj) {
    const {
      pointKeyArr, pointKeyValFunc, requestUrl, isConsole, isDefaultRegister = true,
    } = paramsObj;
    if (!pointKeyArr || !(Array.isArray(pointKeyArr) && pointKeyArr.length) || !requestUrl || ![true, false].includes(isConsole)) {
      console.log('埋点工具初始化失败！原因：缺少参数。');
    } else {
      this.pointKeyArr = Array.isArray(pointKeyArr) ? pointKeyArr : [pointKeyArr];
      this.pointKeyValFunc = pointKeyValFunc;
      this.requestUrl = requestUrl;
      this.isConsole = isConsole;
      this.pointKey = '';
      this.pointKeyVal = '';
      if (isDefaultRegister) {
        window.onload = () => {
          this.init();
        };
      } else {
        this.init();
      }
    }
  }

  // 封装click监听事件
  init() {
    window.document.body.addEventListener('click', e => {
      this.findTargetNode(e, e.target);
    });
  }

  // 寻找命中的埋点元素的节点
  findTargetNode(e, target) {
    if (!target) return;
    if (target?.nodeName === 'HTML') return;
    const domKeys = target?.attributes ? Object.values(target.attributes).map(c => c.name) : [];
    const existKeys = this.pointKeyArr.filter(c => domKeys.includes(c) && target?.getAttribute(c));
    if (existKeys.length) {
      this.handleTargetHit(e, target, existKeys);
      return;
    }
    this.findTargetNode(e, target.parentNode);
  }

  // 处理命中
  handleTargetHit(e, target, existKeys) {
    const [pointKey] = existKeys;
    this.pointKey = pointKey;
    this.pointKeyVal = typeof this.pointKeyValFunc === 'function' ? this.pointKeyValFunc(target, target.getAttribute(this.pointKey)) : target.getAttribute(this.pointKey);
    const reportData = this.getReportData(e, target);
    if (this.isConsole) {
      console.log(target);
      console.log(reportData);
    } else {
      this.report(reportData);
    }
  }

  // 封装原生请求
  request(params) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            resolve(xhr.responseText);
          } else {
            reject(xhr.status);
          }
        }
      };
      xhr.open('post', this.requestUrl);
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.send(JSON.stringify(params));
    });
  }

  // 获取浏览器类型
  getBroswer(userAgent) {
    const isOpera = userAgent.indexOf('Opera') > -1;
    if (isOpera) {
      return 'Opera';
    }
    if (userAgent.indexOf('Firefox') > -1) {
      return 'Firefox';
    }
    if (userAgent.indexOf('Chrome') > -1) {
      return 'Chrome';
    }
    if (userAgent.indexOf('Safari') > -1) {
      return 'Safari';
    }
    if (userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1 && !isOpera) {
      return 'IE';
    }
    if (userAgent.indexOf('Trident') > -1) {
      return 'Edge';
    }
    return '';
  }

  // 收集要上报的数据
  getReportData(eventData, target) {
    const { navigator, innerHeight, innerWidth } = window;
    const {
      altKey, ctrlKey, type, metaKey, shiftKey, keyCode,
    } = eventData;
    const {
      className, innerText, id, nodeName, baseURI,
    } = target;
    const { title, referrer } = window.document;
    const extData = '扩展数据，请忽略';
    return {
      [this.pointKey]: this.pointKeyVal,
      OSType: navigator.userAgentData?.platform || '',
      browser: navigator.userAgent,
      browserType: this.getBroswer(navigator.userAgent),
      altKey,
      ctrlKey,
      className,
      eventType: type,
      // evtTime: `${new Date('yyyy-MM-dd HH:mm:ss')}`,
      evtTime: moment().format('yyyy-MM-DD HH:mm:ss'),
      id,
      innerText,
      metaKey,
      shiftKey,
      name: target.attributes?.name?.value,
      tagname: nodeName,
      type: target.type,
      url: baseURI,
      pageTitle: title,
      referer: referrer,
      screenHeight: innerHeight,
      screenWidth: innerWidth,
      value: target.attributes?.value?.value,
      keyCode,
      extData,
    };
  }

  // 上报埋点数据
  report(reportData) {
    this.request(reportData).then(() => {
    }).catch(status => {
      console.log(`请求失败，状态码：${status}`);
    });
  }
}

export default PagePointUtil;
