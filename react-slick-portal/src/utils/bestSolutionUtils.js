import React from 'react';
import { Tooltip, Input } from 'antd';
import { isEmptyStr } from '@/utils/utils';

/**
 * 设置最小宽度为200：minWidth: 200 或直接设置columns的width属性（如果直接使用width，需要搭配maxWidth，否则会出现自动换行）
 * 设置最大宽度为100，溢出省略：maxWidth: 100（当maxWidth小于列标题长度时，maxWidth无效）
 * 设置固定宽度为200: 同时设置最小宽度和最大宽度
 * 在columns中的使用方式：
 *  {
      title: '归属地市',
      dataIndex: 'regionName',
      style: {
        minWidth: 200,
      }
    }
 */
export const buildCellStyle = params => () => ({
  style: {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    ...params,
  },
});

/**
 * textFormat需要返回一个数组[val, valStr] // TODO 待优化，只返回一个值时默认扩展为数组
 * val支持JSX元素或普通文本
 * valStr用于ToolTip的展示，最好是字符串
 * placement表示Tooltip位置，可省略，默认topLeft。可选返回值：top
 */
export const TooltipWrap = textFormat => (text, record, index) => {
  const [val, valStr, placement] = typeof textFormat === 'function'
    ? textFormat(text, record, index)
    : [text, text];

  return (
    <Tooltip placement={placement || 'topLeft'} title={valStr} mouseEnterDelay={0.3}>
      {isEmptyStr(val) ? '-' : val}
    </Tooltip>
  );
};

const renderInput = params => {
  const config = params?.config ?? {};

  const {
    allowClear,
    placeholder,
    disabled,
    onChange,
  } = config;

  return (value, record, index) => (
    <Input
      allowClear={allowClear ?? false}
      placeholder={placeholder ?? '请输入'}
      value={value}
      disabled={disabled ?? false}
      onChange={e => {
        if (typeof onChange === 'function') {
          onChange(params.dataIndex, e?.target?.value, index);
        }
      }}
    />
  );
};

const renderFormItem = params => {
  // input\select\inputNumber
  if (params.type === 'input') {
    return renderInput(params);
  }
  return undefined;
};

export const buildColumns = _columns => _columns.map(item => {
  const newItem = { ...item };

  if (!newItem.align) {
    newItem.align = 'center';
  }

  if (!newItem.render && newItem.title !== '操作') {
    if (newItem.type) {
      newItem.render = renderFormItem(item);
    } else {
      newItem.render = typeof newItem.textFormat === 'function'
        ? TooltipWrap(newItem.textFormat)
        : TooltipWrap();
    }
  }

  if (!newItem.onCell && newItem.style) {
    newItem.onCell = buildCellStyle(newItem.style);
  }
  return newItem;
});
