import React, { useState } from 'react';
import { Popover } from 'antd';

/**
 * 高阶组件：为组件添加Popover功能
 * @param {React.Component} WrappedComponent - 需要被包装的组件
 * @param {Object} options - 配置选项
 * @param {Function} options.renderContent - 自定义渲染Popover内容的函数
 * @param {Object} options.popoverProps - 传递给Popover组件的props
 * @returns {React.Component} - 包装后的组件
 */
const withPopover = (WrappedComponent, options = {}) => {
  const {
    renderContent,
    popoverProps = {},
  } = options;

  // 返回包装后的组件
  return function WithPopoverComponent(props) {
    const [visible, setVisible] = useState(false);

    // 处理鼠标移入事件
    const handleMouseEnter = () => {
      setVisible(true);
    };

    // 处理鼠标移出事件
    const handleMouseLeave = () => {
      setVisible(false);
    };

    // 默认的Popover内容渲染函数
    const defaultRenderContent = () => {
      return (
        <div>
          {props.popoverContent || '默认提示内容'}
        </div>
      );
    };

    // 使用自定义渲染函数或默认渲染函数
    const content = renderContent ? renderContent(props) : defaultRenderContent();

    return (
      <Popover
        content={content}
        visible={visible}
        trigger="hover"
        {...popoverProps}
      >
        <div
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <WrappedComponent {...props} />
        </div>
      </Popover>
    );
  };
};

export default withPopover;
