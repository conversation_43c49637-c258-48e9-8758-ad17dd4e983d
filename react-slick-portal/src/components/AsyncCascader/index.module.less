.AsyncCascader {
  position: relative;
  display: inline-block;
  width: 100%;
  &:before {
    content: '';
    display: block;
    height: 0;
  }
}

.cascaderWrapper {
  width: 100%;
  position: relative;

  :global {
    .ant-cascader-picker {
      width: 100%;
      transition: none !important;
      position: relative !important;
      display: inline-block !important;
    }

    .ant-cascader-picker-arrow,
    .ant-cascader-picker-clear {
      transition: transform 0.2s !important;
      position: absolute !important;
      right: 8px !important;
      top: 50% !important;
      margin-top: -6px !important;
    }

    .ant-cascader-input.ant-input {
      height: 32px !important;
      width: 100% !important;
      box-sizing: border-box !important;
      padding-right: 24px !important;
    }

    .ant-cascader-menus {
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      max-height: 80vh !important;
      overflow-y: auto !important;
      display: flex !important;
      flex-wrap: nowrap !important;

      @media (max-width: 768px) {
        max-width: calc(100vw - 32px) !important;
        .ant-cascader-menu {
          min-width: 110px !important;
          font-size: 13px !important;

          &-item {
            padding: 8px 8px 8px 12px !important;
          }
        }
      }

      @media (max-width: 480px) {
        max-width: calc(100vw - 16px) !important;
        .ant-cascader-menu {
          min-width: 100px !important;
          font-size: 12px !important;

          &-item {
            padding: 6px 6px 6px 8px !important;
          }
        }
      }
    }

    .ant-cascader-menu {
      opacity: 1;
      transition: opacity 0.3s ease, transform 0.3s ease;
      transform: translateX(0);
      height: auto;
      max-height: 80vh !important;
      overflow-y: auto !important;
      min-width: 120px !important;
      flex: 0 0 auto !important;
      min-height: auto !important;

      &-item {
        transition: all 0.2s ease;
        position: relative;
        padding: 8px 12px 8px 16px;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;

        &:hover {
          background-color: #f5f5f5;
        }

        &-active {
          background-color: #e6f7ff;
          font-weight: 500;
        }

        &-loading {
          position: relative;
          color: #999 !important;
          background-color: #f0f2f5 !important;

          &:after {
            content: '';
            position: absolute;
            right: 12px;
            top: 50%;
            margin-top: -7px;
            width: 14px;
            height: 14px;
            border: 2px solid #1890ff;
            border-radius: 50%;
            border-color: #1890ff transparent #1890ff transparent;
            animation: cascaderLoading 0.8s linear infinite;
            box-shadow: 0 0 5px rgba(24, 144, 255, 0.3);
          }

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 3px;
            height: 100%;
            background-color: #1890ff;
            z-index: 1;
          }

          .ant-cascader-menu-item-expand-icon {
            display: none !important;
          }

          &.ant-cascader-menu-item-expand:after {
            content: '加载中...';
            position: absolute;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #1890ff;
            animation: flashText 1.2s infinite;
          }

          &:hover {
            background-color: #f0f2f5 !important;
            cursor: progress;
          }
        }

        &-expand {
          &:after {
            transition: transform 0.2s ease;
          }
        }
      }

      &:not(:first-child) {
        animation: slideIn 0.25s ease-out;
      }
    }

    .ant-cascader-dropdown {
      z-index: 1100 !important;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateX(10px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes cascaderLoading {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes flashText {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }

    .ant-cascader-menu-item-keyword {
      color: #1890ff;
      font-weight: bold;
    }
  }
}

.selector {
  width: 100%;
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.tabsContainer {
  padding: 8px;
}

.tabs {
  margin-bottom: 0;
}

.tabContent {
  max-height: 300px;
  overflow-y: auto;
}

.option {
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.3s;
  &:hover {
    background: #f5f5f5;
  }
  &.selected {
    background: #e6f7ff;
  }
}

.checkIcon {
  margin-left: 8px;
  color: #1890ff;
}

.emptyOptions {
  padding: 8px 12px;
  color: #999;
  text-align: center;
}

.customContent {
  padding: 8px 12px;
}

.disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;

  &:hover {
    background-color: #f5f5f5;
  }
}

.disabledAll {
  opacity: 0.65;
  cursor: not-allowed;

  .selector {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.searchContainer {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.searchInput {
  width: 100%;
}

.searchResults {
  max-height: 300px;
  overflow-y: auto;
}

.menu-item-keyword {
  color: #f50;
}

.emptyContent {
  padding: 8px 12px;
  color: #999;
  text-align: center;
}
.popoverContent {
  max-width: 300px;
  word-break: break-all;
  white-space: normal;
}

.cascaderPopover {
  :global(.ant-popover-inner-content) {
    padding: 8px 12px;
  }
  :global(.ant-spin-nested-loading) {
    width: 100%;
  }
}

.cascaderPopup {
  animation: none !important;

  :global {
    .ant-cascader-menus-empty {
      pointer-events: none !important;
    }

    .ant-cascader-menus {
      position: absolute !important;
      margin-top: 4px !important;
      min-width: 100% !important;
      transition: none !important;
      animation: fadeInStable 0.15s ease-out !important;
      transform-origin: top !important;
      will-change: opacity, transform !important;

      display: flex;
      flex-wrap: nowrap;
      overflow: auto;
      max-width: calc(100vw - 24px) !important;
    }

    .ant-cascader-dropdown {
      position: absolute;
      max-width: calc(100vw - 24px) !important;

      &.ant-cascader-dropdown-rightmost {
        &:after {
          content: '>>';
          position: absolute;
          right: 8px;
          top: 50%;
          color: #999;
          font-size: 12px;
          margin-top: -6px;
          animation: blinkArrow 1s infinite;
        }
      }
    }

    @keyframes blinkArrow {
      0%, 100% { opacity: 0.5; }
      50% { opacity: 1; }
    }
  }
}

.stableCascaderContainer {
  position: relative;
  transform: translateZ(0);
  will-change: opacity;
  display: flex;
  max-width: 100%;
}

@keyframes fadeInStable {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.popupContainer {
  position: absolute;
  left: 0;
  top: 100%;
  width: 100%;
  z-index: 1050;
  margin-top: 4px;
}

.cascaderLoadingTip {
  display: none;
}
