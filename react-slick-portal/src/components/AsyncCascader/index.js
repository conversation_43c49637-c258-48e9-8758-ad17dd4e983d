import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { Popover, Cascader, Spin } from 'antd';
import styles from './index.module.less';

/**
 * AsyncCascader 地址级联选择器组件
 */
const AsyncCascader = ({
  title,
  onChange,
  className,
  defaultValue = [],
  value,
  disabledAll = false,
  loadData,
  disableOption = [], // 根据层级设置disable函数 [()=> value!==1]
  displayRender,
  placeholder,
  fieldNames = { label: 'label', value: 'value', children: 'children' },
  ...restProps
}) => {
  const containerRef = useRef();
  const cascaderRef = useRef();

  const OptionsRef = useRef([]);
  const [internalValue, setInternalValue] = useState(value || defaultValue || []);
  const [loading, setLoading] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0); // 添加状态变量以强制重渲染
  const prevValueRef = useRef();
  const optionsLoadedRef = useRef(false);

  // 同步外部 value
  useEffect(() => {
    if (value) {
      setInternalValue(value);

      // 当 value 变化且选项已加载时，确保相应的选项已加载
      if (optionsLoadedRef.current && JSON.stringify(value) !== JSON.stringify(prevValueRef.current)) {
        prevValueRef.current = value;
        // eslint-disable-next-line no-use-before-define
        ensureOptionsLoaded(value);
      }
    }
  }, [value]);

  // 确保选项已加载
  const ensureOptionsLoaded = async valueArray => {
    if (!valueArray || valueArray.length === 0 || !loadData) return;

    setLoading(true);

    try {
      // 如果 options 为空，先加载根选项
      let currentOptions = [...OptionsRef.current];
      if (currentOptions.length === 0) {
        const rootOptions = await loadData([]);
        if (Array.isArray(rootOptions)) {
          OptionsRef.current = rootOptions;
          currentOptions = rootOptions;
        } else {
          return;
        }
      }

      // 构建已选择的选项路径
      const selectedOptionPath = [];

      // 逐级加载选项
      for (let i = 0; i < valueArray.length; i += 1) {
        const currentValue = valueArray[i];
        const targetOption = currentOptions.find(option => option[fieldNames.value] === currentValue);

        if (!targetOption) break;

        selectedOptionPath.push(targetOption);

        // 如果不是最后一级且没有子选项，则加载子选项
        if (i < valueArray.length - 1 && (!targetOption[fieldNames.children] || targetOption[fieldNames.children].length === 0)) {
          // eslint-disable-next-line no-await-in-loop
          const children = await loadData(selectedOptionPath);

          if (Array.isArray(children) && children.length > 0) {
            targetOption[fieldNames.children] = children;
          } else {
            targetOption.isLeaf = true;
            break;
          }
        }

        // 更新当前选项列表为下一级
        currentOptions = targetOption[fieldNames.children] || [];
      }

      // 更新选项状态 - 强制组件重新渲染
      OptionsRef.current = [...OptionsRef.current];
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('加载选项数据失败', err);
    } finally {
      setLoading(false);
    }
  };

  // 处理值变化
  const handleChange = (val, selectedOptions) => {
    setInternalValue(val);
    prevValueRef.current = val;
    onChange && onChange(val, selectedOptions);
  };

  // 异步加载子级数据
  const handleLoadData = async selectedOptions => {
    if (!selectedOptions || selectedOptions.length === 0) return;

    const targetOption = selectedOptions[selectedOptions.length - 1];

    // 设置加载状态，增加loading标记
    targetOption.loading = true;

    // 立即更新视图，显示loading状态
    setForceUpdate(prev => prev + 1);

    try {
      // 添加最小加载时间，避免闪烁
      const [children] = await Promise.all([
        loadData(selectedOptions),
        new Promise(resolve => setTimeout(resolve, 150)), // 最小150ms的加载时间
      ]);

      targetOption.loading = false;

      if (Array.isArray(children) && children.length > 0) {
        targetOption[fieldNames.children] = children;
      } else {
        targetOption.isLeaf = true;
      }

      // 找到 OptionsRef 中对应的选项并更新
      const updateOptionsTree = (options, path, index = 0) => {
        if (!options || !Array.isArray(options) || index >= path.length) return;

        const currentPathItem = path[index];
        const targetItem = options.find(item => item[fieldNames.value] === currentPathItem[fieldNames.value]);

        if (targetItem) {
          if (index === path.length - 1) {
            // 找到目标选项，更新其属性
            targetItem.loading = false;
            if (Array.isArray(children) && children.length > 0) {
              targetItem[fieldNames.children] = children;
            } else {
              targetItem.isLeaf = true;
            }
          } else {
            // 继续向下查找
            updateOptionsTree(targetItem[fieldNames.children], path, index + 1);
          }
        }
      };

      // 构建选项路径
      const optionPath = selectedOptions.map(option => ({
        [fieldNames.value]: option[fieldNames.value],
        [fieldNames.label]: option[fieldNames.label],
      }));

      // 更新选项树
      updateOptionsTree(OptionsRef.current, optionPath);

      // 更新引用并触发重新渲染
      OptionsRef.current = [...OptionsRef.current];

      // 强制组件重新渲染以确保下一级展开
      setForceUpdate(prev => prev + 1);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('加载子级数据失败', err);
      targetOption.loading = false;
      targetOption.isLeaf = true;

      // 找到 OptionsRef 中对应的选项并更新错误状态
      const updateOptionsErrorState = (options, path, index = 0) => {
        if (!options || !Array.isArray(options) || index >= path.length) return;

        const currentPathItem = path[index];
        const targetItem = options.find(item => item[fieldNames.value] === currentPathItem[fieldNames.value]);

        if (targetItem && index === path.length - 1) {
          targetItem.loading = false;
          targetItem.isLeaf = true;
        } else if (targetItem) {
          updateOptionsErrorState(targetItem[fieldNames.children], path, index + 1);
        }
      };

      // 构建选项路径
      const optionPath = selectedOptions.map(option => ({
        [fieldNames.value]: option[fieldNames.value],
        [fieldNames.label]: option[fieldNames.label],
      }));

      // 更新选项树错误状态
      updateOptionsErrorState(OptionsRef.current, optionPath);

      // 更新引用并触发重新渲染
      OptionsRef.current = [...OptionsRef.current];

      // 即使出错也强制重新渲染
      setForceUpdate(prev => prev + 1);
    }
  };

  // 初始化加载顶级 options（如果有必要）
  const fetchRootOptions = async () => {
    setLoading(true);
    try {
      const rootOptions = await loadData([]);
      if (Array.isArray(rootOptions)) {
        OptionsRef.current = rootOptions;
        optionsLoadedRef.current = true;

        // 初始化完成后，如果有初始值，确保相应选项已加载
        if (internalValue && internalValue.length > 0) {
          await ensureOptionsLoaded(internalValue);
        }
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('加载顶层数据失败', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRootOptions();
  }, []);

  // 渲染展示文本（Popover 中的内容）
  const displayText = useMemo(() => {
    if (!internalValue || internalValue.length === 0) return '';

    // 使用 Cascader 组件的默认渲染逻辑
    // 尝试从 options 中查找对应的 label
    const labels = [];
    let currentOptions = OptionsRef.current;

    for (let i = 0; i < internalValue.length; i += 1) {
      const currentValue = internalValue[i];
      const targetOption = currentOptions?.find(option => option[fieldNames.value] === currentValue);

      if (targetOption) {
        labels.push(targetOption[fieldNames.label]);
        currentOptions = targetOption[fieldNames.children];
      } else {
        break;
      }
    }
    // 如果提供了自定义渲染函数，则使用它
    if (displayRender) {
      return displayRender(labels);
    }
    return labels.join(' / ');
  }, [internalValue, displayRender, fieldNames]);

  const formatOption = useCallback(() => {
    // 如果没有选项或禁用规则，直接返回原始选项
    if (!OptionsRef.current || OptionsRef.current.length === 0) return OptionsRef.current;

    // 递归处理选项的函数
    const processOptions = (items, level = 0) => {
      if (!items || !Array.isArray(items)) return items;

      return items.map(item => {
        // 创建新对象，避免直接修改原始对象
        const newItem = { ...item };

        // 应用当前层级的禁用规则（如果存在）
        if (Array.isArray(disableOption) && disableOption[level]) {
          newItem.disabled = disableOption[level](newItem);
        }

        // 递归处理子选项
        if (newItem[fieldNames.children] && newItem[fieldNames.children].length > 0) {
          newItem[fieldNames.children] = processOptions(newItem[fieldNames.children], level + 1);
        }

        return newItem;
      });
    };

    return processOptions(OptionsRef.current);
  }, [disableOption, fieldNames, loading, forceUpdate]); // 添加forceUpdate依赖

  // 确保点击外部时可以关闭级联选择器
  useEffect(() => {
    const handleOutsideClick = e => {
      if (cascaderRef.current && cascaderRef.current instanceof HTMLElement && !cascaderRef.current.contains(e.target)) {
        // 点击事件发生在级联选择器外部，需要关闭它
        // 这里通过给document body添加点击事件来触发关闭
        document.body.click();
      }
    };

    // 添加事件监听
    document.addEventListener('mousedown', handleOutsideClick);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, []);

  return (
    <div className={`${styles.AsyncCascader} ${className || ''} ${disabledAll ? styles.disabledAll : ''}`} ref={containerRef}>
      <Popover
        title={title}
        trigger="hover"
        content={displayText || placeholder}
        getPopupContainer={triggerNode => triggerNode.parentNode}
        overlayClassName={styles.cascaderPopover}
      >
        <div className={styles.cascaderWrapper}>
          <Spin spinning={loading} size="small">
            <Cascader
              ref={cascaderRef}
              options={formatOption()}
              value={internalValue}
              onChange={handleChange}
              loadData={handleLoadData}
              changeOnSelect
              expandTrigger="click"
              disabled={disabledAll || loading}
              fieldNames={fieldNames}
              placeholder={placeholder}
              allowClear={false}
              notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
              showSearch={{
                filter: (inputValue, path) => path.some(option =>
                  option[fieldNames.label].toLowerCase().indexOf(inputValue.toLowerCase()) > -1
                ),
                render: (inputValue, path) => {
                  // 自定义搜索结果渲染，高亮匹配文本
                  const label = path[path.length - 1][fieldNames.label];
                  const index = label.toLowerCase().indexOf(inputValue.toLowerCase());
                  if (index === -1) return label;

                  const beforeStr = label.substring(0, index);
                  const matchStr = label.substring(index, index + inputValue.length);
                  const afterStr = label.substring(index + inputValue.length);

                  return (
                    <span>
                      {beforeStr}
                      <span className="ant-cascader-menu-item-keyword">{matchStr}</span>
                      {afterStr}
                    </span>
                  );
                },
              }}
              popupClassName={styles.cascaderPopup}
              dropdownRender={menus => (
                <div className={styles.stableCascaderContainer}>{menus}</div>
              )}
              placement="bottomLeft"
              builtinPlacements={{
                bottomLeft: {
                  points: ['tl', 'bl'],
                  offset: [0, 4],
                  overflow: {
                    adjustX: 1,
                    adjustY: 1,
                  },
                },
              }}
              {...restProps}
            />
          </Spin>
        </div>
      </Popover>
    </div>
  );
};

export default AsyncCascader;
