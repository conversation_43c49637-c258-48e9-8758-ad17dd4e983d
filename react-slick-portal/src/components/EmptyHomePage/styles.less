@import '../../layouts/BasicLayout/less/variables.less';

.header {
  z-index: 900;
  height: @header-height;
  color: @header-default-text;
  line-height: @header-height;
  background-color: @header-blue-bg;
  border-bottom: 1px solid @header-border-default-color;
}

.logo {
  display: inline-block;
  width: 120px;
  height: @header-height;
  padding: 0;
  vertical-align: bottom;
  background: url(../../layouts/BasicLayout/img/header-logo-default.png) no-repeat center center;
  background-image: url(../../layouts/BasicLayout/img/js-header-logo.png);
  background-size: 96px;
  cursor: default;
}

.title {
  position: relative;
  display: inline-block;
  padding: 0 8px 0 0;
  color: #fff;
  font-size: 13px;
  cursor: default;
}
