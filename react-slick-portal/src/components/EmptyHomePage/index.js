import React from 'react';
import { formatMessage } from 'umi/locale';
import styles from './styles.less';

const Index = props => {
  const { children } = props ?? {};

  return (
    <div>
      <div className={styles.header}>
        {/* logo */}
        <span className={styles.logo} />
        {/* title */}
        <span className={styles.title}>
          {formatMessage({ id: 'app.title' })}
        </span>
      </div>
      {children}
    </div>
  );
};

export default Index;
