import React, { useEffect, useState } from 'react';
import { Row, Col, Form, Input, Select } from 'antd';

const { Option } = Select;

// 定义通用查询组件
const QueryComponent = ({ queryConditions, isExpand = false, onChange, form }) => {
  const [selectedCondition, setSelectedCondition] = useState(null);

  const handleValueChange = (key, value) => {
    if (!isExpand) {
      onChange({ key, value });
    }
  };

  // 根据查询条件渲染相应的FormItem
  const renderFormItem = condition => {
    switch (condition.type) {
      case 'select':
        return (
          <Select style={{ width: '150px', textAlign: 'left' }} placeholder={`请选择${condition.label}`} onChange={value => handleValueChange(condition.key, value)}>
            {condition.options.map(opt => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        );
      default:
        return <Input style={{ width: '150px', textAlign: 'left' }} placeholder={`请输入${condition.label}`} onChange={e => handleValueChange(condition.key, e.target.value)} />;
    }
  };

  const handleConditionChange = condition => {
    setSelectedCondition(condition);
    form.setFieldsValue({ queryType: condition.key });
    if (!isExpand) {
      onChange({ key: condition.key, value: '' }); // 默认值可以根据实际情况调整
    }
  };
  useEffect(() => {
    // 初始化时选择第一个查询条件
    if (queryConditions.length > 0 && !selectedCondition) {
      handleConditionChange(queryConditions[0]);
    }
  }, [queryConditions, selectedCondition]);
  return (
    <>
      {isExpand ? (
        <Row gutter={16}>
          {queryConditions.filter(item => !item.disable).map(condition => (
            <Col span={6}>
              <Form.Item name={condition.key} label={condition.label}>
                {renderFormItem(condition)}
              </Form.Item>
            </Col>
          ))}
        </Row>
      ) : (
        <Input.Group compact>
          <Select
            style={{ minWidth: '100px' }}
            placeholder="请选择查询条件"
            onChange={value => {
                const condition = queryConditions.find(c => c.key === value);
                setSelectedCondition(condition);
              }}
          >
            {queryConditions.map(condition => (
              <Option key={condition.key} value={condition.key}>
                {condition.label}
              </Option>
              ))}
          </Select>
          {selectedCondition && renderFormItem(selectedCondition)}
        </Input.Group>
      )}
    </>
  );
};

export default Form.create()(QueryComponent);
