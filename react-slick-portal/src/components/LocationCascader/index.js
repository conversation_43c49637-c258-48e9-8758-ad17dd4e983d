import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { Popover, Spin, Icon, Tabs, Input } from 'antd';
import styles from './index.module.less';

const { TabPane } = Tabs;


/**
 * LocationCascader 地址级联选择器组件
 * @param {object} props - 组件属性
 * @param {array} props.options - 级联选择器的配置项
 * @param {string} [props.placeholder] - 占位符文本
 * @param {string} [props.title] - 鼠标移入时显示的标题
 * @param {function} [props.onChange] - 选择变化时的回调函数
 * @param {string} [props.className] - 自定义类名
 * @param {array} [props.defaultValue] - 默认选中的值
 * @param {array} [props.value] - 当前选中的值（受控模式）
 * @param {boolean} [props.disabled] - 是否禁用特定选项
 * @param {boolean} [props.disabledAll] - 是否禁用整个组件
 */
const LocationCascader = ({
  options = [],
  placeholder = '请选择',
  title,
  onChange,
  className,
  defaultValue = [],
  value,
  disabledAll = false,
  ...rest
}) => {
  // 内部状态
  const [internalValue, setInternalValue] = useState(value || defaultValue);
  const [internalLoading, setInternalLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [activeKey, setActiveKey] = useState('0');
  const [levelOptions, setLevelOptions] = useState([]);
  const [customInputValues, setCustomInputValues] = useState({});
  const [inputValue, setInputValue] = useState('');
  const containerRef = useRef();
  const initialized = useRef(false);

  // 处理受控模式下的值变化
  useEffect(() => {
    if (value !== undefined) {
      setInternalValue(value);
    }
  }, [value]);

  // 加载选项数据 - 使用 useCallback 优化性能
  const loadOptions = useCallback(
    async (level, selectedValues = []) => {
      const levelConfig = options[level];
      if (!levelConfig) return [];

      if (typeof levelConfig.options === 'function') {
        setInternalLoading(true);
        try {
          const result = await levelConfig.options(selectedValues);
          return result;
        } catch (error) {
          console.error('加载选项数据失败:', error);
          return [];
        } finally {
          setInternalLoading(false);
        }
      }
      return levelConfig.options || [];
    },
    [options]
  );

  // 初始化选项数据
  useEffect(() => {
    const initOptions = async () => {
      if (!initialized.current) {
        initialized.current = true;
        const firstLevelOptions = await loadOptions(0);
        setLevelOptions([firstLevelOptions]);

        // 如果有默认值或受控值，逐级加载对应层级的选项
        const initialValue = value || defaultValue;
        if (initialValue && initialValue.length > 0) {
          // 创建一个新的选项数组，包含第一级选项
          const currentLevelOptions = [firstLevelOptions];

          // 如果第一级有值，立即加载第二级选项（不需要等到 i > 0）
          if (initialValue[0]) {
            const secondLevelOptions = await loadOptions(1, [initialValue[0]]);
            currentLevelOptions.push(secondLevelOptions);
          }

          // 继续加载第三级及以上的选项
          for (let i = 1; i < initialValue.length - 1; i += 1) {
            const selectedValues = initialValue.slice(0, i + 1);
            // eslint-disable-next-line no-await-in-loop
            const optionsForThisLevel = await loadOptions(i + 1, selectedValues);
            currentLevelOptions.push(optionsForThisLevel);
          }

          setLevelOptions(currentLevelOptions);
          setActiveKey((initialValue.length - 1).toString());
        }
      }
    };

    initOptions();
  }, [defaultValue, value, loadOptions]);

  // 处理点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = event => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setVisible(false);
        setInputValue('');
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [visible]);


  // 处理选项点击
  const handleOptionClick = async (level, option) => {
    // 如果整个组件被禁用或选项被禁用，不处理点击事件
    if (disabledAll || option.disabled) return;

    const newValue = [...internalValue];
    newValue.splice(level, newValue.length - level, option.value);

    // 非受控模式下更新内部值
    if (value === undefined) {
      setInternalValue(newValue);
    }

    // 加载下一级选项
    if (level < options.length - 1) {
      try {
        const nextLevelOptions = await loadOptions(level + 1, newValue);
        const newLevelOptions = [...levelOptions];
        newLevelOptions.splice(level + 1, newLevelOptions.length - level - 1, nextLevelOptions);
        setLevelOptions(newLevelOptions);
        setActiveKey((level + 1).toString());
      } catch (error) {
        console.error('加载下一级选项失败:', error);
      }
    } else {
      setVisible(false);
    }

    if (typeof onChange === 'function') {
      // 构建选中的选项数组
      const selectedOptions = newValue.map((val, idx) => {
        if (idx <= level) {
          return levelOptions[idx]?.find(item => item.value === val) || { value: val };
        }
        return { value: val };
      });

      onChange(newValue, selectedOptions);
    }
  };

  // 处理自定义输入框的值变化
  const handleCustomInputChange = (level, field, value) => {
    // 如果整个组件被禁用，不处理输入事件
    if (disabledAll) return;

    // 更新自定义输入值
    setCustomInputValues(prev => ({
      ...prev,
      [field]: value,
    }));

    // 构建完整的值数组
    const newValue = [...internalValue];

    // 确保 level 位置有一个对象
    if (typeof newValue[level] !== 'object' || newValue[level] === null) {
      newValue[level] = {};
    } else if (typeof newValue[level] !== 'object') {
      // 如果当前值不是对象，创建一个新对象并保留原始值
      const originalValue = newValue[level];
      newValue[level] = { _value: originalValue };
    }

    // 将自定义输入值添加到对应层级
    newValue[level] = {
      ...newValue[level],
      [field]: value,
    };

    // 非受控模式下更新内部值
    if (value === undefined) {
      setInternalValue(newValue);
    }

    if (typeof onChange === 'function') {
      onChange(newValue);
    }
  };

  // 处理标签页切换
  const handleTabChange = key => {
    // 如果整个组件被禁用，不处理标签页切换
    if (disabledAll) return;

    setActiveKey(key);
  };


  // 获取显示文本
  const displayText = useMemo(
    () =>
      internalValue
        .map((value, index) => {
          const config = options[index];
          if (!config) return '';

          // 处理自定义层级
          if (config.render) {
            return config.format?.(value) || Object.values(value || {}).join('-');
          }

          // 处理普通层级
          return levelOptions[index]?.find(item => item.value === value)?.label || '';
        })
        .filter(Boolean)
        .join(' / '),
    [internalValue, levelOptions, options]
  );

  // 渲染选项内容
  const renderOptionContent = level => {
    if (level >= options.length) return null;

    const levelConfig = options[level];
    const currentOptions = levelOptions[level] || [];

    if (levelConfig.render) {
      // 为自定义渲染组件注入 onChange 事件和禁用状态
      const renderProps = {
        onChange: (field, value) => handleCustomInputChange(level, field, value),
        values: customInputValues,
        // 如果当前层级的值是对象，传入该对象
        currentValues: typeof internalValue[level] === 'object' ? internalValue[level] : {},
        disabled: disabledAll, // 传递禁用状态给自定义渲染组件
      };

      // 如果 render 是函数，传入 props
      if (typeof levelConfig.render === 'function') {
        return levelConfig.render(renderProps);
      }

      return levelConfig.render;
    }

    if (currentOptions.length === 0) {
      return <div className={styles.emptyOptions}>暂无选项</div>;
    }

    return (
      <div className={styles.tabContent}>
        {currentOptions.map(option => {
          const isSelected = internalValue[level] === option.value;
          const isDisabled = disabledAll || option.disabled;

          return (
            <div
              key={option.value}
              className={`${styles.option} ${isSelected ? styles.selected : ''} ${isDisabled ? styles.disabled : ''}`}
              onClick={() => !isDisabled && handleOptionClick(level, option)}
              title={option.label}
            >
              {option.label}
              {isSelected && <Icon type="check" className={styles.checkIcon} />}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={`${styles.locationCascader} ${className || ''} ${disabledAll ? styles.disabledAll : ''}`} ref={containerRef}>
      <Popover title={title} trigger="hover" content={displayText} getPopupContainer={triggerNode => triggerNode.parentNode}>
        <Input
          {...rest}
          className={styles.selector}
          readOnly
          value={displayText}
          placeholder={placeholder}
          suffix={<Icon type={visible ? 'up' : 'down'} />}
          onClick={() => !disabledAll && setVisible(!visible)}
          disabled={disabledAll}
        />
      </Popover>

      {visible && (
        <div className={styles.dropdown}>
          <Spin spinning={internalLoading} size="small">
            {!inputValue && (
              <div className={styles.tabsContainer}>
                <Tabs activeKey={activeKey} onChange={handleTabChange} tabPosition="top" className={styles.tabs}>
                  {options.map((levelConfig, index) => (
                    <TabPane
                      tab={levelConfig.label}
                      key={index.toString()}
                      disabled={disabledAll || (index > 0 && (!internalValue || internalValue.length < index))}
                    >
                      {renderOptionContent(index)}
                    </TabPane>
                  ))}
                </Tabs>
              </div>
            )}
          </Spin>
        </div>
      )}
    </div>
  );
};

export default LocationCascader;
