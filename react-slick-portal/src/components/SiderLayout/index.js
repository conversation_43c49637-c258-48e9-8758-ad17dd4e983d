/* eslint-disable import/no-dynamic-require */
import React, { useState } from 'react';
import { Layout, Menu, Empty, Badge } from 'antd';
import menuFold from './imgs/menuFold.png';
import menuUnfold from './imgs/menuUnfold.png';
import styles from './index.less';

const { Sider, Content } = Layout;

const SiderLayout = React.memo(props => {
  const {
    height,
    menuList,
    needCount,
    activeKeys,
    onChange,
    layoutContent,
  } = props;

  const [collapsed, setCollapsed] = useState(false);

  const changeCollapse = () => {
    setCollapsed(!collapsed);
  };

  return (
    <div className={`${styles.siderLayout} ${collapsed ? styles.collapsedView : ''}`} style={{ height }}>
      <Layout>
        <Sider
          theme="light"
          collapsible
          collapsed={collapsed}
          collapsedWidth={44}
          trigger={(
            <img
              src={collapsed ? menuUnfold : menuFold}
              alt=""
              onClick={changeCollapse}
              style={{ width: 24, height: 24, marginRight: collapsed ? 10 : 16 }}
            />
          )}
        >
          <div
            className={styles.menuContainer}
            style={{ padding: '8px' }}
          >
            {
              menuList?.length > 0 ? (
                <Menu
                  theme="light"
                  mode="inline"
                  selectedKeys={activeKeys}
                  onClick={item => onChange(item)}
                >
                  {
                    menuList.map((item, index) => (
                      <Menu.Item key={item.value} className={styles.menuItem}>
                        <img
                          src={activeKeys.includes(item.value)
                            ? require(`./imgs/iconActive${index % 3}.png`)
                            : require(`./imgs/icon${index % 3}.png`)
                          }
                          width={20}
                          height={20}
                          alt=""
                        />
                        <span style={{ marginLeft: 12, fontSize: '14px' }}>{item.name}</span>
                        {
                          needCount ? (
                            <Badge count={item.count} style={{ fontSize: 10, height: 14, lineHeight: '14px', marginLeft: 12 }} />
                          ) : null
                        }
                      </Menu.Item>
                    ))
                  }
                </Menu>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )
            }
          </div>
        </Sider>
        <Layout>
          <Content
            style={{
              marginLeft: collapsed ? 16 : 8,
            }}
          >
            {layoutContent}
          </Content>
        </Layout>
      </Layout>
    </div>
  );
});
export default SiderLayout;
