@import '~antd/lib/style/themes/default.less';

.feedback {
  position: fixed;
  right: 32px;
  bottom: 63px;
  z-index: 2;
}

.text {
  color: rgba(0, 0, 0, 0.85);
}

.icon {
  width: 50px;
  height: 50px;
  color: #fff;
  font-size: 18px;
  line-height: 50px;
  text-align: center;
  background: linear-gradient(#553c69, #19234b);
  border-radius: 50%;
  box-shadow: 0 2px 6px #df268f63;
  cursor: pointer;
}

.hide {
  display: none;
}
.modal {
  padding: 5px;
  // background: linear-gradient(#553c69, #19234b);
  background: #0085d0;
  border-radius: 4px;
  box-shadow: 0 2px 7px rgb(0 0 0 / 60%);

  :global(.ant-modal-content),
  :global(.ant-modal-header) {
    background: transparent;
  }
  :global(.ant-modal-title),
  :global(.ant-modal-close) {
    color: rgba(255, 255, 255, 0.9);
  }
  :global(.ant-modal-body) {
    position: relative;
    padding: 0;
  }
  :global(.ant-radio-button-wrapper-checked) {
    // background-color: #7534ae !important;
    // border-color: #974ed6 !important;
    background-color: #40b3f4 !important;
    border-color: #40b3f4 !important;
    // color: rgba(0, 0, 0, 0.65) !important;
  }
  :global(.ant-radio-button-wrapper) {
    padding: 0 32px;
    &:not(:global(.ant-radio-button-wrapper-checked)):hover {
      color: #7d37ba;
    }
  }

  :global(.ant-modal-close-x) {
    width: 46px;
    height: 46px;
    line-height: 46px;
  }

  .header {
    padding: 12px;
    color: rgba(255, 255, 255, 0.9);
    // background: url(./images/bg.png) no-repeat center right;
    // height: 120px;
  }

  .title {
    font-size: 24px;
  }
  .tool {
    // padding-top: 12px;
    // color: rgba(255, 255, 255, 0.6);
    color: #fff;
    font-size: 14px;
  }
  .body {
    height: 460px;
    padding: 0 24px;
    overflow-y: auto;
    background: #fff;
    border-radius: 4px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
    }
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
    }
  }
}
.tabs {
  position: absolute;
  top: 10px;
  left: 50%;
  margin-left: -125px;
}
.myForm {
  padding: 30px 0;

  :global(.ant-form-item-with-help) {
    margin-bottom: 8px;
  }
}

.formAction {
  text-align: center;
  > :global(.ant-btn):not(:last-child) {
    margin-right: 8px;
  }
}

.formTitle {
  line-height: 1.6;
}

.formSection {
  position: relative;
  padding: 6px 0 6px 16px;
  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    display: inline-block;
    width: 6px;
    height: 14px;
    margin-top: -7px;
    background-color: #553c69;
    content: '';
  }
}

:global {
  .ifed-timeline {
    position: relative;
    margin-top: 2em;
    margin-bottom: 2em;
    padding: 0;

    &::before {
      position: absolute;
      top: 0;
      left: 18px;
      width: 4px;
      height: 100%;
      background: #e7eaec;
      content: '';
    }
  }

  .ifed-timeline-content .btn {
    float: right;
  }

  .ifed-timeline-footer {
    display: block;
    zoom: 1;
  }
  .ifed-timeline-footer::before,
  .ifed-timeline-footer::after {
    display: table;
    content: ' ';
  }
  .ifed-timeline-footer::after {
    clear: both;
    height: 0;
    font-size: 0;
    visibility: hidden;
  }

  .ifed-timeline-pull-left {
    float: left;
    color: #1ab394;
  }
  .ifed-timeline-pull-right {
    float: right;
  }

  .ifed-timeline-block {
    position: relative;
    margin: 16px 0;

    &.lazur {
      .ifed-timeline-icon {
        color: #fff;
        background-color: #23c6c8;
      }
    }

    &.navy {
      .ifed-timeline-icon {
        color: #fff;
        background-color: #1ab394;
      }
    }

    &.yellow {
      .ifed-timeline-icon {
        color: #fff;
        background-color: #f8ac59;
      }
    }

    &.red {
      .ifed-timeline-icon {
        color: #fff;
        background-color: #ed5565;
      }
    }

    &.blue {
      .ifed-timeline-icon {
        color: #fff;
        background-color: #1c84c6;
      }
    }
  }

  .ifed-timeline-block::after {
    display: table;
    clear: both;
    content: '';
  }

  .ifed-timeline-block:first-child {
    margin-top: 0;
  }

  .ifed-timeline-block:last-child {
    margin-bottom: 0;
  }

  .ifed-timeline-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    font-size: 16px;
    text-align: center;
    border: 3px solid #f1f1f1;
    border-radius: 50%;
  }

  .ifed-timeline-icon i {
    position: relative;
    top: 50%;
    left: 50%;
    display: block;
    width: 24px;
    height: 24px;
    margin-top: -9px;
    margin-left: -12px;
  }

  .ifed-timeline-content {
    position: relative;
    margin-left: 60px;
    padding: 16px;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 4px;
  }

  .ifed-timeline-content::after {
    display: table;
    clear: both;
    content: '';
  }

  .ifed-timeline-content h2 {
    margin-top: 4px;
    font-weight: 400;
    font-size: 18px;
  }

  .ifed-timeline-content p {
    margin: 1em 0;
    line-height: 1.6;
  }

  .ifed-timeline-content::before {
    position: absolute;
    top: 16px;
    right: 100%;
    width: 0;
    height: 0;
    border: 7px solid transparent;
    border-right: 7px solid rgba(0, 0, 0, 0.03);
    content: '';
  }
}
.success {
  margin-top: 50px;
}
