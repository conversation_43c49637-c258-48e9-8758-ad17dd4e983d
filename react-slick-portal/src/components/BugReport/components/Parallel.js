import React, { useReducer } from 'react';
import { Icon, Badge, Modal, Empty, Divider, Button } from 'antd';
import classNames from 'classnames';
import useDeepCompareEffect from './useDeepCompareEffect';
import FormLayout from './FormLayout';
import styles from '../styles.less';

const initialState = {
  current: null, // 当前申报的故障
  showModal: false,
  list: [],
  frame: 'list', // 可选值：'list'和'form'，表示弹窗当前对应的视图
};

function reducer(state, action) {
  const { type, payload } = action;

  if (type === 'updateList') {
    const { errors } = payload;
    return {
      ...state,
      list: errors, // [{parallel,reportTo,type,code,name,msg}]
    };
  }

  if (type === 'showModal') {
    return {
      ...state,
      showModal: true,
    };
  }

  if (type === 'hideModal') {
    return {
      ...state,
      frame: 'list',
      showModal: false,
    };
  }

  if (type === 'showForm') {
    const { current } = payload;
    return {
      ...state,
      current,
      frame: 'form',
    };
  }

  if (type === 'showList') {
    return {
      ...state,
      current: null,
      frame: 'list',
    };
  }

  if (type === 'clear') {
    return {
      ...state,
      list: [],
      current: null,
      frame: 'list',
    };
  }

  if (type === 'syncProps') {
    return {
      ...state,
      list: payload,
    };
  }

  if (type === 'submit_success') {
    const { key } = payload;
    const { list } = state;
    let newList = list;

    if (key) {
      newList = list.filter(i => i.key !== key);
    }

    return {
      ...state,
      list: newList,
      frame: 'list',
    };
  }

  throw Error();
}

function init(list) {
  if (Array.isArray(list)) {
    return { ...initialState, list };
  }
  return initialState;
}

function Parallel({ list }) {
  const [state, dispatch] = useReducer(reducer, list, init);

  const List = () => {
    if (state.list.length === 0) {
      return (
        <div className={styles.body}>
          <Empty style={{ marginTop: '130px' }} description="暂无数据" />
        </div>
      );
    }

    return (
      <div className={styles.body}>
        <div className="ifed-timeline">
          {state.list.map(item => (
            <div
              key={item.key}
              className={classNames('ifed-timeline-block', {
                red: String(item.type).toLocaleLowerCase() === 'system',
                yellow: String(item.type).toLocaleLowerCase() === 'service',
              })}
            >
              <div className="ifed-timeline-icon ">
                <Icon
                  type={String(item.type).toLocaleLowerCase() === 'system' ? 'desktop' : 'swap'}
                />
              </div>

              <div className="ifed-timeline-content">
                <h2>{item.name}</h2>
                <p>{item.msg}</p>
                <div className="ifed-timeline-footer">
                  <Button
                    type="link"
                    className="ifed-timeline-pull-right"
                    onClick={() =>
                      dispatch({
                        type: 'showForm',
                        payload: { current: { ...item, reportTo: 'all' } },
                      })
                    }
                  >
                    一键报障
                  </Button>
                  <span className="ifed-timeline-pull-left">{item.code}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const frames = {
    list: <List />,
    form: (
      <FormLayout
        current={state.current}
        onBack={() => dispatch({ type: 'showList' })}
        onSuccess={({ key }) => dispatch({ type: 'submit_success', payload: { key } })}
      />
    ),
  };

  useDeepCompareEffect(() => {
    if (Array.isArray(list)) {
      dispatch({
        type: 'syncProps',
        payload: list,
      });
    }
  }, [list]);

  return (
    <>
      <div
        className={classNames(styles.feedback, { [styles.hide]: state.list.length === 0 })}
        onClick={() => dispatch({ type: 'showModal' })}
      >
        <Badge count={state.list.length}>
          <div className={styles.icon}>
            <Icon type="bug" />
          </div>
        </Badge>
      </div>
      <Modal
        title={null}
        width={840}
        footer={null}
        className={styles.modal}
        centered
        visible={state.showModal}
        onCancel={() => dispatch({ type: 'hideModal' })}
      >
        <div className={styles.header}>
          <div className={styles.tool}>
            业务异常：{state.list.filter(i => i.type === 'service').length}
            <Divider type="vertical" />
            系统异常：{state.list.filter(i => i.type === 'system').length}
          </div>
        </div>
        {frames[state.frame] || frames.list}
      </Modal>
    </>
  );
}

export default Parallel;
