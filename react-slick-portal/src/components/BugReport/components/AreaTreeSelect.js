import React, { useReducer, useEffect, useCallback } from 'react';
import { Select, Row, Col } from 'antd';
import { useAsync } from '@umijs/hooks';
import get from 'lodash/get';
import { find } from 'lodash';
import request from '@/utils/request';
import useDeepCompareEffect from './useDeepCompareEffect';

// 获取主数据-省市
function queryLanIdList() {
  return request('portal/DomainDataController/getValuesList.do', {
    data: {
      busiNbr: 'BaseEntity',
      propertyName: 'landId',
    },
  });
}
// 获取主数据-区
function getOrgByParentId(parentId) {
  return request('orgauth/OrganizationController/getOrgByParentId.do', {
    data: {
      parentId,
    },
  });
}

const initialState = {
  citys: [],
  countys: [],
  value: [], // [{ label, value, orgLevel }]
  rootLevel: 1, // 开始层级 预期值1、2、3分别表示省市区
};

function reducer(state, action) {
  const { type, payload } = action;

  if (type === 'setCityOptions') {
    return {
      ...state,
      citys: payload,
    };
  }

  if (type === 'setCountyOptions') {
    return {
      ...state,
      countys: payload,
    };
  }

  throw Error();
}

function init(value) {
  if (!Array.isArray(value)) {
    return { ...initialState };
  }
  const lvl = get(value, '[0].orgLevel');

  return {
    ...initialState,
    rootLevel: [1, 2, 3].includes(parseInt(lvl, 10)) ? parseInt(lvl, 10) : 1,
  };
}

function getRootLevel(value) {
  const lvl = get(value, '[0].orgLevel');

  return [1, 2, 3].includes(parseInt(lvl, 10)) ? parseInt(lvl, 10) : 1;
}

function AreaTreeSelect({ onChange, value }) {
  const [state, dispatch] = useReducer(reducer, value, init);

  // 值为1，表示第一级是省；值为2表示第一级是市
  const rootLevel = getRootLevel(value);

  const { run: run1 } = useAsync(queryLanIdList, {
    manual: true,
    onSuccess: res => {
      if (Array.isArray(res)) {
        dispatch({ type: 'setCityOptions', payload: res });
      }
    },
  });

  const { run: run2 } = useAsync(getOrgByParentId, {
    manual: true,
    onSuccess: res => {
      if (Array.isArray(res)) {
        dispatch({ type: 'setCountyOptions', payload: res });
      }
    },
  });

  const formatValue = useCallback(
    (value, newValue, orgLevel) => {
      if (newValue === '99') {
        return [{ label: '江苏省', value: '99', orgLevel: 1 }];
      }
      if (orgLevel === 3) {
        const data = get(value, '[0]');
        return [
          data,
          {
            label: get(find(state.countys, { orgId: newValue }), 'orgName'),
            value: newValue,
            orgLevel: 3,
          },
        ];
      }

      return [
        {
          label: get(find(state.citys, { value: newValue }), 'name'),
          value: newValue,
          orgLevel: 2,
        },
      ];
    },
    [state.citys, state.countys]
  );

  // 获取省市数据
  useEffect(() => {
    run1();
  }, [run1]);

  // 如果父级是市，获取对应的区
  useDeepCompareEffect(() => {
    const parentId = get(value, '[0].value');
    if (rootLevel === 2 && parentId !== undefined) {
      run2(parentId);
    }
  }, [run2, value]);

  return (
    <Row gutter={8}>
      <Col span={12}>
        <Select
          placeholder="省/市"
          value={get(value, '[0].value')}
          onChange={newValue => {
            if (onChange) {
              onChange(formatValue(value, newValue));
            }
          }}
        >
          {state.citys.map(item => {
            return (
              <Select.Option value={item.value} key={item.value}>
                {item.name}
              </Select.Option>
            );
          })}
        </Select>
      </Col>

      {rootLevel === 2 ? (
        <Col span={12}>
          <Select
            placeholder="区"
            value={get(value, '[1].value')}
            onChange={newValue => {
              if (onChange) {
                onChange(formatValue(value, newValue, 3));
              }
            }}
          >
            {state.countys.map(item => {
              return (
                <Select.Option value={item.id} key={item.id}>
                  {item.orgName}
                </Select.Option>
              );
            })}
          </Select>
        </Col>
      ) : null}
    </Row>
  );
}

export default React.forwardRef((props, ref) => <AreaTreeSelect {...props} />);
