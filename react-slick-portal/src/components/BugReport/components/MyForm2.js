import React, { useState, useReducer, useEffect } from 'react';
import { Input, Form, Select, Radio, DatePicker, Row, Col, Button, Result, message } from 'antd';
import classNames from 'classnames';
import { useAsync } from '@umijs/hooks';
import get from 'lodash/get';
import delay from 'lodash/delay';
import moment from 'moment';
import { getItem } from '@/utils/utils';
import request from '@/utils/request';
import CodeTreeSelect from './CodeTreeSelect';
import styles from '../styles.less';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const tailFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

// 客户信息查询
function queryCustomer(custCode) {
  return request('portal/error/query', {
    data: {
      custCode,
    },
  });
}
// 客户信息查询
function submit(fieldValues) {
  return request('portal/error/emos', {
    data: fieldValues,
  });
}

const initialState = {
  custInfo: null,
};

function reducer(state, action) {
  const { type, payload } = action;

  if (type === 'setCustInfo') {
    return {
      ...state,
      custInfo: payload,
    };
  }

  throw Error();
}

const format = 'YYYY-MM-DD hh:mm:ss';

function getDefaultDes({ mobilePhone, address, groupCode, businessType }) {
  function getDefaultValue(val) {
    return typeof val !== 'string' || val.trim() === '' ? ' - ' : val;
  }

  return `客户报障类型：${getDefaultValue(businessType)}
集团名称/一网通账号：${getDefaultValue(groupCode)}
故障地址：${address}
故障开始时间：${moment().format(format)}
故障简单描述：无法正常使用
联系电话：${getDefaultValue(mobilePhone)}`;
}

function MyForm2({ form, onBack, onSuccess, current }) {
  const { natureOfFailure = '1' } = current; // 1代表一键报障 2代表快捷报障 除了顶部导航栏 其余都是一键报障

  const { getFieldDecorator, validateFields, setFieldsValue, resetFields } = form;

  const [state, dispatch] = useReducer(reducer, initialState);

  const [success, setSuccess] = useState(false);

  const { userInfo } = getItem('user');

  const { loading: loading1, run: run1 } = useAsync(queryCustomer, {
    manual: true,
    onSuccess: res => {
      const { resultCode, resultObject, resultMsg } = res;

      if (resultCode === '0' && get(resultObject, 'data')) {
        const custInfo = get(resultObject, 'data');
        dispatch({ type: 'setCustInfo', payload: custInfo });

        delay(() => {
          setFieldsValue({
            complaintLoca: get(custInfo, 'custAddress'),
            complaintDesc: getDefaultDes({
              mobilePhone: userInfo.mobilePhone,
              address: custInfo.custAddress,
              groupCode: custInfo.custCode,
              businessType: get(current, 'businessType'),
            }),
          });
        }, 600);
      } else {
        dispatch({ type: 'setCustInfo', payload: null });

        delay(() => {
          resetFields(['complaintLoca', 'complaintDesc']);
        }, 600);

        message.warning(resultMsg || '客户不存在');
      }
    },
  });

  const { loading: loading2, run: run2 } = useAsync(submit, {
    manual: true,
    onSuccess: res => {
      const { resultCode, resultMsg } = res;

      if (resultCode === '0') {
        // 切换到"成功上报"界面
        setSuccess(true);
      } else {
        message.error(resultMsg || '报障提交失败');
      }
    },
  });

  useEffect(() => {
    // 根据初始值查询集团信息
    if (current.groupCode) {
      run1(current.groupCode);
    }
  }, [current.groupCode, run1]);

  const handleSubmit = async e => {
    e.preventDefault();

    // 一键报障提交
    validateFields((err, fieldsValue) => {
      if (err) return;

      if (state.custInfo === null) {
        message.warning('客户不存在,请重新查询');
        return;
      }

      const { complainTypeList } = fieldsValue;
      const complaintType = complainTypeList.pop();

      const result = {
        // serviceType: '999',
        // serialNo: '99', // 去
        // serCaller: '99', //
        // callTime: '99',
        // opPerson: '99',
        // opCorp: '99',
        // opDepart: '99',
        // opContact: '99',
        // opTime: '99',
        title: complainTypeList.join('-'),
        urgentDegree: fieldsValue.urgentDegree,
        dealTime1: moment(fieldsValue.dealTime1).format(format),
        dealTime2: moment(fieldsValue.dealTime2).format(format),
        affectedAreas: fieldsValue.affectedAreas,
        customAttribution: state.custInfo.lanName,
        complaintTime: moment(fieldsValue.complaintTime).format(format),
        complaintNum: state.custInfo.contactPhone, // 故障单
        faultTime: moment(fieldsValue.faultTime).format(format),
        complaintLoca: fieldsValue.complaintLoca, // 故障地点
        termType: fieldsValue.termType, // 终端描述
        complaintDesc: fieldsValue.complaintDesc, // 投诉内容
        preDealResult: fieldsValue.preDealResult, // 预处理情况
        enterpriseCode: fieldsValue.enterpriseCode, // 企业代码
        serverCode: fieldsValue.serverCode, // 服务代码
        remarks: fieldsValue.remarks, //
        // groupSrTypeId: '99',
        APNName: fieldsValue.apnName,
        circuitCode: fieldsValue.circuitCode,
        customNo: state.custInfo.custCode,
        customName: state.custInfo.custName,
        customLevel: state.custInfo.custLevel,
        // provinceName: '99',
        cityName: state.custInfo.lanName,
        countyName: state.custInfo.regionName,
        bdeptContact: fieldsValue.bdeptContact,
        contactPhone: fieldsValue.contactPhone,
        cManagerPhone: fieldsValue.managerPhone,
        productName: fieldsValue.productName,
        grpcustMngrNm: fieldsValue.grpcustMngrNm,
        groupConcPrsnTelnum: fieldsValue.groupConcPrsnTelnum,
        ruralNetcmplntsFlag: fieldsValue.ruralNetcmplntsFlag,
        complaintType,
        natureOfFailure,
      };

      run2(result);
    });
  };

  const _Select = '--请选择--';

  const urgencyType = {
    非常紧急: 1,
    紧急: 2,
    一般: 3,
    不紧急: 4,
  };

  return (
    <div>
      {success === false ? (
        <>
          <h3 className={styles.formTitle}>网络报障单信息</h3>
          <Form {...formItemLayout}>
            <div className={styles.formSection}>客户信息</div>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="集团信息" required {...tailFormItemLayout}>
                  {getFieldDecorator('customNo', {
                    initialValue: get(current, 'groupCode'),
                    rules: [{ required: true, message: '集团信息不能为空' }],
                  })(
                    <Input.Search
                      loading={loading1}
                      placeholder="集团编号/集团名称"
                      // enterButton="查询"
                      style={{ width: 236 }}
                      onSearch={v => run1(v)}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
            {state.custInfo ? (
              <Row gutter={16} className="margin-bottom">
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      集团编码：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.custCode || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      集团级别：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.custLevel || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      集团名称：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.custName || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      联系人：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.contactPerson || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      联系人电话：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.contactPhone || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      所属地市：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.areaName || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      营业部：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.divisionName || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      客户经理名称：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.custManagerName || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      客户经理联系电话：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.custManagerTel || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      创建时间：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {moment(parseInt(state.custInfo.createDate, 10)).format(format) || ''}
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row className="ant-form-item">
                    <Col span={8} className={classNames('ant-form-item-label', styles.text)}>
                      联系地址：
                    </Col>
                    <Col span={16} className="ant-form-item-control-wrapper">
                      {state.custInfo.custAddress || ''}
                    </Col>
                  </Row>
                </Col>
              </Row>
            ) : null}

            <div className={styles.formSection}>故障单信息</div>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="紧急程度" {...tailFormItemLayout}>
                  {getFieldDecorator('urgentDegree', {
                    initialValue: 3,
                    rules: [{ required: true, message: '不能为空' }],
                  })(
                    <Radio.Group>
                      {Object.keys(urgencyType).map(key => (
                        <Radio key={urgencyType[key]} value={urgencyType[key]}>
                          {key}
                        </Radio>
                      ))}
                    </Radio.Group>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="故障影响范围">
                  {getFieldDecorator('affectedAreas', {
                    initialValue: 1,
                    rules: [{ required: true, message: '故障影响范围不能为空' }],
                  })(
                    <Select placeholder="请选择">
                      <Select.Option value={1}>本地</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="受理时限" required>
                  {getFieldDecorator('dealTime1', {
                    initialValue: moment(),
                    rules: [{ required: true, message: '受理时限不能为空' }],
                  })(
                    <DatePicker
                      placeholder={_Select}
                      format="YYYY-MM-DD HH:mm:ss"
                      name="dealTime1"
                      style={{ width: '100%' }}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="处理时限" required>
                  {getFieldDecorator('dealTime2', {
                    initialValue: moment().add(7, 'days'),
                    rules: [{ required: true, message: '处理时限不能为空' }],
                  })(
                    <DatePicker
                      placeholder={_Select}
                      format="YYYY-MM-DD HH:mm:ss"
                      name="dealTime2"
                      style={{ width: '100%' }}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="故障时间" required>
                  {getFieldDecorator('faultTime', {
                    initialValue: moment(),
                    rules: [{ required: true, message: '故障时间不能为空' }],
                  })(
                    <DatePicker
                      placeholder={_Select}
                      format="YYYY-MM-DD HH:mm:ss"
                      name="faultTime"
                      style={{ width: '100%' }}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="投诉时间" required>
                  {getFieldDecorator('complaintTime', {
                    initialValue: moment(),
                    rules: [{ required: true, message: '投诉时间不能为空' }],
                  })(
                    <DatePicker
                      placeholder={_Select}
                      format="YYYY-MM-DD HH:mm:ss"
                      name="complaintTime"
                      style={{ width: '100%' }}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="备注信息">
                  {getFieldDecorator('remarks', {})(<Input />)}
                </Form.Item>
              </Col>
            </Row>

            <div className={styles.formSection}>故障要素信息</div>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="故障地点" {...tailFormItemLayout}>
                  {getFieldDecorator('complaintLoca', {
                    rules: [{ required: true, message: '故障地点不能为空' }],
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="终端描述" {...tailFormItemLayout}>
                  {getFieldDecorator('termType', {
                    rules: [{ required: true, message: '终端描述不能为空' }],
                  })(<Input.TextArea autoSize={{ minRows: 3, maxRows: 3 }} />)}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="投诉内容" {...tailFormItemLayout}>
                  {getFieldDecorator('complaintDesc', {
                    rules: [{ required: true, message: '投诉内容不能为空' }],
                  })(<Input.TextArea autoSize={{ minRows: 6, maxRows: 6 }} />)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="预处理情况" {...tailFormItemLayout}>
                  {getFieldDecorator('preDealResult', {
                    initialValue: '请EOMS协助处理，谢谢',
                    rules: [{ required: true, message: '预处理情况不能为空' }],
                  })(<Input.TextArea autoSize={{ minRows: 3, maxRows: 3 }} />)}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  label="投诉业务类型编码"
                  {...tailFormItemLayout}
                >
                  {getFieldDecorator('complainTypeList', {
                    rules: [{ required: true, message: '投诉业务类型编码不能为空' }],
                  })(<CodeTreeSelect />)}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="企业代码">
                  {getFieldDecorator('enterpriseCode', {})(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="服务代码">
                  {getFieldDecorator('serverCode', {})(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="APN名称">
                  {getFieldDecorator('apnName', {})(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="传输专线电路代号">
                  {getFieldDecorator('circuitCode', {})(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="集客部联系人" required>
                  {getFieldDecorator('bdeptContact', {
                    rules: [{ required: true, message: '集客部联系人不能为空' }],
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="集客部联系人电话" required>
                  {getFieldDecorator('contactPhone', {
                    rules: [{ required: true, message: '集客部联系人电话不能为空' }],
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="集团联系人" required>
                  {getFieldDecorator('grpcustMngrNm', {
                    rules: [{ required: true, message: '集团联系人不能为空' }],
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="集团联系人电话" required>
                  {getFieldDecorator('groupConcPrsnTelnum', {
                    rules: [{ required: true, message: '集团联系人电话不能为空' }],
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="客户经理电话" required>
                  {getFieldDecorator('managerPhone', {
                    rules: [{ required: true, message: '客户经理电话不能为空' }],
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="产品名称" required>
                  {getFieldDecorator('productName', {
                    rules: [{ required: true, message: '产品名称不能为空' }],
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="农村网络标志" required>
                  {getFieldDecorator('ruralNetcmplntsFlag', {
                    rules: [{ required: true, message: '农村网络标志不能为空' }],
                  })(
                    <Select placeholder="请选择">
                      <Select.Option value={0}>否</Select.Option>
                      <Select.Option value={1}>是</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <div className={classNames(styles.formAction, 'margin-top-lg')}>
            {typeof onBack === 'function' ? (
              <Button type="default" onClick={onBack}>
                取消
              </Button>
            ) : null}

            <Button type="primary" onClick={handleSubmit} loading={loading2}>
              确认申告
            </Button>
          </div>
        </>
      ) : (
        <Result
          status="success"
          className={styles.success}
          title="故障上报成功"
          extra={[
            typeof onSuccess === 'function' ? (
              <Button type="primary" key="return" onClick={() => onSuccess({ key: current.key })}>
                返回列表
              </Button>
            ) : null,
          ]}
        />
      )}
    </div>
  );
}

export default Form.create()(MyForm2);
