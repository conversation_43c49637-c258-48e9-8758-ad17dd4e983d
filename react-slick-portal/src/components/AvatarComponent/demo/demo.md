### AvatarComponent

| Property | Description | Type | Default |
|----------|------------------------------------------|-------------|-------|
| disabled | Whether the button is available |false  or true|false |
~~~ jsx
import React, { useState, useEffect, useRef } from 'react';
import Avatar from '@/components/AvatarComponent';
function Index(props) {
 
  const[base64Url,setBase64Url] = useState("");
  function getImageInfo(file){
    setBase64Url(file.base64);
  }
  return (
      <div>
         <img  src={base64Url} style={{width:'25px',height:'25px',float: 'left',marginRight:'4px'}}/>
         <Avatar buttonTitle="更新头像" disabled={false} getImageInfo={getImageInfo} />
      </div>
  );
}

export default Index;
~~~