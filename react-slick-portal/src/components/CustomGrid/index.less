@import '~antd/lib/style/themes/default.less';
@import '../../../config/theme/variables.less';

.combogrid {
  :global(.ant-input-suffix) {
    color: fade(@black, 20%) !important;
  }
}
.combogridDropdown {
  padding: 16px 16px 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.search {
  margin-bottom: @slick-space-base;
}

:global(.has-error) {
  .combogridDropdown {
    :global(.ant-input) {
      border-color: @border-color-base !important;
      &:hover {
        border-color: @border-color-base !important;
      }
      &:focus {
        box-shadow: none;
      }
    }
  }
}

.searchForm {
  margin-bottom: 16px;
  background-color: #fff;
}

.formItemCol {
  margin-bottom: 16px;
}

.formItem {
  display: flex;

  :global {
    .ant-form-item-label {
      width: auto !important;
      text-align: right;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-form-item-control-wrapper {
      flex: 1;
    }
  }
}

.buttonCol {
  margin-bottom: 16px;

  :global {
    .ant-form-item {
      margin-bottom: 0;
      text-align: right;
    }
  }
}
.inputStyle{
  width: 80% !important;
}