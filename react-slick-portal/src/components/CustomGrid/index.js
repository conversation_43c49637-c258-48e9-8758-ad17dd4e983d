import React, { useState, useRef, useEffect } from 'react';
import { Form, Input, Icon, Select, DatePicker, Col, Row, Button } from 'antd';
import { useAntdTable, useUpdateEffect } from '@umijs/hooks';
import PropTypes from 'prop-types';
import Trigger from 'rc-trigger';
import 'rc-trigger/assets/index.css';
import isFunction from 'lodash/isFunction';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
import styles from './index.less';

const { RangePicker } = DatePicker;

function getTableData({ current, pageSize, method, url, ...rest }) {
  return request(url, {
    method,
    data: {
      pageNum: current,
      pageSize,
      ...rest,
    },
  }).then(res => {
    if (Array.isArray(res.list)) {
      return {
        total: res.total,
        data: res.list.map(val => {
          // eslint-disable-next-line no-shadow
          const { children, ...rest } = val;
          return rest;
        }),
      };
    }
    return {
      total: 0,
      data: [],
    };
  });
}

function getValue(value, tokenSeparators, label) {
  const result = [];
  if (Array.isArray(value)) {
    value.map(val => result.push(val[label]));
  } else if (typeof value === 'string') {
    result.push(value);
  }
  return result.join(tokenSeparators);
}

const CustomGrid = React.memo(
  ({
    form,
    onChange,
    value,
    url,
    method = 'POST',
    action = 'click',
    placeholder = '请选择',
    searchPlaceholder = '请输入关键字搜索',
    columns = [],
    rowKey = 'id',
    label = 'name',
    tokenSeparators = ',',
    extra,
    pick = 'radio',
    disabled = false,
    params = {},
    onConfirm = () => {},
    outsideOpen = false,
    setOutsideOpen,
    multipleFlag = false,
    suffix,
    searchFields = [],
    ...restProps
  }) => {
    const [selectedRows, setSelectedRows] = useState(value);
    const [keyword, setKeyword] = useState('');
    const [open, setOpen] = useState(false);

    const tableEl = useRef(null);
    const { getFieldDecorator } = form;

    // 构建查询参数
    const buildSearchParams = ({ current, pageSize }) => {
      let searchParams = {};

      // 如果有searchFields配置，则根据searchFields构建查询参数
      if (searchFields && Array.isArray(searchFields) && searchFields.length > 0) {
        searchParams = form.getFieldsValue();
      } else {
        // 默认使用filterVal
        searchParams = { filterVal: keyword };
      }

      return { current, pageSize, method, url, ...searchParams, ...params };
    };

    const {
      tableProps,
      search: { submit },
    } = useAntdTable(query => getTableData(buildSearchParams(query)), [keyword, JSON.stringify(params), url, JSON.stringify(searchFields)], {
      defaultPageSize: 5,
      form,
    });

    const { pagination, loading, ...restTableProps } = tableProps;

    // 渲染不同类型的表单控件
    const renderFormControl = (field, fieldType, fieldName, fieldLabel) => {
      switch (fieldType) {
        case 'input':
          return <Input placeholder={`请输入${fieldLabel}`} style={{ width: '100%' }} allowClear />;
        case 'select':
          return (
            <Select placeholder={`请选择${fieldLabel}`} allowClear style={{ width: '100%' }}>
              {(field.options || []).map((opt, i) => (
                <Select.Option key={i} value={opt.value || opt}>
                  {opt.label || opt}
                </Select.Option>
              ))}
            </Select>
          );
        case 'datePicker':
          return <DatePicker style={{ width: '100%' }} />;
        case 'rangePicker':
          return <RangePicker style={{ width: '100%' }} />;
        default:
          return <Input placeholder={`请输入${fieldLabel}`} style={{ width: '100%' }} allowClear />;
      }
    };

    // 渲染搜索表单
    const renderSearchForm = () => {
      // 如果没有searchFields或者不是数组，则使用默认的搜索框
      if (!searchFields || !Array.isArray(searchFields) || searchFields.length === 0) {
        return <Input.Search placeholder={searchPlaceholder} allowClear className={styles.search} onSearch={val => setKeyword(val)} />;
      }

      // 计算前面列的span总和
      const totalSpan = searchFields.reduce((sum, field) => {
        const fieldSpan = field.span || 8;
        return sum + fieldSpan;
      }, 0);

      // 计算按钮列的offset和span
      /*
      - 如果前面的列正好占满一行或多行，按钮列会从新行开始（span=24, offset=0）
      - 如果前面的列没有占满一行，按钮列会紧跟在最后一个字段后面
      * */
      const remainingSpan = totalSpan % 24;
      const buttonOffset = remainingSpan === 0 ? 0 : remainingSpan;
      const buttonSpan = remainingSpan === 0 ? 24 : Math.min(24 - remainingSpan, 8);

      // 如果有searchFields，则渲染表单
      return (
        <Form className={styles.searchForm}>
          <Row gutter={16}>
            {searchFields.map(field => {
              const fieldName = field.name || field;
              const fieldLabel = field.label || fieldName;
              const fieldType = field.type || 'input';
              const fieldRules = field.rules || [];
              const fieldSpan = field.span || 8;

              return (
                <Col span={fieldSpan} key={fieldName} className={styles.formItemCol}>
                  <Form.Item label={fieldLabel} className={styles.formItem}>
                    {getFieldDecorator(fieldName, {
                      rules: fieldRules,
                    })(renderFormControl(field, fieldType, fieldName, fieldLabel))}
                  </Form.Item>
                </Col>
              );
            })}
            <Col span={buttonSpan} offset={buttonOffset} className={styles.buttonCol} style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                size="small"
                loading={loading}
                disabled={loading}
                onClick={() => {
                  form.validateFields(err => {
                    if (!err) {
                      submit();
                    }
                  });
                }}
                style={{ marginRight: 8 }}
              >
                查询
              </Button>
              <Button
                size="small"
                loading={loading}
                onClick={() => {
                  form.resetFields();
                }}
              >
                重置
              </Button>
            </Col>
          </Row>
        </Form>
      );
    };

    // outsideOpen可用于外部点击确认按钮后关闭此组件展示
    function onPopupVisibleChange(isVisible) {
      if (!isVisible) {
        onConfirm(selectedRows);
      }
      if (typeof setOutsideOpen === 'function' && isVisible) {
        setOutsideOpen(true);
      }
      setOpen(isVisible);
    }

    /**
     * 1、input元素被get包裹 会从props带入onChange方法,selectedRows 保证form取到的值是实时最新的
     * 2、只在更新阶段执行onChange，避免在初始化时触发rule校验（如果被getFieldDecorator包裹）
     */
    useUpdateEffect(() => {
      if (isFunction(onChange)) {
        onChange(selectedRows);
      }
    }, [selectedRows, onChange]);

    /**
     * 外部通过setFieldsValue设置值的时候，即时更新
     */
    useEffect(() => {
      if (Array.isArray(value)) {
        if (value.length === 0) {
          setKeyword('');
        }
        setSelectedRows(value);
      } else if (!value) {
        setKeyword('');
      }
    }, [value]);

    return (
      <Trigger
        className={styles.combogrid}
        action={[action]}
        popupAlign={{
          overflow: {
            adjustX: true, // true 表示当下拉框宽度>所在容器宽度时 会自动切换 左右对齐
            adjustY: true, // true 表示当下拉框高度 > 所在容器高度是 会自动切换 上下对齐
          },
          offset: [0, 4],
        }}
        getPopupContainer={trigger => trigger.parentNode}
        onPopupVisibleChange={isVisible => onPopupVisibleChange(isVisible)}
        mouseEnterDelay={0.3}
        builtinPlacements={{
          left: {
            points: ['cr', 'cl'],
          },
          right: {
            points: ['cl', 'cr'],
          },
          top: {
            points: ['bc', 'tc'],
          },
          bottom: {
            points: ['tc', 'bc'],
          },
          topLeft: {
            points: ['bl', 'tl'],
          },
          topRight: {
            points: ['br', 'tr'],
          },
          bottomRight: {
            points: ['tr', 'br'],
          },
          bottomLeft: {
            points: ['tl', 'bl'],
          },
        }}
        popupClassName={styles.combogridDropdown}
        popupVisible={open && outsideOpen}
        popup={(
          <div>
            {renderSearchForm()}
            <SlickTable
              loading={loading}
              ref={tableEl}
              extra={extra}
              rowKey={rowKey}
              pick={pick}
              data={{ pagination }}
              {...restTableProps}
              selectedRowKeys={selectedRows.map(row => row[rowKey])}
              onSelectRow={_selectedRows => {
                setSelectedRows(_selectedRows);
                if (!multipleFlag) {
                  // 单选
                  onConfirm(_selectedRows);
                  setOpen(false);
                }
              }}
              columns={columns}
            />
          </div>
        )}
        {...restProps}
      >
        <Input
          placeholder={placeholder}
          disabled={disabled}
          className={styles.inputStyle}
          // allowClear
          // onChange={e => {
          //   window.tableEl = tableEl;
          //   if (tableEl && tableEl.current) {
          //     tableEl.current.cleanSelectedKeys([], []);
          //   }
          // }}
          suffix={suffix || <Icon type="table" />}
          value={getValue(selectedRows, tokenSeparators, label)}
        />
      </Trigger>
    );
  }
);

CustomGrid.defaultProps = {
  value: [],
  method: 'post',
  placeholder: '',
  searchPlaceholder: '',
  action: 'click',
  stretch: 'width',
  popupStyle: {},
  popupPlacement: 'bottomLeft',
  tokenSeparators: ',',
  destroyPopupOnHide: false,
  disabled: false,
  extra: null,
  pick: 'checkbox',
  params: {},
  outsideOpen: true,
  onConfirm: () => {},
  searchFields: null,
};

CustomGrid.propTypes = {
  value: PropTypes.array,
  method: PropTypes.oneOf(['post', 'get']),
  url: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  rowKey: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,
  columns: PropTypes.arrayOf(PropTypes.object).isRequired,
  placeholder: PropTypes.string,
  searchPlaceholder: PropTypes.string,
  tokenSeparators: PropTypes.string,
  action: PropTypes.oneOf(['hover', 'click']),
  popupStyle: PropTypes.object,
  stretch: PropTypes.oneOf(['width', 'height', 'minWidth', 'minHeight']),
  popupPlacement: PropTypes.oneOf(['top', 'right', 'bottom', 'right', 'topLeft', 'topRight', 'bottomLeft', 'bottomRight']),
  destroyPopupOnHide: PropTypes.bool,
  disabled: PropTypes.bool,
  outsideOpen: PropTypes.bool,
  params: PropTypes.object,
  extra: PropTypes.oneOfType([PropTypes.func, PropTypes.element]),
  pick: PropTypes.oneOf(['checkbox', 'radio']),
  onConfirm: PropTypes.func,
  searchFields: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        name: PropTypes.string,
        label: PropTypes.string,
        type: PropTypes.string,
        rules: PropTypes.array,
      }),
    ])
  ),
};

export default Form.create()(CustomGrid);
