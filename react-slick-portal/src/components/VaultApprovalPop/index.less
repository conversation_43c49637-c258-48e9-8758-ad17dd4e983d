.textAreaFormItem {
  :global {
    .ant-form-item-control {
      line-height: normal !important;
      // height: 100% !important;
      .ant-form-item-children{
        height: 100% !important;
      }
    }
  }
}

.approvalDiv {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  font-weight: bold;
  text-align: left;
}

.approvalLeft {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.approvalLeft div {
  height: 40px;
}

.approvalRight {
  display: flex;
  flex-direction: column;
  text-align: left;
}
.approvalRight div {
  height: 40px;
}

.approvalRight Input {
  // height: 40px;
}


.refreshBtn {
  font-weight: 500;
  color: white;
}
