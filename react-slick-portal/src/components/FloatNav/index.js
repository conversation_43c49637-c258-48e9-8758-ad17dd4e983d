import React, { useState, useEffect, useRef } from 'react';
import { Icon, Anchor } from 'antd';
import classNames from 'classnames';
import styles from './styles.less';

const { Link } = Anchor;

const FloatNav = ({ items = [], position = 'right', offsetTop = 100, offsetBottom = 100, onClick, containerId }) => {
  const [collapsed, setCollapsed] = useState(true);

  const containerRef = useRef(null);
  useEffect(() => {
    // 获取容器元素，如果提供了container则使用，否则默认使用document.body
    const containerElement = containerId ? document.getElementById(containerId) : document.body;
    containerRef.current = containerElement;
  }, [containerId]);

  const handleToggle = () => {
    setCollapsed(!collapsed);
  };

  // 添加回到顶部功能
  const scrollToTop = () => {
    // 获取当前容器
    const containerElement = containerRef.current;

    // 滚动到容器顶部
    if (containerElement === document.body) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    } else {
      containerElement.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }

    // 如果有锚点，模拟点击第一个锚点
    if (items.length > 0) {
      setTimeout(() => {
        const firstAnchor = containerElement.querySelector(items[0].href) || document.querySelector(items[0].href);
        if (firstAnchor) {
          firstAnchor.scrollIntoView({ behavior: 'smooth' });
          onClick && onClick({ href: items[0].href, title: items[0].title });
        }
      }, 100); // 添加短暂延时确保滚动到顶部后再触发锚点
    }
  };

  if (!containerRef?.current) {
    return null;
  }
  return (
    <div
      className={classNames(styles.floatNav, {
        [styles.collapsed]: collapsed,
        [styles.left]: position === 'left',
        [styles.right]: position === 'right',
      })}
      onMouseLeave={() => setCollapsed(true)}
    >
      <div className={styles.content}>
        <Anchor
          affix={false}
          offsetTop={offsetTop}
          offsetBottom={offsetBottom}
          showInkInFixed
          bounds={50}
          getContainer={() => containerRef.current}
          onClick={(e, link) => {
            e.preventDefault();
            // 先在容器内查找，如果找不到再在整个文档中查找
            const containerElement = containerRef.current;
            const targetElement = containerElement.querySelector(link.href) || document.querySelector(link.href);

            if (targetElement) {
              targetElement.scrollIntoView({ behavior: 'smooth' });
              onClick && onClick(link);
            }
          }}
        >
          {items.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              title={(
                <div className={styles.item}>
                  {item.icon && <Icon type={item.icon} />}
                  <span className={styles.text}>{item.title}</span>
                </div>
              )}
            />
          ))}
        </Anchor>
      </div>
      <div className={styles.toggle} onClick={collapsed ? handleToggle : scrollToTop}>
        {collapsed ? <Icon type="left" /> : <Icon type="arrow-up" />}
      </div>
    </div>
  );
};

export default FloatNav;
