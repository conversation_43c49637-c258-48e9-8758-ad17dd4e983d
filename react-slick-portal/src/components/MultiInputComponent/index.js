import React, { useState, useEffect, useMemo } from 'react';
import { Button, Col, Icon, Input, Popover, Row } from 'antd';

const MultiInputComponent = ({ fields = [], value, onChange, disabled = false, placeholder, suffix, ...other }) => {
  // 确保 fields 是数组
  const fieldsArray = Array.isArray(fields) ? fields : [];

  const [visible, setVisible] = useState(false);
  const [internalValues, setInternalValues] = useState([]);
  const [tempValues, setTempValues] = useState([]);
  // 处理受控和非受控模式
  const values = value !== undefined ? value : internalValues;

  // 初始化 values 数组
  useEffect(() => {
    if (value === undefined) {
      const initialValues = fieldsArray.map(() => '');
      setInternalValues(initialValues);
    }
  }, [fieldsArray, value]);

  // 当 Popover 打开时，初始化临时值
  useEffect(() => {
    if (visible) {
      setTempValues([...values]);
    }
  }, [visible, values]);

  const handleVisibleChange = newVisible => {
    setVisible(newVisible);
  };

  const handleInputChange = (index, e) => {
    const newValue = e.target.value;

    // 更新临时值
    const newTempValues = [...tempValues];
    newTempValues[index] = newValue;
    setTempValues(newTempValues);
  };

  // 生成输入框
  const generateInputs = () =>
    fieldsArray.map((item, index) => (
      <Row key={index} style={{ marginBottom: '8px' }}>
        <Col span={24} style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ width: '80px', marginRight: '8px', textAlign: 'right' }}>{item.label}：</span>
          <Input
            allowClear
            placeholder={`请输入${item.label}`}
            value={tempValues[index] || ''}
            onChange={e => handleInputChange(index, e)}
            disabled={disabled}
          />
        </Col>
      </Row>
    ));

  // 提交按钮处理函数
  const handleSubmit = () => {
    // 如果是受控组件，只触发 onChange
    if (value !== undefined) {
      if (onChange) {
        onChange([...tempValues]);
      }
    } else {
      // 非受控组件，更新内部状态
      setInternalValues([...tempValues]);
      if (onChange) {
        onChange([...tempValues]);
      }
    }
    // 确保关闭 Popover
    setVisible(false);
  };

  const formatDisplayVal = useMemo(
    () =>
      fields
        .map((field, idx) => {
          const val = value && value[idx] !== undefined ? value[idx] : '-';
          return `${val}${field.label}`;
        })
        .join('-'),
    [fields, value]
  );
  // Popover 中的内容
  const popoverContent = (
    <div style={{ width: '300px' }}>
      {generateInputs()}
      {!disabled && (
        <div style={{ textAlign: 'right', marginTop: '10px' }}>
          <Button size="small" type="primary" onClick={handleSubmit} style={{ marginRight: '8px' }}>
            确定
          </Button>
          <Button size="small" onClick={() => setVisible(false)}>
            关闭
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <Popover content={popoverContent} title="详细信息" trigger="click" visible={visible} onVisibleChange={handleVisibleChange}>
      <Input style={{ width: '80%' }} {...other} placeholder={placeholder} suffix={suffix || <Icon type="profile" />} value={formatDisplayVal} readOnly />
    </Popover>
  );
};

export default MultiInputComponent;
