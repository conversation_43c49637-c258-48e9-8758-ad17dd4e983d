import React from 'react';
import { Link } from 'umi';
import { message } from 'antd';
import { connect } from 'dva';
import { findMenu } from '@/layouts/index';

const AuthLink = ({ to, children, all, route, ...rest }) => {
  const handleClick = e => {
    // 获取目标路径的菜单信息
    const targetMenu = findMenu(to, all, route);

    // 如果找不到菜单信息,说明没有权限访问
    if (!targetMenu) {
      e.preventDefault(); // 阻止默认跳转行为
      message.error('无权限，请联系管理员授权');
    }
  };

  return (
    <Link to={to} onClick={handleClick} {...rest}>
      {children}
    </Link>
  );
};

export default connect(({ menu: { all }, route }) => ({
  all,
  route,
}))(AuthLink);
