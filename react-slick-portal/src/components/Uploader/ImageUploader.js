import React, { Fragment, PureComponent } from 'react';
import {
  Icon,
  Modal,
  Empty,
} from 'antd';
import UploadContainer, { FileType } from './UploadContainer';

const CustomCurrentUploadedFileProcessHandler = file => {
  const _file = Array.isArray(file.response) && file.response.length > 0 ? file.response[0] : {}
  const { photoUrl, fileGetUrl } = _file;
  file.url = fileGetUrl;
  file.thumbUrl = fileGetUrl;
  file.relativePath = fileGetUrl;
  return file;
};

class ImageUploader extends PureComponent {
  state = {
    previewing: false,
    previewingFile: {},
  }

  handlePreview = file => {
    this.setState({
      previewing: true,
      previewingFile: file,
    });
  }

  handleCancelPreview = () => {
    this.setState({
      previewing: false,
    });
  }

  render() {
    if (this.props.readonly && this.props.uploadedFileList.length === 0) {
      return <Empty className="ant-empty-small" style={{ width: 104, height: 104 }} />;
    }
    return (
      <Fragment>
        <UploadContainer
          {...this.props}
          listType="picture-card"
          mode={FileType.image}
          fileProcessHandler={CustomCurrentUploadedFileProcessHandler}
          previewHandler={this.props.customPreviewHandler || this.handlePreview}
        >
          {this.props.uploadedFileList.length >= this.props.maximum || this.props.readonly ? null : (() => (
            <Fragment>
              <Icon type="plus" />
              <div className="ant-upload-text">
                {this.props.title}
                <br />
                {this.props.maximum ? (
                  `最多${this.props.maximum}
                张`
                ) : null}
              </div>
            </Fragment>
          ))()}
        </UploadContainer>
        <Modal
          visible={this.state.previewing}
          footer={null}
          onCancel={this.handleCancelPreview}
        >
          <img
            alt=""
            style={{ width: '100%' }}
            src={this.state.previewingFile.url || this.state.previewingFile.thumbUrl || ''}
          />
        </Modal>
      </Fragment>
    );
  }
}

export default ImageUploader;
