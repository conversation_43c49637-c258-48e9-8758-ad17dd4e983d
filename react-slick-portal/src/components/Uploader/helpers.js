/**
 * 检查单个文件大小
 */
export const checkFileSize = (maxSize, file) => new Promise(resolve => {
  const validated = file.size <= 1024 * 1024 * maxSize;
  resolve([validated ? null : `上传图片大小最大为 ${maxSize} MB`, validated]);
});

/**
 * 检查单个文件大小（KB）
 */
export const checkFileSizeForKB = (maxSize, file) => new Promise(resolve => {
  if (!maxSize || Number.isNaN(maxSize)) {
    resolve([null, true]);
  }
  const validated = file.size <= 1024 * maxSize;
  resolve([validated ? null : `上传图片大小最大为 ${maxSize} KB`, validated]);
});

/**
 * 检查文件类型
 */
export const checkType = (permittedFormats, file) => new Promise(resolve => {
  const nameComponents = file.name.split('.') || [];
  const extension = nameComponents[nameComponents.length - 1] || '';
  const validated = permittedFormats.includes(extension.toLowerCase());
  resolve([validated ? null
    : `上传图片格式应为 ${permittedFormats.join(',')} 其中一种`, validated]);
});

/**
 * 文件尺寸校验
 * @param {尺寸} dimension
 * @param {文件} file
 */
export const checkDimension = (dimension = [], file) => new Promise(resolve => {
  if (dimension.length === 0) {
    resolve([null, true]);
  } else {
    const _URL = window.URL || window.webkitURL;
    const img = new Image();
    img.onload = function () {
      const valid = (img.width === dimension[0] && img.height === dimension[1]) || (img.width / img.height === dimension[0] / dimension[1]);
      resolve(valid ? [null, true] : ['图片尺寸不符合规范，请重新上传！', false]);
    };
    img.src = _URL.createObjectURL(file);
  }
});
