/* eslint-disable no-await-in-loop */
import React, { Component } from 'react';

import {
  Upload,
  message,
  Empty,
} from 'antd';

// import { connect } from 'react-redux';
import {
  checkDimension,
  checkFileSize,
  checkFileSizeForKB,
  checkType,
} from './helpers';
import { ImageUploadAddress } from '@/services/message';

const DefaultUploadHeader = () => ({
  token: window.localStorage.getItem('mg_token') || '',
});

export const FileType = {
  image: 'image',
  video: 'video',
  file: 'file',
};

/**
 * 文件数量
 */
// const FileAmount = 5;

/**
 * 上传文件最大大小，单位 MB
 */
const DefaultSingleFileMaxSize = {
  [FileType.image]: 4,
  [FileType.video]: 20,
  [FileType.file]: 20,
};

/**
 * 允许上传的文件类型
 */
export const DefaultPermittedFormat = {
  [FileType.image]: ['jpg', 'jpeg', 'png'],
  [FileType.video]: ['mp4'],
  [FileType.file]: ['jpg', 'jpeg', 'bmp', 'png', 'pdf', 'rar', 'zip'],
  // [FileType.file]: ['doc', 'docx', 'pdf'],
};

class UploadContainer extends Component {
  constructor(props) {
    super(props);
    this.uploadedFileList = [];
    this.singleFileMaxSize = (props.singleFileMaxSize || DefaultSingleFileMaxSize)[props.mode || FileType.image];
    if (props.fileSize && !Number.isNaN(props.fileSize)) {
      this.singleFileMaxSize = props.fileSize;
    }
    this.fileSizeForKB = props.fileSizeForKB;
    this.permittedFormats = (props.permittedFormats || DefaultPermittedFormat)[props.mode || FileType.image];
    // 如果存在自定义属性
    if (props.accept) {
      this.permittedFormats = [...props.accept];
    }
    this.permittedFormatsAcceptString = this.permittedFormats.map(format => `.${format}`).join(', ');
    this.dimension = Array.isArray(props.dimension) && props.dimension?.length === 2 ? props.dimension : [];
  }

  componentWillReceiveProps(nextprops) {
    // if ((nextprops.uploadedFileList || []).length <= 0) {
    //   // 清空时，同时清楚内部状态
    //   this.uploadedFileList = [];
    // }
    this.uploadedFileList = [...nextprops.uploadedFileList];
  }

  handleBeforeUpload = async file => new Promise((resolve, reject) => {
    /**
     * 将所有上传前的检验放到这里
     *
     * 检验函数的结果返回格式
     * [#错误原因#, true/false]
     */
    const checkList = [
      checkFileSize(this.singleFileMaxSize, file),
      checkFileSizeForKB(this.fileSizeForKB, file),
      checkType(this.permittedFormats, file),
      checkDimension(this.dimension, file),
    ];
    const promiseRes = Promise.all(checkList);
    promiseRes.then(res => {
      const errItem = res.find(arr => !arr[1]);
      if (errItem) {
        message.warning(errItem[0]);
        reject();
      } else {
        if (this.props.onUploading) {
          this.props.onUploading();
        }
        resolve(file);
      }
    });
  })


  handleChange = ({ file, fileList }) => {
    switch (file.status) {
      case 'removed':
        {
          // 对删除文件的操作，直接返回，只做尝试删除的操作
          this.uploadedFileList = fileList.filter(ele => ele.url !== file.url);
          this.props.onFileListChange({
            file,
            fileList: this.uploadedFileList,
          });
          break;
        }
      case 'error':
        {
          // 上传发生错误，不显示错误图片
          this.props.onFileListChange({
            file,
            fileList: this.uploadedFileList.filter(n => n.url), // 去除上传中的数据
          });
          if (this.props.onFinish) this.props.onFinish(false);
          message.error('上传文件失败');
          break;
        }
      case '': break; // 被 beforeUpload 阻止的上传
      case undefined: break; // 被 beforeUpload 阻止的上传
      default:
        {
          this.props.onFileListChange({ file, fileList: [...fileList] }); // 由于PureComponent， fileList不能直接传引用对象fileList
          if (file.response) {
            const _response = Array.isArray(file.response) && file.response.length > 0 ? file.response[0] : '';
            if (_response.resultCode === '0000') {
              if (!this.props.fileProcessHandler) {
                throw new Error('需提供文件对象处理函数');
              }
              const finalFile = this.props.fileProcessHandler(file);
              // this.uploadedFileList.push(finalFile);
              for (let i = 0; i < this.uploadedFileList.length; i += 1) {
                if (this.uploadedFileList[i].uid === finalFile.uid) {
                  this.uploadedFileList[i] = finalFile;
                }
              }
              this.props.onFileListChange({
                file: finalFile,
                fileList: this.uploadedFileList,
              });
              if (this.props.onFinish) this.props.onFinish(finalFile);
            } else {
              this.props.onFileListChange({
                file,
                fileList: this.uploadedFileList.filter(n => n.url), // 去除上传中的数据
              });
              if (this.props.onFinish) this.props.onFinish(false);
              message.error('上传文件失败');
            }
          }
        }
    }
  }

  handleUploadData = file => ({})

  handleRemoveImage = file => {
    if (this.props.handleRemove) {
      this.props.handleRemove();
    }
    const deleteImgName = file.url;
    const params = {
      deleteImgName,
    };

    const promise = new Promise(async (resolve, reject) => {
      try {
        resolve();
      } catch (e) {
        // message.error('删除图片失败');
        // reject();
        // TODO: 暂时对忽视删除图片失败的情况，前端将数据删除
        resolve();
      }
    });
    return promise;
  }

  render() {
    const {
      uploadedFileList,
      header,
      listType,
      previewHandler,
      readonly,
      disabled,
    } = this.props;
    if (this.props.readonly && this.props.uploadedFileList.length === 0) {
      return <Empty className="ant-empty-small" style={{ width: 104, height: 104 }} />;
    }
    return (
      <Upload
        action={this.props.uploadUrl || ImageUploadAddress}
        headers={header || DefaultUploadHeader()}
        listType={listType}
        disabled={disabled}
        fileList={uploadedFileList}
        onPreview={previewHandler}
        onChange={this.handleChange}
        data={this.handleUploadData}
        onRemove={this.handleRemoveImage}
        beforeUpload={this.handleBeforeUpload}
        accept={this.permittedFormatsAcceptString}
        showUploadList={{
          showPreviewIcon: true,
          showRemoveIcon: !readonly,
        }}
      >
        {this.props.children}
      </Upload>
    );
  }
}

export default UploadContainer;
