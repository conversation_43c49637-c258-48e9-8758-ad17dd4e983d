下拉表格

## API

| 参数(*非必填)       | 说明                                                                                                            | 类型                        | 默认值                       |
| ------------------- | --------------------------------------------------------------------------------------------------------------- | --------------------------- | ---------------------------- |
| url                 | 请求地址                                                                                                        | string                      | -                            |
| label               | 选中行后，哪个字段的值显示在input上                                                                             | string                      | -                            |
| rowKey              | 表格设置key，与table的key用法一样                                                                               | string                      | -                            |
| columns             | 列配置，与table的columns用法一样                                                                                | object[]                    | -                            |
| *method             | 请求方式,可选值：'post','get'                                                                                   | string                      | 'post'                       |
| *method             | 请求方式,可选值：'post','get'                                                                                   | string                      | 'post'                       |
| *value              | 默认值，传入对象数组，对象必须包含label和rowKey所指定的2个属性                                                  | object[]                    | []                           |
| *popupPlacement     | 下拉框对齐方式。可选值：'top','right','bottom','right','topLeft','topRight','bottomLeft','bottomRight'          | string                      | 'bottomRight'                |
| *popupStyle         | 下拉框样式                                                                                                      | object                      | { width:600, height:'auto' } |
| *placeholder        | 对应input                                                                                                       | string                      | -                            |
| *searchPlaceholder  | 对应搜索框                                                                                                      | string                      | -                            |
| *destroyPopupOnHide | 收起下拉时是否销毁                                                                                              | boolean                     | false                        |
| *disabled           | 禁用                                                                                                            | boolean                     | false                        |
| *tokenSeparators    | 分隔符                                                                                                          | string                      | ','                          |
| *extra              | 把react元素显示在左下脚，比如放一些按钮；<br/>如果值为function 会往该函数传入当前的所有选中项以便实现复杂的逻辑 | React.ReactNode 或 function | null                         |
| *params             | 异步请求url时 带入的额外入参                                                                                    | object[]                    | {}                           |
| *onConfirm          | 收起dropdown时，执行的回调函数，并往该函数传入当前选中项的所有数据                                              | function(selectedRows)      | ()=>{}                       |

更多[props](https://github.com/react-component/trigger)

## demo

与form结合使用

```jsx
import ComboGrid from '@/components/ComboGrid';

<Form.Item label="接收对象">
  {getFieldDecorator(
    'rcvObjNames',
    renderOptions({
      initialValue: [{ orgName: '四川省', orgId: 10008 }],
      rules: [{ required: true, message: '请选择' }],
    })
  )(
    <ComboGrid
      url="orgauth/OrganizationController/qryOperOrgGridData.do"
      popupStyle={{ width: 600 }}
      placeholder="请选择"
      searchPlaceholder="组织名称类型"
      destroyPopupOnHide
      label="orgName"
      rowKey="orgId"
      columns={[
        {
          title: '组织名称',
          dataIndex: 'orgName',
          ellipsis: true,
        },
        {
          title: '组织编码',
          dataIndex: 'orgCode',
          ellipsis: true,
        },
        {
          title: '组织类型',
          align: 'center',
          dataIndex: 'orgTypeName',
        },
      ]}
    />
  )}
</Form.Item>
```

