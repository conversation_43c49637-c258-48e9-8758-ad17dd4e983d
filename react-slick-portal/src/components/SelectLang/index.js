import React, { PureComponent } from 'react';
import { formatMessage, setLocale, getLocale } from 'umi-plugin-react/locale';
import { Menu, Icon, Popover } from 'antd';
import classNames from 'classnames';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';

export default class SelectLang extends PureComponent {
  changeLang = ({ key }) => {
    setLocale(key);
  };

  render() {
    const { className } = this.props;
    const selectedLang = getLocale();
    const locales = ['zh-CN', /* 'zh-TW', */ 'en-US' /* 'pt-BR' */];
    const languageLabels = {
      'zh-CN': '简体中文',
      //   'zh-TW': '繁体中文',
      'en-US': 'English',
      //   'pt-BR': 'Português',
    };
    const languageIcons = {
      'zh-CN': '🇨🇳',
      //   'zh-TW': '🇭🇰',
      'en-US': '🇬🇧',
      //   'pt-BR': '🇧🇷',
    };
    const langMenu = (
      <Menu className={styles.menu} selectedKeys={[selectedLang]} onClick={this.changeLang}>
        {locales.map(locale => (
          <Menu.Item key={locale}>
            <span role="img" aria-label={languageLabels[locale]}>
              {languageIcons[locale]}
            </span>
            {languageLabels[locale]}
          </Menu.Item>
        ))}
      </Menu>
    );
    return (
      <Popover
        placement="bottom"
        content={langMenu}
        trigger="hover"
        getPopupContainer={trigger => trigger.parentNode}
      >
        <span className={classNames(styles.dropDown, className)}>
          <Icon type="global" title={formatMessage({ id: 'navBar.lang' })} />
        </span>
      </Popover>
    );
  }
}
