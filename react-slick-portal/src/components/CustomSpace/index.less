/* 全局样式 */
:global {
  .custom-space,
  .custom-space--inline,
  .custom-space--inline-block {
    display: flex;
    align-items: center;

    &--vertical {
      flex-direction: column;
    }

    &--horizontal,
    &--inline,
    &--inline-block {
      flex-direction: row;
    }

    &--inline,
    &--inline-block {
      display: inline-flex;
    }

    &--wrap {
      flex-wrap: wrap;
    }

    &--center,
    &--align-center {
      justify-content: center;
    }

    &--align-start {
      justify-content: flex-start;
    }

    &--align-end {
      justify-content: flex-end;
    }

    &--align-center,
    &--align-middle {
      align-items: center;
    }

    &--align-baseline {
      align-items: baseline;
    }

    .custom-space-item {
      position: relative;
    }
  }
}
