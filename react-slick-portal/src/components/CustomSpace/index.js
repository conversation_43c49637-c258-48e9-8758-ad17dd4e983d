import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import './index.less';

const spaceEnum = {
  small: 4,
  middle: 8,
  large: 12,
};

const CustomSpace = props => {
  const {
    size,
    align,
    split,
    wrap,
    direction,
    style = {},
    className,
    children,
    ...restProps
  } = props;

  const sizeNumber = spaceEnum[size] || size || 0;

  const classes = classNames(
    'custom-space',
    {
      [`custom-space--align-${align}`]: align,
      [`custom-space--${direction}`]: direction,
      'custom-space--wrap': wrap,
    },
    className
  );

  const items = React.Children.map(children, child => {
    if (!child) {
      return null;
    }

    return React.cloneElement(child, {
      className: 'custom-space-item',
      style: {
        ...child.props.style,
        gap: sizeNumber,
      },
    });
  });

  const childNodes = [];

  items.forEach((item, index) => {
    childNodes.push(item);

    if (split && index < items.length - 1) {
      childNodes.push(
        React.cloneElement(split, { className: 'custom-space-item-split', key: `split-${index}` })
      );
    }
  });

  return (
    <div className={classes} style={style} {...restProps}>
      {[...childNodes]}
    </div>
  );
};

CustomSpace.propTypes = {
  size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  align: PropTypes.oneOf(['start', 'end', 'center', 'baseline']),
  split: PropTypes.node,
  wrap: PropTypes.bool,
  direction: PropTypes.oneOf(['horizontal', 'vertical', 'inline', 'inline-block']),
  style: PropTypes.object,
  className: PropTypes.string,
  children: PropTypes.node,
};
CustomSpace.defaultProps = {
  size: 8,
  align: 'center',
  split: '',
  wrap: false,
  direction: 'horizontal',
  style: {},
  className: '',
  children: null,
};

export default CustomSpace;
