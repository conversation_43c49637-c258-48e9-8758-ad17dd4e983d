import React from 'react';
import { Modal } from 'antd';
import { ErrorIcon, InfoIcon, SuccessIcon, WarnIcon } from './icons';
import style from './index.less';
// 默认配置
const defaultConfig = {
  duration: 60,
  placement: 'topRight',
};

// 自定义图标配置
const iconMap = {
  success: <SuccessIcon style={{ fontSize: 28, width: 28, height: 28 }} />,
  info: <InfoIcon style={{ fontSize: 28, width: 28, height: 28 }} />,
  warn: <WarnIcon style={{ fontSize: 28, width: 28, height: 28 }} />,
  error: <ErrorIcon style={{ fontSize: 28, width: 28, height: 28 }} />,
};

// 封装通知方法
const CustomModal = {
  success(config) {
    Modal.success({
      ...defaultConfig,
      ...config,
      icon: config.icon || iconMap.success,
      style: { ...(config.style || {}) },
      className: `${style.customModal} ${config.className || ''}`,
    });
  },

  error(config) {
    Modal.error({
      ...defaultConfig,
      ...config,
      icon: config.icon || iconMap.error,
      style: { ...(config.style || {}) },
      className: `${style.customModal} ${config.className || ''}`,
    });
  },

  info(config) {
    Modal.info({
      ...defaultConfig,
      ...config,
      icon: config.icon || iconMap.info,
      style: { ...(config.style || {}) },
      className: `${style.customModal} ${config.className || ''}`,
    });
  },

  warning(config) {
    Modal.warning({
      ...defaultConfig,
      ...config,
      icon: config.icon || iconMap.warn,
      style: { ...(config.style || {}) },
      className: `${style.customModal} ${config.className || ''}`,
    });
  },

  confirm(config) {
    return Modal.confirm({
      ...defaultConfig,
      ...config,
      icon: config.icon || iconMap.info,
      style: { ...(config.style || {}) },
      okButtonProps: {
        icon: 'check',
      },
      className: `${style.customModal} ${style.customModalConfirm} ${config.className || ''}`,
      okText: config.okText || '确定',
      cancelText: config.cancelText || '取消',
    });
  },
};

export default CustomModal;
