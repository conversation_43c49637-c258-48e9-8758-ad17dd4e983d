.customModal {
  font-family: <PERSON>Fang SC, Noto Sans SC;

  :global {
    .ant-modal-content {
      background: #FCFCFC;
      box-shadow: 0 8px 24px 8px rgba(0, 28, 56, 0.12) !important;
      border-radius: 12px !important;
    }

    .ant-modal-body {
      padding: 36px !important;
    }

    .ant-modal-confirm-body-wrapper {
      .ant-modal-confirm-body {
        > svg {
          float: left;
          margin-right: 24px !important;
        }

        .ant-modal-confirm-title {
          font-weight: 500;
          font-size: 20px;
          color: #333333;
          text-align: left;
        }

        .ant-modal-confirm-content {
          margin-left: 52px !important;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
        }
      }

      .ant-modal-confirm-btns {
        margin-top: 20px;
        text-align: right;
        float: right !important;
        margin-left: 54px !important;

        &::after {
          content: '';
          display: block;
          clear: both;
        }

        .ant-btn {
          font-size: 14px;
          color: #0085D0;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #0085D0;
          height: 40px;
        }

        .ant-btn-primary {
          float: left;
          margin-left: 0 !important;
          margin-right: 16px;
          color: #FFFFFF;
          background: #0085D0;
          border-radius: 4px;
          border: none;
          height: 40px;
        }
      }
    }
  }
}

/* 专门针对confirm类型的模态框样式 */
.customModalConfirm {
  /* 提高选择器优先级 */
  &.customModal {
    :global {
      .ant-modal-confirm-btns {
        text-align: left !important;
        float: none !important;
      }
    }
  }
}
