import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Alert } from 'antd';
import { IEVersion, isEmptyStr } from '@/utils/utils';

function WangEditor(props) {
  const {
    editor,
    setEditor,
    editorConfig,
    toolbarConfig,
    toolbarStyles = {},
    editorStyles = {},
  } = props;

  // 编辑器内容
  const [html, setHtml] = useState(null);
  const [wangeditor, setWangeditor] = useState(null);

  useLayoutEffect(() => {
    // 只有在非ie浏览器时，才引入wangeditor
    if (IEVersion() === -1) {
      import('@wangeditor/editor-for-react').then(_wangeditor => {
        setWangeditor(_wangeditor);
      });
      import('@wangeditor/editor/dist/css/style.css');
    }
    // return () => {
    //   if (wangeditor == null) return;
    //   wangeditor.destroy();
    //   setWangeditor(null);
    // };
  }, []);

  return (
    <>
      <div style={{ border: '1px solid #ccc', zIndex: 100 }}>
        {wangeditor && IEVersion() === -1 && (
          <>
            <wangeditor.Toolbar
              editor={editor}
              defaultConfig={toolbarConfig}
              mode="default"
              style={{ borderBottom: '1px solid #ccc', ...toolbarStyles }}
            />
            <wangeditor.Editor
              defaultConfig={editorConfig}
              value={html}
              onCreated={setEditor}
              onChange={newEditor => setHtml(newEditor.getHtml())}
              mode="default"
              style={editorStyles}
            />
          </>
        )}
        {IEVersion() !== -1 && <Alert message="提示：" description="本控件不支持IE浏览器，请更换其他浏览器" type="warning" showIcon />}
      </div>
    </>
  );
}

const isEqual = (prevProps, nextProps) => prevProps.editor === nextProps.editor;

export default React.memo(WangEditor, isEqual);
