@import '~antd/lib/style/themes/default.less';

.exception {
  width: 100%;
  height: 100%;

  .img {
    width: 100%;
    width: 430px;
    height: 360px;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: contain;
    margin-right: 50px;
  }

  .content {
    padding-top: 80px;
    h1 {
      margin-bottom: 24px;
      color: #434e59;
      font-weight: 600;
      font-size: 72px;
      line-height: 72px;
    }

    .desc {
      margin-bottom: 16px;
      color: @text-color-secondary;
      font-size: 20px;
      line-height: 28px;
    }

    .desc403 {
      font-weight: bold;
      font-size: 28px;
      color: #434e59;
    }

    .actions {
      button:not(:last-child) {
        margin-right: 8px;
      }
    }
  }
}

@media screen and (max-width: @screen-lg) {
  .exception {
    :global(.media-box) {
      width: 100% !important;
    }
    :global(.media-left) {
      float: none;
    }
    .img {
      margin: 0 auto;
      height: 320px;
    }
    .content {
      padding-top: 0;
      text-align: center;
    }
  }
}
