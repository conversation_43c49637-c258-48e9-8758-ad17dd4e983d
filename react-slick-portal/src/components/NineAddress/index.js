import React, { useState, useRef } from 'react';
import { Form, Input, Icon, Button, Row, Col, message } from 'antd';
import Trigger from 'rc-trigger';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
// import { queryNineAddress } from './services/index';
import styles from './index.less';

const FormItem = Form.Item;

const QueryCustManager = ({
  form,
  onChange,
  value,
  action,
  placeholder,
  searchPlaceholder,
  columns,
  rowKey,
  label,
  tokenSeparators,
  extra,
  pick,
  disabled,
  params,
  onConfirm,
  outsideOpen,
  setOutsideOpen,
  multipleFlag,
  suffix,
  currentCust,
  mode,
  custName,
  areaInfo,
  ...restProps
}) => {
  const { getFieldDecorator } = form;
  const [selectedRows, setSelectedRows] = useState(value);
  const [open, setOpen] = useState(false);
  const tableEl = useRef(null);
  const [listData, setListData] = useState([]);
  // const [dsArea, setDsArea] = useState([]); // 地市映射
  // const [qxArea, setQxArea] = useState([]); //  区县映射
  // const [town, setTown] = useState([]); //  乡镇映射
  const [isLoading, setIsLoading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [total, setTotal] = useState(0);

  // eslint-disable-next-line no-unused-vars
  const [formValues, setFormValues] = useState({
    searchText: '',
  });
  // 格式化值
  // eslint-disable-next-line no-shadow
  const getValue = (value, tokenSeparators, label) => {
    // 如果是数组，尝试获取第一个元素的 label 属性
    if (Array.isArray(value) && value.length > 0) {
      const firstItem = value[0];
      // 确保第一个元素有 label 属性
      if (firstItem && firstItem[label]) {
        return firstItem[label]; // 返回label的值
      }
    }
    return custName;
  };

  // 获取表格数据
  const getTableData = (areaInfoParams, fieldsValue = {}) =>
    request('portal/QueryLineAddressController/searchByLine.do', {
      method: 'post',
      data: {
        queryType: '8',
        regionName: areaInfoParams[0],
        countyId: areaInfoParams[1],
        townshipId: areaInfoParams[2],
        ...fieldsValue,
      },
    }).then(res => {
      setIsLoading(false);
      if (Array.isArray(res?.resultObject)) {
        setListData(res?.resultObject || []);
        setTotal(res?.resultObject.length || 0);
      }
    });

  const handleSearch = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      if (!areaInfo || areaInfo.length !== 3) {
        message.error('请先选择市区街');
      } else {
        setIsLoading(true);
        // console.log(areaInfo);
        
        getTableData(areaInfo, fieldsValue);
        setFormValues({ ...fieldsValue });
      }
    });
  };

  const handleReset = () => {
    form.resetFields();
    setFormValues({
      searchText: '',
    });
  };

  // 获取地市
  // const queryDS = async () => {
  //   const addressParams = {
  //     queryType: '1',
  //     regionName: '内蒙',
  //   };
  //   const resp = await queryNineAddress(addressParams);
  //   if (resp.resultCode === 'TRUE' && resp.resultObject.length !== 0) {
  //     setDsArea(resp.resultObject);
  //   }
  // };

  // 地市值变化
  // const handleDs = async e => {
  //   const addressParams = {
  //     queryType: '2',
  //     regionName: e,
  //   };
  //   const resp = await queryNineAddress(addressParams);
  //   if (resp.resultCode === 'TRUE' && resp.resultObject.length !== 0) {
  //     setQxArea(resp.resultObject);
  //     const formParams = {
  //       countyId: undefined,
  //       townshipId: undefined,
  //       searchText: undefined,
  //     };
  //     form.setFieldsValue(formParams);
  //   }
  // };

  // 区县值变化
  // const handleQx = async e => {
  //   const addressParams = {
  //     queryType: '3',
  //     countyId: e,
  //   };
  //   const resp = await queryNineAddress(addressParams);
  //   if (resp.resultCode === 'TRUE' && resp.resultObject.length !== 0) {
  //     setTown(resp.resultObject);
  //     const formParams = {
  //       townshipId: undefined,
  //       searchText: undefined,
  //     };
  //     form.setFieldsValue(formParams);
  //   }
  // };


  // useEffect(() => {
  //   queryDS();
  // }, []);

  return (
    <div>
      <Trigger
        className={styles.queryCustManager}
        action={[action]}
        popupAlign={{ overflow: { adjustX: true, adjustY: true }, offset: [0, 4] }}
        getPopupContainer={trigger => trigger.parentNode}
        onPopupVisibleChange={isVisible => {
          if (!isVisible) {
            onConfirm(selectedRows);
          }
          if (typeof setOutsideOpen === 'function' && isVisible) {
            setOutsideOpen(true);
          }
          setOpen(isVisible);
        }}
        mouseEnterDelay={0.3}
        builtinPlacements={{
          left: {
            points: ['cr', 'cl'],
          },
          right: {
            points: ['cl', 'cr'],
          },
          top: {
            points: ['bc', 'tc'],
          },
          bottom: {
            points: ['tc', 'bc'],
          },
          topLeft: {
            points: ['bl', 'tl'],
          },
          topRight: {
            points: ['br', 'tr'],
          },
          bottomRight: {
            points: ['tr', 'br'],
          },
          bottomLeft: {
            points: ['tl', 'bl'],
          },
        }}
        popupClassName={styles.queryCustManagerDropdown}
        popupVisible={open && outsideOpen}
        popup={(
          <>
            <Form className={styles.searchForm}>
              <Row>
                {/* <Col span={6} className={styles.formItemCol}>
                  <FormItem label="地市" className={styles.formItem}>
                    {getFieldDecorator('regionName', {
                      rules: [{ required: true, message: '地市不能为空!' }],
                    })(
                      <Select placeholder="请选择" onChange={e => handleDs(e)} className={styles.inputStyle}>
                        {dsArea?.map(e => (
                          <Select.Option value={e.regionId} key={e.regionId}>
                            {e.regionName}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </FormItem>
                </Col>
                <Col span={6} className={styles.formItemCol}>
                  <FormItem label="区县" className={styles.formItem}>
                    {getFieldDecorator('countyId', {
                      rules: [{ required: true, message: '区县不能为空!' }],
                    })(
                      <Select placeholder="请选择" onChange={e => handleQx(e)} className={styles.inputStyle}>
                        {qxArea?.map(e => (
                          <Select.Option value={e.countyId} key={e.countyId}>
                            {e.countyName}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </FormItem>
                </Col>
                <Col span={6} className={styles.formItemCol}>
                  <FormItem label="乡镇" className={styles.formItem}>
                    {getFieldDecorator('townshipId', {
                      rules: [{ required: true, message: '乡镇不能为空!' }],
                    })(
                      <Select placeholder="请选择" className={styles.inputStyle}>
                        {town?.map(e => (
                          <Select.Option value={e.townshipId} key={e.townshipId}>
                            {e.townshipName}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </FormItem>
                </Col> */}
                <Col span={24} className={styles.formItemCol}>
                  <FormItem label="详细地址" className={styles.formItem}>
                    {getFieldDecorator('searchText', {
                      rules: [{ required: true, message: '详细地址不能为空!' }],
                    })(
                      <Input placeholder="请输入" className={styles.inputStyle} />
                    )}
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col className="text-right">
                  <Button type="primary" onClick={handleSearch}>
                    查询
                  </Button>
                  <Button onClick={handleReset}>重置</Button>
                </Col>
              </Row>
            </Form>
            <SlickTable
              loading={isLoading}
              ref={tableEl}
              extra={extra}
              rowKey={rowKey}
              pick={pick}
              style={{ marginTop: 8 }}
              data={{ list: listData }}
              pagination={{
                pageSize: 5,
                showQuickJumper: true,
                showSizeChanger: true,
                // eslint-disable-next-line no-shadow
                showTotal: total => `共 ${total} 条记录`,
              }}
              selectedRowKeys={Array.isArray(selectedRows) && selectedRows?.map(row => row[rowKey])}
              onSelectRow={rows => {
                setSelectedRows(rows);
                if (!multipleFlag) {
                  onConfirm(rows);
                  setOpen(false);
                }
              }}
              columns={columns}
            />
          </>
        )}
        {...restProps}
      >
        <Input
          style={{ width: '80%' }}
          placeholder={placeholder}
          disabled={disabled}
          suffix={suffix || <Icon type="table" />}
          value={getValue(selectedRows, tokenSeparators, label)}
        />
      </Trigger>
    </div>
  );
};

QueryCustManager.defaultProps = {
  value: [],
  method: 'post',
  placeholder: '',
  searchPlaceholder: '',
  action: 'click',
  stretch: 'width',
  popupStyle: {},
  popupPlacement: 'bottomLeft',
  tokenSeparators: ',',
  destroyPopupOnHide: false,
  disabled: false,
  extra: null,
  pick: 'checkbox',
  params: {},
  outsideOpen: true,
  onConfirm: () => {},
};

export default Form.create()(QueryCustManager);
