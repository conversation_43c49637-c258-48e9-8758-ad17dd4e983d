import { Input, Radio } from 'antd';
import React from 'react';
import style from './index.less';

/**
 * 金额选择器组件
 * 包含输入框和快速选择按钮
 * @param {any} value - 当前选中的金额值
 * @param {function} onChange - 金额变更回调函数
 */
const AmountSelector = ({ value, onChange }) => {
  // 预设金额选项配置
  const amounts = ['20', '50', '100', '200'];
  const handleChange = e => {
    let inputValue = e.target.value;
    // 使用正则表达式确保只允许输入整数
    inputValue = inputValue.replace(/[^\d]/g, '');
    // 触发 props 中的 onChange 方法
    if (onChange) {
      onChange(inputValue);
    }
  };
  return (
    <div className={style.moneySelectContainer}>
      <Input addonAfter="元" value={value} style={{ width: '100%' }} onChange={handleChange} placeholder="请输入金额" />
      <Radio.Group value={value} onChange={e => onChange && onChange(e.target.value)} className="moneySelectGroup">
        {amounts.map(amount => (
          <Radio.Button key={amount} value={amount} className="money-select-button">
            {amount}元
          </Radio.Button>
        ))}
      </Radio.Group>
    </div>
  );
};

export default AmountSelector;
