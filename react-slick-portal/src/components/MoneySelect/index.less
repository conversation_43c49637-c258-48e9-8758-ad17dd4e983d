.moneySelectContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;

}

:global(.ant-radio-group.moneySelectGroup) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  :global {
    .ant-radio-button-wrapper {
      position: relative;
      text-align: center;
      height: 24px;
      min-width: 60px;
      background: #FFFFFF;
      border-radius: 3px;
      border: 1px solid #E6E6E6;
      color: #333333;
      overflow: hidden;
      box-shadow: none;


      &:before {
        display: none;
      }

      &:after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 16px 16px 0;
        border-color: transparent #1890ff transparent transparent;
        display: none;
      }

      &.ant-radio-button-wrapper-checked {
        background: #F0FDFF;
        border-radius: 3px;
        border: 1px solid #0085D0;
        color: #0085D0;
        box-shadow: none;

        &:hover {
          color: #0085D0;
        }

        &:after {
          display: block;
        }
      }
    }
  }

}
