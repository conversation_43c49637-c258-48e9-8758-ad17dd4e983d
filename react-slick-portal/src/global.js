/* eslint-disable */
import 'intl';
import { message } from 'antd';
import 'resize-observer-polyfill';
import { IEVersion, getItem, isJSON } from '@/utils/utils';
import logActionConsts from '@/utils/logActionConsts';
import find from 'lodash/find';
import isObject from 'lodash/isObject';

/**
 * 适配IE兼容
 */
if ([8, 9, 10, 11].indexOf(IEVersion()) !== -1) {
  document.getElementsByTagName('html')[0].setAttribute('class', `ie${IEVersion()}`);
}

require('raf').polyfill(window);

// if (!sessionStorage.length) {
//   // 这个调用能触发目标事件，从而达到共享数据的目的
//   localStorage.setItem('getSessionStorage', Date.now());
// }

// 该事件是核心
// window.addEventListener('storage', function(event) {
//   if (event.key === 'getSessionStorage') {
//     // 已存在的标签页会收到这个事件
//     localStorage.setItem('sessionStorage', JSON.stringify(sessionStorage));
//     localStorage.removeItem('sessionStorage');
//   } else if (event.key === 'sessionStorage' && !sessionStorage.length) {
//     // 新开启的标签页会收到这个事件
//     const data = JSON.parse(event.newValue);

//     // eslint-disable-next-line no-restricted-syntax
//     // for (const key in data) {
//     //   if (Object.prototype.hasOwnProperty.call(data, key)) {
//     //     sessionStorage.setItem(key, data[key]);
//     //   }
//     // }
//   }
// });
