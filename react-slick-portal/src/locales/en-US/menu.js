export default {
  'menu.home': 'Home',
  'menu.dashboard.analysis': 'Analysis',
  'menu.dashboard.monitor': 'Monitor',
  'menu.dashboard.workplace': 'Workplace',
  'menu.form.basicform': 'Basic Form',
  'menu.form.stepform': 'Step Form',
  'menu.form.stepform.info': 'Step Form(write transfer information)',
  'menu.form.stepform.confirm': 'Step Form(confirm transfer information)',
  'menu.form.stepform.result': 'Step Form(finished)',
  'menu.form.advancedform': 'Advanced Form',
  'menu.list': 'List',
  'menu.list.searchtable': 'Search Table',
  'menu.list.basiclist': 'Basic List',
  'menu.list.cardlist': 'Card List',
  'menu.list.searchlist': 'Search List',
  'menu.list.searchlist.articles': 'Search List(articles)',
  'menu.list.searchlist.projects': 'Search List(projects)',
  'menu.list.searchlist.applications': 'Search List(applications)',
  'menu.profile': 'Profile',
  'menu.profile.basic': 'Basic Profile',
  'menu.profile.advanced': 'Advanced Profile',
  'menu.result': 'Result',
  'menu.result.success': 'Success',
  'menu.result.fail': 'Fail',
  'menu.exception': 'Exception',
  'menu.exception.not-permission': '403',
  'menu.exception.not-find': '404',
  'menu.exception.server-error': '500',
  'menu.exception.trigger': 'Trigger',
  'menu.account': 'Account',
  'menu.account.center': 'Account Center',
  'menu.account.settings': 'Account Settings',
  'menu.account.trigger': 'Trigger Error',
  'menu.account.logout': 'Logout',

  'menu.menus': 'Menu Manage',
  'menu.form': 'Form',
  'menu.tabs': 'Tabs',
  'menu.form.inputs': 'Inputs',
  'menu.dashboard': 'Dashboard',
  'menu.charts': 'Charts',
  'menu.buttons': 'Button',
  'menu.icons': 'Icon',
  'menu.dropdown': 'Dropdown',
  'menu.modal': 'Modal',
  'menu.pop': 'Pop',
  'menu.tables': 'Tables',
  'menu.tables.searchTable': 'Search Table',
  'menu.tables.advancedSearchTable': 'Advanced Search Table',
  'menu.tables.style': 'Other Style',
  'menu.cards': 'Cards',
  'menu.other': 'Other',
  'menu.demo': 'Demo',
  'menu.demo.clover': 'Clover',
  'menu.demo.clover.basic': 'Basic Clover',
  'menu.demo.clover.advanced': 'Advanced Clover',
  'menu.demo.iframe': 'iframe',
  'menu.demo.pureComponent': 'pureComponent',
  'menu.demo.sceneSwitch': 'Scene Switch(dva)',
  'menu.demo.sceneSwitch2': 'Scene Switch(StepWizard)',
  'menu.demo.curd': 'curd',
  'menu.demo.upload': 'About Upload',
  'menu.welcome': 'Welcome',
  'menu.map': 'Map',
  'menu.map.index': 'City Map',
  'menu.map.search': 'Map Search',
  'menu.graph': 'UML',
  'menu.editor': 'Editor',
  'menu.editor.flow': 'Flow Editor',
  'menu.editor.mind': 'Mind Editor',
  'menu.editor.koni': 'Koni Editor',
};
