import React from 'react';
import { notification, ConfigProvider } from 'antd';
import { formatMessage } from 'umi/locale';

const { themeConfig } = require('./defaultSettings.js');

// export const dva = {
//   config: {
//     onError(err) {
//       err.preventDefault();
//       notification.error({
//         message: '请求错误',
//         description: err.message,
//       });
//     },
//   },
// };

// window.addEventListener('unhandledrejection', function(event) {
//   event.preventDefault();
// });

export function patchRoutes(routes) {
  window.g_routes = routes;
}

export function render(oldRender) {
  oldRender();
}

export function rootContainer(container) {
  return <>{container}</>;
}
