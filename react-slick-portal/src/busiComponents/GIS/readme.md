```javascript
import React, { useState } from 'react';
import { Card, Button, Descriptions } from 'antd';
import EnterpriseTwelveAddressChoose from '../components/EnterpriseTwelveAddressChoose';

const AddressChoosePage = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState(null);

  // GIS地图和访问号配置
  const params = JSON.stringify({
    GIS_URL: 'https://example.com/gis-map',
    ACCESS_NUM: '12345678'
  });

  // 地址选择回调函数
  const handleAddressSelect = (addressData) => {
    console.log('选中的地址信息：', addressData);
    setSelectedAddress(addressData);
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="地址选择示例">
        <Button type="primary" onClick={() => setModalVisible(true)}>
          选择地址
        </Button>

        {selectedAddress && (
          <Descriptions title="已选择地址信息" bordered style={{ marginTop: 20 }}>
            <Descriptions.Item label="城市">{selectedAddress.city}</Descriptions.Item>
            <Descriptions.Item label="区县">{selectedAddress.county}</Descriptions.Item>
            <Descriptions.Item label="乡镇">{selectedAddress.town}</Descriptions.Item>
            <Descriptions.Item label="社区">{selectedAddress.communityName}</Descriptions.Item>
            <Descriptions.Item label="道路">{selectedAddress.road}</Descriptions.Item>
            <Descriptions.Item label="门牌号">{selectedAddress.roadNumber}</Descriptions.Item>
            <Descriptions.Item label="小区">{selectedAddress.villages}</Descriptions.Item>
            <Descriptions.Item label="楼栋">{selectedAddress.building}</Descriptions.Item>
            <Descriptions.Item label="单元">{selectedAddress.unitNo}</Descriptions.Item>
            <Descriptions.Item label="楼层">{selectedAddress.floorNo}</Descriptions.Item>
            <Descriptions.Item label="房间号">{selectedAddress.roomNo}</Descriptions.Item>
            <Descriptions.Item label="覆盖程度">{selectedAddress.coverState || '无覆盖'}</Descriptions.Item>
          </Descriptions>
        )}

        <EnterpriseTwelveAddressChoose
          id="addressChoose"
          busiMapping="address_mapping"
          params={params}
          onAddressSelect={handleAddressSelect}
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
        />
      </Card>
    </div>
  );
};

export default AddressChoosePage;
```
