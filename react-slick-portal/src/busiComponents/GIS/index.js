import React, { useState, useEffect, useRef } from 'react';
import { Button, Select, Input, Modal, Table, Radio, message, Spin, Empty, Card, Row, Col } from 'antd';
import axios from 'axios';

const { Option } = Select;

const EnterpriseTwelveAddressChoose = props => {
  // 基础配置
  const { params = {}, onAddressSelect, visible, onCancel } = props;

  // 状态管理
  const [citys, setCitys] = useState([]);
  const [areas, setAreas] = useState([]);
  const [countys, setCountys] = useState([]);
  const [addressList, setAddressList] = useState([]);
  const [selectedCity, setSelectedCity] = useState('');
  const [selectedArea, setSelectedArea] = useState('');
  const [selectedCounty, setSelectedCounty] = useState('');
  const [searchParam, setSearchParam] = useState('');
  const [loading, setLoading] = useState(false);
  const [coverModalVisible, setCoverModalVisible] = useState(false);
  const [coverData, setCoverData] = useState([]);
  const [selectedCoverRow, setSelectedCoverRow] = useState(null);
  const [selectedAddressData, setSelectedAddressData] = useState(null);
  const [gisMapLoading, setGisMapLoading] = useState(false);
  const [gisIframeVisible, setGisIframeVisible] = useState(false);

  // GIS地图相关
  const gisMapUrl = params.GIS_URL || '';
  const accessNum = params.ACCESS_NUM || '';
  const [timestamp, setTimestamp] = useState('');
  const gisQueryTimerRef = useRef(null);
  const iframeRef = useRef(null);

  // 处理程序名称
  const twelveAddresshandler = 'com.ai.baseframe.saas.handler.EnterpriseTwelveAddressHandler';

  // 初始化
  useEffect(() => {
    if (visible) {
      console.log('EnterpriseTwelveAddressChoose.init');
      console.log(params);
      // eslint-disable-next-line no-use-before-define
      initAddress('内蒙古', '', 'CITY_NAME');
    }

    return () => {
      // 清理定时器
      if (gisQueryTimerRef.current) clearTimeout(gisQueryTimerRef.current);
    };
  }, [visible]);

  // 初始化地址
  const initAddress = (upAddress, upCol, lowCol) => {
    setLoading(true);
    axios
      .post('/api/address/initAddress', {
        UP_ADDRESS: upAddress,
        UP_COL: upCol,
        LOW_COL: lowCol,
      })
      .then(response => {
        const { data } = response;
        if (lowCol === 'CITY_NAME') {
          setCitys(data.districts || []);
        } else if (lowCol === 'AREA_NAME') {
          setAreas(data.districts || []);
        } else if (lowCol === 'COUNTY_NAME') {
          setCountys(data.districts || []);
        }
        setLoading(false);
      })
      .catch(error => {
        message.error(`加载地址异常：${error.message || '未知错误'}`);
        setLoading(false);
      });
  };

  // 查询地址
  const queryAddress = () => {
    if (!selectedCity || !selectedArea || !selectedCounty) {
      message.warning('请选择地市区县乡镇后再进行查询！');
      return;
    }

    if (!searchParam) {
      message.warning('请输入要查询的地址！');
      return;
    }

    setLoading(true);
    axios
      .post('/api/address/queryAddress', {
        CITY: selectedCity,
        AREA: selectedArea,
        COUNTY: selectedCounty,
        ADDPARAM: searchParam,
      })
      .then(response => {
        const { data } = response;
        setAddressList(data.addressInfo || []);

        if (data.total > 1000) {
          message.warning('查询到的地址数大于1000，请输入更精确查询条件！');
        }

        setLoading(false);
      })
      .catch(error => {
        message.error(`加载地址异常：${error.message || '未知错误'}`);
        setLoading(false);
      });
  };

  // 选择地址
  const selectAddr = (addressId, addressData) => {
    setSelectedAddressData(addressData);
    setCoverData([]);

    setLoading(true);
    axios
      .post('/api/handler', {
        handler: twelveAddresshandler,
        method: 'getCoverBoxByAddressId',
        params: {
          ADDRESS_ID: addressId,
        },
      })
      .then(response => {
        const { data } = response;
        setLoading(false);

        if (data.total > 0) {
          const coverInfo = data.coverAddressInfo.map((item, index) => ({
            key: index,
            COVER_STATE: item.coverState,
            COVER_SCENE: item.coverScene,
            DEVICE_INFO: item.deviceInfo,
          }));
          setCoverData(coverInfo);
          setCoverModalVisible(true);
        } else {
          Modal.confirm({
            title: '确认信息',
            content: '当前地址无资源覆盖程度',
            onOk: () => {
              addressData.coverState = '无覆盖';
              // eslint-disable-next-line no-use-before-define
              afterAddressChoose(addressData);
            },
          });
        }
      })
      .catch(error => {
        setLoading(false);
        message.error(`查询覆盖程度失败：${error.message || '未知错误'}`);
      });
  };

  // 提交覆盖程度选择
  const submitCoverSelection = () => {
    if (!selectedCoverRow) {
      message.warning('请选择一个覆盖程度');
      return;
    }

    if (selectedAddressData) {
      selectedAddressData.coverState = selectedCoverRow.COVER_STATE;
      // eslint-disable-next-line no-use-before-define
      afterAddressChoose(selectedAddressData);
      setCoverModalVisible(false);
    }
  };

  // 地址选择回调
  const afterAddressChoose = addressData => {
    if (typeof onAddressSelect === 'function') {
      onAddressSelect(addressData);
      onCancel(); // 选择完地址后关闭弹窗
    } else {
      message.warning('未提供地址选择回调函数！');
    }
  };

  // 打开GIS地图 - 使用iframe
  const openGisMap = () => {
    const newTimestamp = new Date().getTime();
    setTimestamp(newTimestamp);
    // const gisUrl = `${gisMapUrl}&accessnum=${accessNum}&timestamp=${encodeURIComponent(newTimestamp)}`;

    // 显示iframe
    setGisIframeVisible(true);
    setGisMapLoading(true);

    // 开始轮询查询地址信息
    // eslint-disable-next-line no-use-before-define
    queryGisAddrInfo();
  };

  // 查询GIS地址信息
  const queryGisAddrInfo = () => {
    axios
      .post('/api/handler', {
        handler: twelveAddresshandler,
        method: 'queryGisAddrInfo',
        params: {
          ACCESS_NUM: accessNum,
          TIMESTAMP: timestamp,
        },
      })
      .then(response => {
        const { data } = response;
        console.log(data);

        if (data.OUT_DATA && data.OUT_DATA.length > 0) {
          const gisAddr = data.OUT_DATA[0];
          console.log(gisAddr);

          const addressLevel = gisAddr.ADDRESS_LEVEL;
          if (parseInt(addressLevel, 10) <= 6) {
            message.warning('专线至少需要选择到6级地址才允许安装，请重新选择！');
          } else {
            setGisMapLoading(false);
            setGisIframeVisible(false);
            // eslint-disable-next-line no-use-before-define
            setAddress(gisAddr);
          }
        } else {
          // 继续轮询
          gisQueryTimerRef.current = setTimeout(() => {
            queryGisAddrInfo();
          }, 1000);
        }
      })
      .catch(() => {
        setGisMapLoading(false);
        setGisIframeVisible(false);
        message.error('查询GIS选址信息失败，请重试！');
      });
  };

  // 设置地址
  const setAddress = addrData => {
    const addressFullName = addrData.ADDRESS_FULL_NAME;
    const split = addressFullName.split('|');

    const data = {
      city: split.length > 1 ? split[1] : '',
      county: split.length > 2 ? split[2] : '',
      town: split.length > 3 ? split[3] : '',
      communityName: split.length > 4 ? split[4] : '',
      road: split.length > 5 ? split[5] : '',
      roadNumber: split.length > 6 ? split[6] : '',
      villages: split.length > 7 ? split[7] : '',
      building: split.length > 8 ? split[8] : '',
      unitNo: split.length > 9 ? split[9] : '',
      floorNo: split.length > 10 ? split[10] : '',
      roomNo: split.length > 11 ? split[11] : '',
    };

    afterAddressChoose(data);
  };

  // 关闭GIS iframe
  const closeGisIframe = () => {
    if (gisQueryTimerRef.current) {
      clearTimeout(gisQueryTimerRef.current);
    }
    setGisIframeVisible(false);
    setGisMapLoading(false);
  };

  // 表格列配置
  const coverColumns = [
    {
      title: '选择',
      dataIndex: 'select',
      key: 'select',
      render: (_, record) => <Radio checked={selectedCoverRow && selectedCoverRow.key === record.key} onChange={() => setSelectedCoverRow(record)} />,
    },
    {
      title: '资源覆盖程度',
      dataIndex: 'COVER_STATE',
      key: 'coverState',
    },
    {
      title: '覆盖场景',
      dataIndex: 'COVER_SCENE',
      key: 'coverScene',
    },
    {
      title: '设备信息',
      dataIndex: 'DEVICE_INFO',
      key: 'deviceInfo',
    },
  ];

  return (
    <Modal title="地址选择" visible={visible} onCancel={onCancel} width={900} footer={null} destroyOnClose>
      <Spin spinning={loading}>
        <Card>
          <Row type="flex" gutter={16}>
            <Col span={6}>
              <Select
                placeholder="选择城市"
                style={{ width: '100%' }}
                value={selectedCity || undefined}
                onChange={value => {
                  setSelectedCity(value);
                  setSelectedArea('');
                  setSelectedCounty('');
                  initAddress(value, 'CITY_NAME', 'AREA_NAME');
                }}
              >
                {citys.map(city => (
                  <Option key={city.code} value={city.name}>
                    {city.name}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Select
                placeholder="选择区域"
                style={{ width: '100%' }}
                value={selectedArea || undefined}
                onChange={value => {
                  setSelectedArea(value);
                  setSelectedCounty('');
                  initAddress(value, 'AREA_NAME', 'COUNTY_NAME');
                }}
              >
                {areas.map(area => (
                  <Option key={area.code} value={area.name}>
                    {area.name}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Select
                placeholder="选择县区"
                style={{ width: '100%' }}
                value={selectedCounty || undefined}
                onChange={value => {
                  setSelectedCounty(value);
                }}
              >
                {countys.map(county => (
                  <Option key={county.code} value={county.name}>
                    {county.name}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Button type="primary" icon="environment" onClick={openGisMap} loading={gisMapLoading}>
                GIS地图选址
              </Button>
            </Col>
          </Row>

          <Row type="flex" style={{ marginTop: 16 }}>
            <Col span={18}>
              <Input.Search
                placeholder="请输入地址关键字"
                value={searchParam}
                onChange={e => setSearchParam(e.target.value)}
                onSearch={queryAddress}
                enterButton
              />
            </Col>
            <Col span={6} style={{ textAlign: 'right' }}>
              <Button type="primary" onClick={queryAddress}>
                查询
              </Button>
            </Col>
          </Row>

          <div style={{ marginTop: 16, maxHeight: 400, overflow: 'auto' }}>
            {addressList.length > 0 ? (
              <div>
                {addressList.map((address, index) => (
                  <Card
                    key={address.addressId || index}
                    size="small"
                    style={{ marginBottom: 8, cursor: 'pointer' }}
                    onClick={() => selectAddr(address.addressId, address)}
                  >
                    <div>{address.addressName}</div>
                  </Card>
                ))}
              </div>
            ) : (
              <Empty description="暂无数据，请重新输入查询条件" />
            )}
          </div>
        </Card>
      </Spin>

      {/* GIS地图iframe弹窗 */}
      <Modal
        title="GIS地图选址"
        visible={gisIframeVisible}
        onCancel={closeGisIframe}
        footer={[
          <Button key="close" onClick={closeGisIframe}>
            关闭
          </Button>,
        ]}
        width={1000}
        destroyOnClose
      >
        <Spin spinning={gisMapLoading} tip="正在查询选中地址...">
          <iframe
            title="gis"
            ref={iframeRef}
            src={gisMapUrl ? `${gisMapUrl}&accessnum=${accessNum}&timestamp=${timestamp}` : ''}
            style={{ width: '100%', height: '600px', border: 'none' }}
          />
        </Spin>
      </Modal>

      {/* 覆盖程度选择弹窗 */}
      <Modal
        title="地址资源覆盖程度"
        visible={coverModalVisible}
        onCancel={() => setCoverModalVisible(false)}
        onOk={submitCoverSelection}
        width={800}
      >
        <Table
          columns={coverColumns}
          dataSource={coverData}
          pagination={false}
          rowKey="key"
          rowSelection={{
            type: 'radio',
            selectedRowKeys: selectedCoverRow ? [selectedCoverRow.key] : [],
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedCoverRow(selectedRows[0]);
            },
          }}
        />
      </Modal>
    </Modal>
  );
};

// 添加默认props
EnterpriseTwelveAddressChoose.defaultProps = {
  onAddressSelect: null,
  visible: false,
  onCancel: () => {},
};

export default EnterpriseTwelveAddressChoose;
