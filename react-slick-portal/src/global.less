@import '../config/theme/variables.less';
@import '~antd/lib/style/themes/default.less';
@import './less/card.less';
@import './less/tab.less';
@import './less/descriptions.less';
@import './less/collapse.less';
@import './less/notification.less';
@import './less/modal.less';
@import './less/form.less';
@import './less/table.less';
@import './less/middleItem.less';
@import './less/mediaBox.less';
@import './less/utils.less';
@import './less/animate.less';
@import '../config/theme/bss/index.less';
@import './less/message.less';
@import './fix-ie11.less';
@import './fix-ie10.less';
@import './fix-ie9.less';

@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 500;
  src: local(''),
  url('./assets/fonts/NotoSansSC-Regular.ttf') format('ttf');
}
@font-face {
  font-family: 'HarmonyOS Sans SC';
  font-style: normal;
  font-weight: 500;
  src: local(''),
  url('./assets/fonts/HarmonyOS_Sans_Regular.ttf') format('ttf');
}

body {
  background-color: #F6F6F6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';

  .yx-chat-window.in  {
    margin-top: 48px;

    &.screen {
      top: 0;
      margin-top: 0px;
    }
  }
}

/* 重置滚动条样式 BEGIN */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
}
::-webkit-scrollbar-thumb {
  background-color: #999;
  border-radius: 3px;
}

/* 滚动条 END */

/* UTILS CSS BEGIN */

.pull-left {
  float: left;
}
.pull-right {
  float: right;
}
.inline-block {
  display: inline-block !important;
  vertical-align: middle;
}
.block {
  display: block !important;
}
.line {
  display: inline-block;
  width: 6px;
  height: 14px;
  margin-right: @slick-space-base;
  vertical-align: middle;
  background-color: @blue-6;
  border-radius: 2px;
}
.hide {
  display: none !important;
}
.text-middle {
  vertical-align: middle;
}
.text-left {
  text-align: left !important;
}
.text-center {
  text-align: center !important;
}
.text-right {
  text-align: right !important;
}
.text-warning {
  color: @warning-color !important;
}
.text-danger {
  color: @error-color !important;
}
.text-success {
  color: @success-color !important;
}
.text-primary {
  color: @primary-color !important;
}
.text-color {
  color: @text-color !important;
}
.text-gray {
  color: @text-color-secondary !important;
}
.text-ellipsis {
  display: inline-block;
  width: 80%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.text-info {
  color: @info-color !important;
}
.bold {
  font-weight: bold;
}
.text-lg {
  font-size: @font-size-base * 1.5 !important;
}
.pointer {
  cursor: pointer !important;
}
.clearfix {
  .clearfix();
}
.noScroll {
  overflow: hidden !important;
}
// margin and padding utils
.padding-0 {
  padding: 0 !important;
}
.margin-0 {
  margin: 0 !important;
}
.gutterUtils(margin; @slick-space-base;);
.gutterUtils(padding; @slick-space-base;);

/* UTILS CSS END */

.tableList {
  .tableListOperator {
    margin-bottom: 16px;
    button {
      margin-right: 8px;
    }
  }
}

@media screen and (max-width: @screen-lg) {
  .tableListForm :global(.ant-form-item) {
    margin-right: 24px;
  }
}

@media screen and (max-width: @screen-md) {
  .tableListForm :global(.ant-form-item) {
    margin-right: 8px;
  }
}

/**
  *******************************
    重置ANTD CSS  BEGIN
  *******************************
**/

/* form开始 */
.bss-form-inline .ant-form-item {
  display: table;
  width: 100%;
}
.bss-form-inline .ant-form-item-label {
  display: table-cell;
  width: 1%;
}
.bss-form-inline .ant-form-item-control-wrapper {
  display: table-cell;
  width: 99%;
}

/* form结束 */

/* card开始 */
// .ant-card-extra {
//   padding: 12px 0 !important;
//   line-height: 32px !important;
// }

.ant-card + .ant-card {
  margin-top: @slick-space-base;
}

/* tab开始 */

.blockUI.blockPage,
.blockUI.blockElement {
  top: 40%;
  left: 50%;
  padding: 6px 12px;
  background-color: #646464;
  border-radius: 4px;
}

.blockUI.blockPage {
  background: url(./assets/loading.gif) 10px 8px no-repeat;
}
.blockUI.blockPage > .blockUI-content,
.blockUI.blockElement > .blockUI-content {
  padding-left: 12px;
  color: #fff;
  font-size: large;
}

/* ant-table react-resizable */
.ant-table .react-resizable {
  position: relative;
}

.ant-table .react-resizable-handle {
  position: absolute;
  right: -5px;
  bottom: 0;
  width: 10px;
  height: 100%;
  cursor: col-resize;
}

/* antd table 结束 */

/* 修复附件上传 数量超过一行时 upload 错位 BEGIN */
.ant-upload-list-picture-card {
  float: none !important;
}


/* END */

/* 优化 使Table的水平滚动条仅在过长时显示 BEGIN */
.ant-table-scroll {
  > .ant-table-body {
    // overflow-x: auto !important;
  }
}

.ant-table-row > td {
  padding: 4px 8px !important;
  line-height: 24px;
}

/* END */

.gutter {
  background-color: #eee;
  background-repeat: no-repeat;
  background-position: 50%;
}
.gutter.gutter-vertical {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFAQMAAABo7865AAAABlBMVEVHcEzMzMzyAv2sAAAAAXRSTlMAQObYZgAAABBJREFUeF5jOAMEEAIEEFwAn3kMwcB6I2AAAAAASUVORK5CYII=');
}

.gutter.gutter-horizontal {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==');
}
.split {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.split,
.gutter.gutter-horizontal {
  float: left;
}

.anchorBL {
  display: none;
}

/* fdx dynamicAttribute component BEGIN */

.fdx-form {
  .dynamic-form-item {
    width: 100%;
  }
  .hiddenItem {
    display: none !important;
  }
  .readOnlyItem {
    textarea {
      height: 32px;
    }
    > div:first-child > label {
      &::before,
      &::after {
        visibility: hidden;
      }
    }
    > div:last-child {
      > div > span {
        display: block;
        width: 100%;
        border-bottom: 1px solid #d9d9d9;
        > div {
          width: 100%;
          border: none;
        }
      }
      input[disabled] {
        color: #848484;
        background: none;
        border: none;
        border-radius: 0;
        cursor: text;
      }
      input,
      textarea,
      select {
        &[readonly],
        &[readonly]:focus,
        &[readonly]:hover {
          border: none;
          border-right-width: 0 !important;
          border-radius: 0;
          -webkit-box-shadow: none;
          box-shadow: none;
          resize: none;
        }
      }
      .dynamic-input-number input {
        &[readonly],
        &[readonly]:focus,
        &[readonly]:hover {
          border: none;
        }
      }
      .dynamic-select > div,
      .dynamic-select > span {
        color: #848484;
        background: #fff;
        border: none;
        border-radius: 0;
        cursor: text;
        [role='menuitem'] {
          color: #848484;
          background: #fff;
          border: none;
        }
      }
      .dynamic-radio-group,
      .dynamic-checkbox-group {
        > label span,
        input[type='radio'],
        input[type='checkbox'] {
          color: #848484;
          cursor: text;
        }
      }
      .dynamic-range-picker > span {
        background: none;
        border: none;
        border-radius: 0;
      }
      .anticon {
        display: none;
      }
      .dynamic-rate {
        .anticon {
          display: block;
        }
      }
    }
  }
}

// 新增卡片样式
.ant-card {
  .ant-card-head {
    height:46px;
    padding: 4px 12px 12px 22px;
    font-weight:600;
  }
   .ant-card-body {
    padding-top: 10px !important;
  }
}

// .ant-modal-mask {
//   z-index: 1060;
// }

// .ant-modal-wrap {
//   z-index: 1070;
// }

// .ant-select-dropdown {
//   z-index: 1080 !important;
// }

// .ant-message {
//   z-index: 1080;
// }
/* fdx dynamicAttribute component END */

// 输入框大小
// .ant-form.flow .ant-form-item > .ant-form-item-label {
//   width: auto;
//   line-height: 32px ;
// }
// .ant-form-item-label > label {
//   font-size: 14px !important;
// }

// .ant-input {
//   height: 32px !important;
//   font-size: 14px !important;
// }

// .ant-form-explain,
// .ant-form-extra{
//   line-height: 2;
//   font-size: 14px;
// }
