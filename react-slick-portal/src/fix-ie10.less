.clearfixAfter {
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  content: '.';
}
.clearfix {
  display: block;
  zoom: 1;
}

/* 结束 */

.ie10 {
  // 优化 弹窗时出现滚动条 BEGIN
  .ant-scrolling-effect {
    #root {
      overflow: hidden;
    }
  }
  // END
  // 标准卡片列表 BEGIN
  .ant-list {
    .ant-list-item {
      .clearfix;
      &::after {
        .clearfixAfter;
      }
      span {
        flex: 1;
      }
      .ant-list-item-meta {
        .clearfix;

        float: left;
        width: 40%;
        min-width: 450px;
        &::after {
          .clearfixAfter;
        }

        .ant-list-item-meta-avatar {
          float: left;
        }
        .ant-list-item-meta-content {
          float: left;
          width: 70%;
        }
      }
      .ant-list-item-content {
        float: left;
        min-width: 428px;
      }
      .ant-list-item-action {
        float: right;
      }
    }
  }
  // END
}
