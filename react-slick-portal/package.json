{"name": "react-slick", "version": "1.4.0", "description": "Front-end solution based on antd, umi, dva, and react", "scripts": {"dev": "umi dev", "dev:no-mock": "cross-env MOCK=none umi dev", "build": "umi build", "lint": "eslint --ext .js src mock && npm run lint:style && npm run lint:prettier", "lint:fix": "eslint --fix --ext .js src mock && stylelint --fix \"src/**/*.less\" --syntax less", "lint:prettier": "check-prettier lint", "lint:style": "stylelint \"src/**/*.less\" --syntax less", "prettier": "node ./scripts/prettier.js", "gh": "cross-env gh=true umi build", "analyze": "cross-env ANALYZE=1 umi build", "clear": "rimraf node_modules", "commit": "git cz", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "version": "standard-version"}, "keywords": [], "author": "", "license": "ISC", "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "lint-staged": {"src/**/*.{js,ts,jsx,tsx,vue}": ["eslint --cache --fix"], "packages/**/*.{js,ts,jsx,tsx,vue}": ["eslint --cache --fix"], "*": ["prettier --write --cache --ignore-unknown"]}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@whalecloud/eslint-config": "^0.0.33-beta8", "babel-eslint": "^10.0.1", "babel-plugin-import": "^1.11.0", "chalk": "^2.4.2", "check-prettier": "^1.0.1", "commitizen": "^4.0.4", "conventional-changelog-cli": "^2.0.31", "cross-env": "^5.2.0", "customize-cra": "^0.2.11", "cz-conventional-changelog": "^3.1.0", "eslint": "7.25.0", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^4.1.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-compat": "^3.1.1", "eslint-plugin-import": "^2.17.2", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-react": "^7.12.4", "eslint-plugin-react-hooks": "^1.6.0", "husky": "^4.2.5", "mockjs": "^1.0.1-beta3", "prettier": "^1.14.3", "raf": "^3.4.0", "react-app-rewired": "^2.1.0", "rimraf": "^2.6.2", "roadhog-api-doc": "^1.1.2", "stylelint": "^10.0.1", "stylelint-config-css-modules": "^1.4.0", "stylelint-config-prettier": "^5.1.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^18.3.0", "stylelint-declaration-block-no-ignored-properties": "^2.1.0", "stylelint-order": "^3.0.0"}, "dependencies": {"@antv/data-set": "^0.9.6", "@antv/g6": "^3.2.0", "@babel/runtime": "^7.1.2", "@micro-zoe/micro-app": "^0.8.10", "@rehooks/component-size": "^1.0.3", "@umijs/hooks": "^1.5.1-beta.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "antd": "^3.26.0", "bizcharts": "^3.2.4", "bizcharts-plugin-slider": "^2.1.1-beta.1", "braft-editor": "^2.3.8", "byte-guide": "^1.0.7", "classnames": "^2.2.6", "crypto-js": "^3.1.9-1", "dva": "2.5.0-beta.2", "echarts": "^5.6.0", "echarts-for-react": "^3.0.3", "es6-promise": "^4.2.5", "gg-editor": "2.0.4", "hash.js": "^1.1.5", "html2canvas": "^1.4.1", "immer": "^10.1.1", "immutability-helper": "^3.0.1", "intl": "^1.2.5", "lodash": "4.17.12", "lodash-decorators": "^6.0.0", "memoize-one": "^4.0.2", "moment": "^2.22.2", "numeral": "^2.0.6", "nzh": "^1.0.3", "omit.js": "^1.0.2", "path-to-regexp": "^2.4.0", "prop-types": "^15.6.2", "pubsub-js": "^1.7.0", "qs": "^6.5.2", "rc-queue-anim": "^1.8.3", "rc-texty": "^0.2.0", "rc-trigger": "^2.6.2", "rc-tween-one": "^2.3.2", "react": "^16.8.3", "react-color": "^2.19.3", "react-cropper": "^1.3.0", "react-custom-scrollbars": "^4.2.1", "react-dnd": "^10.0.2", "react-dnd-html5-backend": "^10.0.2", "react-file-viewer": "^1.2.1", "react-fittext": "^1.0.0", "react-grid-layout": "^0.17.1", "react-helmet": "^5.2.1", "react-infinite-scroller": "^1.2.6", "react-player": "^2.12.0", "react-resizable": "^1.7.5", "react-scroll": "^1.9.0", "react-stars": "^2.2.5", "react-sticky": "^6.0.3", "resize-observer-polyfill": "^1.5.1", "standard-version": "^7.1.0", "sunflower-antd": "^0.9.1", "umi": "^2.7.0", "umi-plugin-react": "^1.7.4", "websocket": "^1.0.34", "xlsx": "^0.18.5", "@react-buddy/ide-toolbox": "^2.4.0", "https-proxy-agent": "5.0.1"}, "engines": {"node": ">=10.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 9"], "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}