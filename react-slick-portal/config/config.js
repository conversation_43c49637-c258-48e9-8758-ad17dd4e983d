// https://umijs.org/config/
// import os from 'os';
import path from 'path';
import pageRoutes from './router.config';
import defaultSettings from '../src/defaultSettings';
import themeConfig from './theme/yunnan/config';
import { HttpsProxyAgent } from 'https-proxy-agent';

// 执行命令 npm run build API_HOST=http://www.baidu.com时
// 执行命令 npm run dev API_HOST=http://www.baidu.com时
// process.argv[2] 都等于 'build'
const clashAgent = new HttpsProxyAgent('http://**************:7890/');

const MODE = process.argv[2];
let API_HOST = '';
const versionTime = new Date().toLocaleString();
if ((MODE == 'build' || MODE == 'gh') && process.argv[3] != undefined) {
  API_HOST = process.argv[3].toLocaleLowerCase().split('api_host=')[1];
}
// import webpackplugin from './plugin.config';
export default {
  // extraBabelIncludes: [path.join(__dirname, '../node_modules/@react-dnd')],
  extraBabelIncludes: [path.join(__dirname, '../node_modules/@react-dnd/asap')],
  // add for transfer to umi
  plugins: [
    [
      'umi-plugin-react',
      {
        // 这里暂时还没有添加配置，该插件还不会有作用，我们会在后面的课程按照需求打开相应的配置
        antd: true, // 引入 antd 并实现按需编译
        dva: true,
        dynamicImport: {
          webpackChunkName: true,
          loadingComponent: './components/PageLoading/index',
        },
        locale: {
          enable: false, // default false
          // default: defaultSettings.language, // default zh-CN
          // baseNavigator: defaultSettings.baseNavigator, // default true, 为true时，用navigator.language的值作为默认语言
          // antd: true, // 是否启用antd的<LocaleProvider />
        },
        routes: {
          exclude: [/(consts|models|messages|locales|services|components|utils)/, /model\.js/, /config\.js/],
        },
      },
    ],
  ],
  // theme: {
  //   // "font-size-base": "14px",
  //   'primary-color': defaultSettings.primaryColor,
  //   // "badge-font-size": "12px",
  //   // "btn-font-size-lg": "@font-size-base",
  //   // "menu-dark-submenu-bg": "#000B14",
  //   // "layout-body-background": "#f0f2f5",
  //   // "menu-dark-bg": "#1b1c21",
  //   // "layout-sider-background": "#363742",
  //   'menu-dark-item-active-bg': '#363742',
  //   'border-radius-base': defaultSettings.borderRadiusBase,
  //   'link-color': defaultSettings.primaryColor,
  //   'checkbox-color': defaultSettings.primaryColor,
  //   'btn-primary-bg': defaultSettings.primaryColor,
  // },
  treeShaking: true,
  targets: {
    ie: 9,
  },

  externals: {
    // '@antv/data-set': 'DataSet',
  },
  ignoreMomentLocale: true,
  // 不使用 url-loader 的规则（即不让小图片被转成bas64）,为了兼容ie9
  urlLoaderExcludes: [/\.(png|jpe?g|gif|svg)$/],
  chainWebpack(config, { webpack }) {
    //  svg 使用 file-loader 引入
    config.module
      .rule('svg-with-file')
      .test(/.svg$/)
      .use('svg-with-file-loader')
      .loader('file-loader')
      .options({
        name: 'svg/[name]-[hash:8].[ext]',
      });

    //  png|jpe?g|gif 使用 file-loader 引入
    config.module
      .rule('image-file')
      .test(/\.(png|jpe?g|gif)$/)
      .use('file-loader')
      .loader('file-loader')
      .options({
        name: 'images/[name]-[hash:8].[ext]',
      });

    // config.resolve.alias.set('@mock', path.resolve(process.cwd(), 'mock'));

    // config.devtool('cheap-module-source-map'); // 生成css sourcemap
    config.optimization.splitChunks({
      cacheGroups: {
        vendor: {
          name: 'vendors',
          priority: 1,
        },
        microZoe: {
          name: 'micro-zoe',
          test: /[\\/]node_modules[\\/](@micro-zoe)/,
          enforce: true,
          priority: 10,
        },
        wangeditor: {
          name: 'wangeditor',
          test: /[\\/]node_modules[\\/](@wangeditor)/,
          enforce: true,
          priority: 10,
        },
      },
    });
  },
  lessLoaderOptions: {
    modifyVars: {
      // ...masterTheme,
      ...themeConfig,
    },
    javascriptEnabled: true,
  },
  // devtool: 'source-map',
  cssModulesExcludes: [path.join(__dirname, '../src/global.less')],
  // extraBabelPlugins: [
  //   [
  //     'import',
  //     {
  //       libraryName: 'antd',
  //       style:true
  //     },
  //   ],
  // ],
  base: process.env.gh ? '/' : '/',
  publicPath: process.env.gh ? '/portal-react/' : '/',
  history: 'hash',
  hash: true,
  define: {
    API_HOST,
    OFFER_FLAG: '产品',
    versionTime,
  },
  proxy: {
    '/portal': {
      // target: 'http://*************:9999/portal-react/',
      // target: 'http://*************:9999/portal-react/',
      // target: 'http://localhost:8083/portal-react/',
      // target: 'http://************:9906/portal-react/', //内蒙所内-测试环境
      // target: 'http://************:8083/portal-react/', // 一凡本地
      target: 'http://*************:8282/portal-react/', // 明航本地
      // target:'http://**************:8082/portal-react/',
      // target: 'http://************:9000/portal/',
      // target: 'https://*************:31111/portal-react/', // 现场测试
      secure: false,
      // changeOrigin: true,
      changeOrigin: true,
      // agent: clashAgent,
      // 可选：自定义请求头
      /*headers: {
        Host: '************:9906',
        'X-Proxy-By': 'Clash',
      },*/
    },
    '/orgauth': {
      // target: 'http://*************:9999/portal-react/',
      // target: 'http://*************:9999/portal-react/',
      // target: 'http://localhost:8083/portal-react/',
      target: 'http://************:9906/portal-react/', //内蒙所内-测试环境
      // target: 'http://************:8083/portal-react/', // 一凡本地
      // target: 'http://*************:8282/portal-react/', // 明航本地
      // target:'http://**************:8082/portal-react/',
      // target: 'http://************:9000/portal/',
      // target: 'https://*************:31111/portal-react/', // 现场测试
      secure: false,
      changeOrigin: true,
      agent: clashAgent,
      // 可选：自定义请求头
      headers: {
        Host: '************:9906',
        'X-Proxy-By': 'Clash',
      },
      //changeOrigin: true,
    },
    '/ids-busi-service-isupport': {
      // target: 'http://*************:9999/portal-react/',
      // target: 'http://*************:9999/portal-react/',
      // target: 'http://localhost:8083/portal-react/',
      target: 'http://************:8700/',
      // target:'http://**************:8082/portal-react/',
      // target: 'http://************:9000/portal/',
      // target: 'http://************:8998/portal/',
      // changeOrigin: true,
    },
  },
  // 路由配置
  // routes: pageRoutes,
};
