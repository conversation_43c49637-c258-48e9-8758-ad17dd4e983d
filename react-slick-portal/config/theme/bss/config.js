export default {
  '@font-size-base': '13px',
  '@blue-6': '#0085D0',
  '@link-color': '#0085D0',
  // '@border-color-split': '#f2f4f5',
  '@border-radius-base': '2px',
  // table
  '@table-padding-vertical': '4px',
  '@table-padding-horizontal': '4px',
  // input
  '@input-height-base': '24px',
  '@input-height-lg': '24px',
  '@form-component-max-height': '24px',
  '@btn-height-base': '24px',
  '@input-padding-horizontal-base': '8px', // input padding left and right
  '@input-padding-vertical-base': '2px', // input padding top and bottom
  '@form-item-margin-bottom': '8px', // form-item
  // pagination
  '@pagination-item-size': '24px',
  // card
  '@card-padding-base': '16px', // head padding left and right
  '@card-head-padding': '6px', // head title padding top and bottom
  '@card-inner-head-padding': '6px',
  '@card-head-height': '32px',
  // '@card-head-background': '#fafafa',
  // tabs
  '@tabs-horizontal-padding': '8px',
  '@tabs-vertical-padding': '8px',
  '@tabs-horizontal-margin': '0 16px 0 0',
  '@tabs-vertical-margin': '0 0 8px 0',
  '@tabs-card-height': '32px',
  // menu
  '@menu-inline-toplevel-item-height': '32px',
  '@menu-item-height': '32px',
  // 自定义
  '@slick-space-base': '8px',
};
