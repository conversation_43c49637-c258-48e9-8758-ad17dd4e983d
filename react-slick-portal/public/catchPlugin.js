(function(AA,S){typeof exports=="object"&&typeof module<"u"?module.exports=S():typeof define=="function"&&define.amd?define(S):(AA=typeof globalThis<"u"?globalThis:AA||self,AA.CatchPlugin=S())})(this,function(){"use strict";var hg=Object.defineProperty;var dg=(AA,S,QA)=>S in AA?hg(AA,S,{enumerable:!0,configurable:!0,writable:!0,value:QA}):AA[S]=QA;var D=(AA,S,QA)=>(dg(AA,typeof S!="symbol"?S+"":S,QA),QA);/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var AA=function(e,A){return AA=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])},AA(e,A)};function S(e,A){if(typeof A!="function"&&A!==null)throw new TypeError("Class extends value "+String(A)+" is not a constructor or null");AA(e,A);function t(){this.constructor=e}e.prototype=A===null?Object.create(A):(t.prototype=A.prototype,new t)}var QA=function(){return QA=Object.assign||function(A){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var B in t)Object.prototype.hasOwnProperty.call(t,B)&&(A[B]=t[B])}return A},QA.apply(this,arguments)};function Z(e,A,t,r){function n(B){return B instanceof t?B:new t(function(s){s(B)})}return new(t||(t=Promise))(function(B,s){function i(c){try{o(r.next(c))}catch(g){s(g)}}function a(c){try{o(r.throw(c))}catch(g){s(g)}}function o(c){c.done?B(c.value):n(c.value).then(i,a)}o((r=r.apply(e,A||[])).next())})}function J(e,A){var t={label:0,sent:function(){if(B[0]&1)throw B[1];return B[1]},trys:[],ops:[]},r,n,B,s;return s={next:i(0),throw:i(1),return:i(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function i(o){return function(c){return a([o,c])}}function a(o){if(r)throw new TypeError("Generator is already executing.");for(;t;)try{if(r=1,n&&(B=o[0]&2?n.return:o[0]?n.throw||((B=n.return)&&B.call(n),0):n.next)&&!(B=B.call(n,o[1])).done)return B;switch(n=0,B&&(o=[o[0]&2,B.value]),o[0]){case 0:case 1:B=o;break;case 4:return t.label++,{value:o[1],done:!1};case 5:t.label++,n=o[1],o=[0];continue;case 7:o=t.ops.pop(),t.trys.pop();continue;default:if(B=t.trys,!(B=B.length>0&&B[B.length-1])&&(o[0]===6||o[0]===2)){t=0;continue}if(o[0]===3&&(!B||o[1]>B[0]&&o[1]<B[3])){t.label=o[1];break}if(o[0]===6&&t.label<B[1]){t.label=B[1],B=o;break}if(B&&t.label<B[2]){t.label=B[2],t.ops.push(o);break}B[2]&&t.ops.pop(),t.trys.pop();continue}o=A.call(e,t)}catch(c){o=[6,c],n=0}finally{r=B=0}if(o[0]&5)throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}}function Ie(e,A,t){if(t||arguments.length===2)for(var r=0,n=A.length,B;r<n;r++)(B||!(r in A))&&(B||(B=Array.prototype.slice.call(A,0,r)),B[r]=A[r]);return e.concat(B||A)}for(var wA=function(){function e(A,t,r,n){this.left=A,this.top=t,this.width=r,this.height=n}return e.prototype.add=function(A,t,r,n){return new e(this.left+A,this.top+t,this.width+r,this.height+n)},e.fromClientRect=function(A,t){return new e(t.left+A.windowBounds.left,t.top+A.windowBounds.top,t.width,t.height)},e.fromDOMRectList=function(A,t){var r=Array.from(t).find(function(n){return n.width!==0});return r?new e(r.left+A.windowBounds.left,r.top+A.windowBounds.top,r.width,r.height):e.EMPTY},e.EMPTY=new e(0,0,0,0),e}(),ve=function(e,A){return wA.fromClientRect(e,A.getBoundingClientRect())},FB=function(e){var A=e.body,t=e.documentElement;if(!A||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(A.scrollWidth,t.scrollWidth),Math.max(A.offsetWidth,t.offsetWidth),Math.max(A.clientWidth,t.clientWidth)),n=Math.max(Math.max(A.scrollHeight,t.scrollHeight),Math.max(A.offsetHeight,t.offsetHeight),Math.max(A.clientHeight,t.clientHeight));return new wA(0,0,r,n)},me=function(e){for(var A=[],t=0,r=e.length;t<r;){var n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var B=e.charCodeAt(t++);(B&64512)===56320?A.push(((n&1023)<<10)+(B&1023)+65536):(A.push(n),t--)}else A.push(n)}return A},M=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var t=e.length;if(!t)return"";for(var r=[],n=-1,B="";++n<t;){var s=e[n];s<=65535?r.push(s):(s-=65536,r.push((s>>10)+55296,s%1024+56320)),(n+1===t||r.length>16384)&&(B+=String.fromCharCode.apply(String,r),r.length=0)}return B},ut="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",hB=typeof Uint8Array>"u"?[]:new Uint8Array(256),ye=0;ye<ut.length;ye++)hB[ut.charCodeAt(ye)]=ye;for(var ft="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ae=typeof Uint8Array>"u"?[]:new Uint8Array(256),Ke=0;Ke<ft.length;Ke++)Ae[ft.charCodeAt(Ke)]=Ke;for(var dB=function(e){var A=e.length*.75,t=e.length,r,n=0,B,s,i,a;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var o=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),c=Array.isArray(o)?o:new Uint8Array(o);for(r=0;r<t;r+=4)B=Ae[e.charCodeAt(r)],s=Ae[e.charCodeAt(r+1)],i=Ae[e.charCodeAt(r+2)],a=Ae[e.charCodeAt(r+3)],c[n++]=B<<2|s>>4,c[n++]=(s&15)<<4|i>>2,c[n++]=(i&3)<<6|a&63;return o},EB=function(e){for(var A=e.length,t=[],r=0;r<A;r+=2)t.push(e[r+1]<<8|e[r]);return t},pB=function(e){for(var A=e.length,t=[],r=0;r<A;r+=4)t.push(e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]);return t},LA=5,Ur=6+5,Fr=2,HB=Ur-LA,Ct=65536>>LA,IB=1<<LA,hr=IB-1,vB=1024>>LA,mB=Ct+vB,yB=mB,KB=32,LB=yB+KB,bB=65536>>Ur,DB=1<<HB,xB=DB-1,Ut=function(e,A,t){return e.slice?e.slice(A,t):new Uint16Array(Array.prototype.slice.call(e,A,t))},TB=function(e,A,t){return e.slice?e.slice(A,t):new Uint32Array(Array.prototype.slice.call(e,A,t))},SB=function(e,A){var t=dB(e),r=Array.isArray(t)?pB(t):new Uint32Array(t),n=Array.isArray(t)?EB(t):new Uint16Array(t),B=24,s=Ut(n,B/2,r[4]/2),i=r[5]===2?Ut(n,(B+r[4])/2):TB(r,Math.ceil((B+r[4])/4));return new OB(r[0],r[1],r[2],r[3],s,i)},OB=function(){function e(A,t,r,n,B,s){this.initialValue=A,this.errorValue=t,this.highStart=r,this.highValueIndex=n,this.index=B,this.data=s}return e.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>LA],t=(t<<Fr)+(A&hr),this.data[t];if(A<=65535)return t=this.index[Ct+(A-55296>>LA)],t=(t<<Fr)+(A&hr),this.data[t];if(A<this.highStart)return t=LB-bB+(A>>Ur),t=this.index[t],t+=A>>LA&xB,t=this.index[t],t=(t<<Fr)+(A&hr),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),Ft="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",MB=typeof Uint8Array>"u"?[]:new Uint8Array(256),Le=0;Le<Ft.length;Le++)MB[Ft.charCodeAt(Le)]=Le;var GB="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",ht=50,RB=1,dt=2,Et=3,VB=4,NB=5,pt=7,Ht=8,It=9,FA=10,dr=11,vt=12,Er=13,_B=14,ee=15,pr=16,be=17,re=18,kB=19,mt=20,Hr=21,te=22,Ir=23,NA=24,eA=25,ne=26,Be=27,_A=28,PB=29,bA=30,XB=31,De=32,xe=33,vr=34,mr=35,yr=36,se=37,Kr=38,Te=39,Se=40,Lr=41,yt=42,JB=43,YB=[9001,65288],Kt="!",I="\xD7",Oe="\xF7",br=SB(GB),lA=[bA,yr],Dr=[RB,dt,Et,NB],Lt=[FA,Ht],bt=[Be,ne],WB=Dr.concat(Lt),Dt=[Kr,Te,Se,vr,mr],ZB=[ee,Er],qB=function(e,A){A===void 0&&(A="strict");var t=[],r=[],n=[];return e.forEach(function(B,s){var i=br.get(B);if(i>ht?(n.push(!0),i-=ht):n.push(!1),["normal","auto","loose"].indexOf(A)!==-1&&[8208,8211,12316,12448].indexOf(B)!==-1)return r.push(s),t.push(pr);if(i===VB||i===dr){if(s===0)return r.push(s),t.push(bA);var a=t[s-1];return WB.indexOf(a)===-1?(r.push(r[s-1]),t.push(a)):(r.push(s),t.push(bA))}if(r.push(s),i===XB)return t.push(A==="strict"?Hr:se);if(i===yt||i===PB)return t.push(bA);if(i===JB)return B>=131072&&B<=196605||B>=196608&&B<=262141?t.push(se):t.push(bA);t.push(i)}),[r,t,n]},xr=function(e,A,t,r){var n=r[t];if(Array.isArray(e)?e.indexOf(n)!==-1:e===n)for(var B=t;B<=r.length;){B++;var s=r[B];if(s===A)return!0;if(s!==FA)break}if(n===FA)for(var B=t;B>0;){B--;var i=r[B];if(Array.isArray(e)?e.indexOf(i)!==-1:e===i)for(var a=t;a<=r.length;){a++;var s=r[a];if(s===A)return!0;if(s!==FA)break}if(i!==FA)break}return!1},xt=function(e,A){for(var t=e;t>=0;){var r=A[t];if(r===FA)t--;else return r}return 0},jB=function(e,A,t,r,n){if(t[r]===0)return I;var B=r-1;if(Array.isArray(n)&&n[B]===!0)return I;var s=B-1,i=B+1,a=A[B],o=s>=0?A[s]:0,c=A[i];if(a===dt&&c===Et)return I;if(Dr.indexOf(a)!==-1)return Kt;if(Dr.indexOf(c)!==-1||Lt.indexOf(c)!==-1)return I;if(xt(B,A)===Ht)return Oe;if(br.get(e[B])===dr||(a===De||a===xe)&&br.get(e[i])===dr||a===pt||c===pt||a===It||[FA,Er,ee].indexOf(a)===-1&&c===It||[be,re,kB,NA,_A].indexOf(c)!==-1||xt(B,A)===te||xr(Ir,te,B,A)||xr([be,re],Hr,B,A)||xr(vt,vt,B,A))return I;if(a===FA)return Oe;if(a===Ir||c===Ir)return I;if(c===pr||a===pr)return Oe;if([Er,ee,Hr].indexOf(c)!==-1||a===_B||o===yr&&ZB.indexOf(a)!==-1||a===_A&&c===yr||c===mt||lA.indexOf(c)!==-1&&a===eA||lA.indexOf(a)!==-1&&c===eA||a===Be&&[se,De,xe].indexOf(c)!==-1||[se,De,xe].indexOf(a)!==-1&&c===ne||lA.indexOf(a)!==-1&&bt.indexOf(c)!==-1||bt.indexOf(a)!==-1&&lA.indexOf(c)!==-1||[Be,ne].indexOf(a)!==-1&&(c===eA||[te,ee].indexOf(c)!==-1&&A[i+1]===eA)||[te,ee].indexOf(a)!==-1&&c===eA||a===eA&&[eA,_A,NA].indexOf(c)!==-1)return I;if([eA,_A,NA,be,re].indexOf(c)!==-1)for(var g=B;g>=0;){var Q=A[g];if(Q===eA)return I;if([_A,NA].indexOf(Q)!==-1)g--;else break}if([Be,ne].indexOf(c)!==-1)for(var g=[be,re].indexOf(a)!==-1?s:B;g>=0;){var Q=A[g];if(Q===eA)return I;if([_A,NA].indexOf(Q)!==-1)g--;else break}if(Kr===a&&[Kr,Te,vr,mr].indexOf(c)!==-1||[Te,vr].indexOf(a)!==-1&&[Te,Se].indexOf(c)!==-1||[Se,mr].indexOf(a)!==-1&&c===Se||Dt.indexOf(a)!==-1&&[mt,ne].indexOf(c)!==-1||Dt.indexOf(c)!==-1&&a===Be||lA.indexOf(a)!==-1&&lA.indexOf(c)!==-1||a===NA&&lA.indexOf(c)!==-1||lA.concat(eA).indexOf(a)!==-1&&c===te&&YB.indexOf(e[i])===-1||lA.concat(eA).indexOf(c)!==-1&&a===re)return I;if(a===Lr&&c===Lr){for(var f=t[B],w=1;f>0&&(f--,A[f]===Lr);)w++;if(w%2!==0)return I}return a===De&&c===xe?I:Oe},zB=function(e,A){A||(A={lineBreak:"normal",wordBreak:"normal"});var t=qB(e,A.lineBreak),r=t[0],n=t[1],B=t[2];(A.wordBreak==="break-all"||A.wordBreak==="break-word")&&(n=n.map(function(i){return[eA,bA,yt].indexOf(i)!==-1?se:i}));var s=A.wordBreak==="keep-all"?B.map(function(i,a){return i&&e[a]>=19968&&e[a]<=40959}):void 0;return[r,n,s]},$B=function(){function e(A,t,r,n){this.codePoints=A,this.required=t===Kt,this.start=r,this.end=n}return e.prototype.slice=function(){return M.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),As=function(e,A){var t=me(e),r=zB(t,A),n=r[0],B=r[1],s=r[2],i=t.length,a=0,o=0;return{next:function(){if(o>=i)return{done:!0,value:null};for(var c=I;o<i&&(c=jB(t,B,n,++o,s))===I;);if(c!==I||o===i){var g=new $B(t,c,a,o);return a=o,{value:g,done:!1}}return{done:!0,value:null}}}},es=1<<0,rs=1<<1,ie=1<<2,Tt=1<<3,Me=10,St=47,ae=92,ts=9,ns=32,Ge=34,oe=61,Bs=35,ss=36,is=37,Re=39,Ve=40,ce=41,as=95,q=45,os=33,cs=60,gs=62,Qs=64,ws=91,ls=93,us=61,fs=123,Ne=63,Cs=125,Ot=124,Us=126,Fs=128,Mt=65533,Tr=42,DA=43,hs=44,ds=58,Es=59,ge=46,ps=0,Hs=8,Is=11,vs=14,ms=31,ys=127,aA=-1,Gt=48,Rt=97,Vt=101,Ks=102,Ls=117,bs=122,Nt=65,_t=69,kt=70,Ds=85,xs=90,Y=function(e){return e>=Gt&&e<=57},Ts=function(e){return e>=55296&&e<=57343},kA=function(e){return Y(e)||e>=Nt&&e<=kt||e>=Rt&&e<=Ks},Ss=function(e){return e>=Rt&&e<=bs},Os=function(e){return e>=Nt&&e<=xs},Ms=function(e){return Ss(e)||Os(e)},Gs=function(e){return e>=Fs},_e=function(e){return e===Me||e===ts||e===ns},ke=function(e){return Ms(e)||Gs(e)||e===as},Pt=function(e){return ke(e)||Y(e)||e===q},Rs=function(e){return e>=ps&&e<=Hs||e===Is||e>=vs&&e<=ms||e===ys},hA=function(e,A){return e!==ae?!1:A!==Me},Pe=function(e,A,t){return e===q?ke(A)||hA(A,t):ke(e)?!0:!!(e===ae&&hA(e,A))},Sr=function(e,A,t){return e===DA||e===q?Y(A)?!0:A===ge&&Y(t):Y(e===ge?A:e)},Vs=function(e){var A=0,t=1;(e[A]===DA||e[A]===q)&&(e[A]===q&&(t=-1),A++);for(var r=[];Y(e[A]);)r.push(e[A++]);var n=r.length?parseInt(M.apply(void 0,r),10):0;e[A]===ge&&A++;for(var B=[];Y(e[A]);)B.push(e[A++]);var s=B.length,i=s?parseInt(M.apply(void 0,B),10):0;(e[A]===_t||e[A]===Vt)&&A++;var a=1;(e[A]===DA||e[A]===q)&&(e[A]===q&&(a=-1),A++);for(var o=[];Y(e[A]);)o.push(e[A++]);var c=o.length?parseInt(M.apply(void 0,o),10):0;return t*(n+i*Math.pow(10,-s))*Math.pow(10,a*c)},Ns={type:2},_s={type:3},ks={type:4},Ps={type:13},Xs={type:8},Js={type:21},Ys={type:9},Ws={type:10},Zs={type:11},qs={type:12},js={type:14},Xe={type:23},zs={type:1},$s={type:25},Ai={type:24},ei={type:26},ri={type:27},ti={type:28},ni={type:29},Bi={type:31},Or={type:32},Xt=function(){function e(){this._value=[]}return e.prototype.write=function(A){this._value=this._value.concat(me(A))},e.prototype.read=function(){for(var A=[],t=this.consumeToken();t!==Or;)A.push(t),t=this.consumeToken();return A},e.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case Ge:return this.consumeStringToken(Ge);case Bs:var t=this.peekCodePoint(0),r=this.peekCodePoint(1),n=this.peekCodePoint(2);if(Pt(t)||hA(r,n)){var B=Pe(t,r,n)?rs:es,s=this.consumeName();return{type:5,value:s,flags:B}}break;case ss:if(this.peekCodePoint(0)===oe)return this.consumeCodePoint(),Ps;break;case Re:return this.consumeStringToken(Re);case Ve:return Ns;case ce:return _s;case Tr:if(this.peekCodePoint(0)===oe)return this.consumeCodePoint(),js;break;case DA:if(Sr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case hs:return ks;case q:var i=A,a=this.peekCodePoint(0),o=this.peekCodePoint(1);if(Sr(i,a,o))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(Pe(i,a,o))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(a===q&&o===gs)return this.consumeCodePoint(),this.consumeCodePoint(),Ai;break;case ge:if(Sr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case St:if(this.peekCodePoint(0)===Tr)for(this.consumeCodePoint();;){var c=this.consumeCodePoint();if(c===Tr&&(c=this.consumeCodePoint(),c===St))return this.consumeToken();if(c===aA)return this.consumeToken()}break;case ds:return ei;case Es:return ri;case cs:if(this.peekCodePoint(0)===os&&this.peekCodePoint(1)===q&&this.peekCodePoint(2)===q)return this.consumeCodePoint(),this.consumeCodePoint(),$s;break;case Qs:var g=this.peekCodePoint(0),Q=this.peekCodePoint(1),f=this.peekCodePoint(2);if(Pe(g,Q,f)){var s=this.consumeName();return{type:7,value:s}}break;case ws:return ti;case ae:if(hA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case ls:return ni;case us:if(this.peekCodePoint(0)===oe)return this.consumeCodePoint(),Xs;break;case fs:return Zs;case Cs:return qs;case Ls:case Ds:var w=this.peekCodePoint(0),l=this.peekCodePoint(1);return w===DA&&(kA(l)||l===Ne)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case Ot:if(this.peekCodePoint(0)===oe)return this.consumeCodePoint(),Ys;if(this.peekCodePoint(0)===Ot)return this.consumeCodePoint(),Js;break;case Us:if(this.peekCodePoint(0)===oe)return this.consumeCodePoint(),Ws;break;case aA:return Or}return _e(A)?(this.consumeWhiteSpace(),Bi):Y(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):ke(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:M(A)}},e.prototype.consumeCodePoint=function(){var A=this._value.shift();return typeof A>"u"?-1:A},e.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},e.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},e.prototype.consumeUnicodeRangeToken=function(){for(var A=[],t=this.consumeCodePoint();kA(t)&&A.length<6;)A.push(t),t=this.consumeCodePoint();for(var r=!1;t===Ne&&A.length<6;)A.push(t),t=this.consumeCodePoint(),r=!0;if(r){var n=parseInt(M.apply(void 0,A.map(function(a){return a===Ne?Gt:a})),16),B=parseInt(M.apply(void 0,A.map(function(a){return a===Ne?kt:a})),16);return{type:30,start:n,end:B}}var s=parseInt(M.apply(void 0,A),16);if(this.peekCodePoint(0)===q&&kA(this.peekCodePoint(1))){this.consumeCodePoint(),t=this.consumeCodePoint();for(var i=[];kA(t)&&i.length<6;)i.push(t),t=this.consumeCodePoint();var B=parseInt(M.apply(void 0,i),16);return{type:30,start:s,end:B}}else return{type:30,start:s,end:s}},e.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return A.toLowerCase()==="url"&&this.peekCodePoint(0)===Ve?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===Ve?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},e.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===aA)return{type:22,value:""};var t=this.peekCodePoint(0);if(t===Re||t===Ge){var r=this.consumeStringToken(this.consumeCodePoint());return r.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===aA||this.peekCodePoint(0)===ce)?(this.consumeCodePoint(),{type:22,value:r.value}):(this.consumeBadUrlRemnants(),Xe)}for(;;){var n=this.consumeCodePoint();if(n===aA||n===ce)return{type:22,value:M.apply(void 0,A)};if(_e(n))return this.consumeWhiteSpace(),this.peekCodePoint(0)===aA||this.peekCodePoint(0)===ce?(this.consumeCodePoint(),{type:22,value:M.apply(void 0,A)}):(this.consumeBadUrlRemnants(),Xe);if(n===Ge||n===Re||n===Ve||Rs(n))return this.consumeBadUrlRemnants(),Xe;if(n===ae)if(hA(n,this.peekCodePoint(0)))A.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),Xe;else A.push(n)}},e.prototype.consumeWhiteSpace=function(){for(;_e(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===ce||A===aA)return;hA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(A){for(var t=5e4,r="";A>0;){var n=Math.min(t,A);r+=M.apply(void 0,this._value.splice(0,n)),A-=n}return this._value.shift(),r},e.prototype.consumeStringToken=function(A){var t="",r=0;do{var n=this._value[r];if(n===aA||n===void 0||n===A)return t+=this.consumeStringSlice(r),{type:0,value:t};if(n===Me)return this._value.splice(0,r),zs;if(n===ae){var B=this._value[r+1];B!==aA&&B!==void 0&&(B===Me?(t+=this.consumeStringSlice(r),r=-1,this._value.shift()):hA(n,B)&&(t+=this.consumeStringSlice(r),t+=M(this.consumeEscapedCodePoint()),r=-1))}r++}while(!0)},e.prototype.consumeNumber=function(){var A=[],t=ie,r=this.peekCodePoint(0);for((r===DA||r===q)&&A.push(this.consumeCodePoint());Y(this.peekCodePoint(0));)A.push(this.consumeCodePoint());r=this.peekCodePoint(0);var n=this.peekCodePoint(1);if(r===ge&&Y(n))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=Tt;Y(this.peekCodePoint(0));)A.push(this.consumeCodePoint());r=this.peekCodePoint(0),n=this.peekCodePoint(1);var B=this.peekCodePoint(2);if((r===_t||r===Vt)&&((n===DA||n===q)&&Y(B)||Y(n)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=Tt;Y(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Vs(A),t]},e.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),t=A[0],r=A[1],n=this.peekCodePoint(0),B=this.peekCodePoint(1),s=this.peekCodePoint(2);if(Pe(n,B,s)){var i=this.consumeName();return{type:15,number:t,flags:r,unit:i}}return n===is?(this.consumeCodePoint(),{type:16,number:t,flags:r}):{type:17,number:t,flags:r}},e.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(kA(A)){for(var t=M(A);kA(this.peekCodePoint(0))&&t.length<6;)t+=M(this.consumeCodePoint());_e(this.peekCodePoint(0))&&this.consumeCodePoint();var r=parseInt(t,16);return r===0||Ts(r)||r>1114111?Mt:r}return A===aA?Mt:A},e.prototype.consumeName=function(){for(var A="";;){var t=this.consumeCodePoint();if(Pt(t))A+=M(t);else if(hA(t,this.peekCodePoint(0)))A+=M(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(t),A}},e}(),Jt=function(){function e(A){this._tokens=A}return e.create=function(A){var t=new Xt;return t.write(A),new e(t.read())},e.parseValue=function(A){return e.create(A).parseComponentValue()},e.parseValues=function(A){return e.create(A).parseComponentValues()},e.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===31;)A=this.consumeToken();if(A.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var t=this.consumeComponentValue();do A=this.consumeToken();while(A.type===31);if(A.type===32)return t;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){for(var A=[];;){var t=this.consumeComponentValue();if(t.type===32)return A;A.push(t),A.push()}},e.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},e.prototype.consumeSimpleBlock=function(A){for(var t={type:A,values:[]},r=this.consumeToken();;){if(r.type===32||ii(r,A))return t;this.reconsumeToken(r),t.values.push(this.consumeComponentValue()),r=this.consumeToken()}},e.prototype.consumeFunction=function(A){for(var t={name:A.value,values:[],type:18};;){var r=this.consumeToken();if(r.type===32||r.type===3)return t;this.reconsumeToken(r),t.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){var A=this._tokens.shift();return typeof A>"u"?Or:A},e.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},e}(),Qe=function(e){return e.type===15},PA=function(e){return e.type===17},L=function(e){return e.type===20},si=function(e){return e.type===0},Mr=function(e,A){return L(e)&&e.value===A},Yt=function(e){return e.type!==31},XA=function(e){return e.type!==31&&e.type!==4},oA=function(e){var A=[],t=[];return e.forEach(function(r){if(r.type===4){if(t.length===0)throw new Error("Error parsing function args, zero tokens for arg");A.push(t),t=[];return}r.type!==31&&t.push(r)}),t.length&&A.push(t),A},ii=function(e,A){return A===11&&e.type===12||A===28&&e.type===29?!0:A===2&&e.type===3},dA=function(e){return e.type===17||e.type===15},R=function(e){return e.type===16||dA(e)},Wt=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},k={type:17,number:0,flags:ie},Gr={type:16,number:50,flags:ie},EA={type:16,number:100,flags:ie},we=function(e,A,t){var r=e[0],n=e[1];return[x(r,A),x(typeof n<"u"?n:r,t)]},x=function(e,A){if(e.type===16)return e.number/100*A;if(Qe(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number},Zt="deg",qt="grad",jt="rad",zt="turn",Je={name:"angle",parse:function(e,A){if(A.type===15)switch(A.unit){case Zt:return Math.PI*A.number/180;case qt:return Math.PI/200*A.number;case jt:return A.number;case zt:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},$t=function(e){return e.type===15&&(e.unit===Zt||e.unit===qt||e.unit===jt||e.unit===zt)},An=function(e){var A=e.filter(L).map(function(t){return t.value}).join(" ");switch(A){case"to bottom right":case"to right bottom":case"left top":case"top left":return[k,k];case"to top":case"bottom":return tA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[k,EA];case"to right":case"left":return tA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[EA,EA];case"to bottom":case"top":return tA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[EA,k];case"to left":case"right":return tA(270)}return 0},tA=function(e){return Math.PI*e/180},pA={name:"color",parse:function(e,A){if(A.type===18){var t=ai[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return t(e,A.values)}if(A.type===5){if(A.value.length===3){var r=A.value.substring(0,1),n=A.value.substring(1,2),B=A.value.substring(2,3);return IA(parseInt(r+r,16),parseInt(n+n,16),parseInt(B+B,16),1)}if(A.value.length===4){var r=A.value.substring(0,1),n=A.value.substring(1,2),B=A.value.substring(2,3),s=A.value.substring(3,4);return IA(parseInt(r+r,16),parseInt(n+n,16),parseInt(B+B,16),parseInt(s+s,16)/255)}if(A.value.length===6){var r=A.value.substring(0,2),n=A.value.substring(2,4),B=A.value.substring(4,6);return IA(parseInt(r,16),parseInt(n,16),parseInt(B,16),1)}if(A.value.length===8){var r=A.value.substring(0,2),n=A.value.substring(2,4),B=A.value.substring(4,6),s=A.value.substring(6,8);return IA(parseInt(r,16),parseInt(n,16),parseInt(B,16),parseInt(s,16)/255)}}if(A.type===20){var i=uA[A.value.toUpperCase()];if(typeof i<"u")return i}return uA.TRANSPARENT}},HA=function(e){return(255&e)===0},_=function(e){var A=255&e,t=255&e>>8,r=255&e>>16,n=255&e>>24;return A<255?"rgba("+n+","+r+","+t+","+A/255+")":"rgb("+n+","+r+","+t+")"},IA=function(e,A,t,r){return(e<<24|A<<16|t<<8|Math.round(r*255)<<0)>>>0},en=function(e,A){if(e.type===17)return e.number;if(e.type===16){var t=A===3?1:255;return A===3?e.number/100*t:Math.round(e.number/100*t)}return 0},rn=function(e,A){var t=A.filter(XA);if(t.length===3){var r=t.map(en),n=r[0],B=r[1],s=r[2];return IA(n,B,s,1)}if(t.length===4){var i=t.map(en),n=i[0],B=i[1],s=i[2],a=i[3];return IA(n,B,s,a)}return 0};function Rr(e,A,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(A-e)*t*6+e:t<1/2?A:t<2/3?(A-e)*6*(2/3-t)+e:e}var tn=function(e,A){var t=A.filter(XA),r=t[0],n=t[1],B=t[2],s=t[3],i=(r.type===17?tA(r.number):Je.parse(e,r))/(Math.PI*2),a=R(n)?n.number/100:0,o=R(B)?B.number/100:0,c=typeof s<"u"&&R(s)?x(s,1):1;if(a===0)return IA(o*255,o*255,o*255,1);var g=o<=.5?o*(a+1):o+a-o*a,Q=o*2-g,f=Rr(Q,g,i+1/3),w=Rr(Q,g,i),l=Rr(Q,g,i-1/3);return IA(f*255,w*255,l*255,c)},ai={hsl:tn,hsla:tn,rgb:rn,rgba:rn},le=function(e,A){return pA.parse(e,Jt.create(A).parseComponentValue())},uA={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},oi={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(t){if(L(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},ci={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Ye=function(e,A){var t=pA.parse(e,A[0]),r=A[1];return r&&R(r)?{color:t,stop:r}:{color:t,stop:null}},nn=function(e,A){var t=e[0],r=e[e.length-1];t.stop===null&&(t.stop=k),r.stop===null&&(r.stop=EA);for(var n=[],B=0,s=0;s<e.length;s++){var i=e[s].stop;if(i!==null){var a=x(i,A);a>B?n.push(a):n.push(B),B=a}else n.push(null)}for(var o=null,s=0;s<n.length;s++){var c=n[s];if(c===null)o===null&&(o=s);else if(o!==null){for(var g=s-o,Q=n[o-1],f=(c-Q)/(g+1),w=1;w<=g;w++)n[o+w-1]=f*w;o=null}}return e.map(function(l,p){var h=l.color;return{color:h,stop:Math.max(Math.min(1,n[p]/A),0)}})},gi=function(e,A,t){var r=A/2,n=t/2,B=x(e[0],A)-r,s=n-x(e[1],t);return(Math.atan2(s,B)+Math.PI*2)%(Math.PI*2)},Qi=function(e,A,t){var r=typeof e=="number"?e:gi(e,A,t),n=Math.abs(A*Math.sin(r))+Math.abs(t*Math.cos(r)),B=A/2,s=t/2,i=n/2,a=Math.sin(r-Math.PI/2)*i,o=Math.cos(r-Math.PI/2)*i;return[n,B-o,B+o,s-a,s+a]},iA=function(e,A){return Math.sqrt(e*e+A*A)},Bn=function(e,A,t,r,n){var B=[[0,0],[0,A],[e,0],[e,A]];return B.reduce(function(s,i){var a=i[0],o=i[1],c=iA(t-a,r-o);return(n?c<s.optimumDistance:c>s.optimumDistance)?{optimumCorner:i,optimumDistance:c}:s},{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},wi=function(e,A,t,r,n){var B=0,s=0;switch(e.size){case 0:e.shape===0?B=s=Math.min(Math.abs(A),Math.abs(A-r),Math.abs(t),Math.abs(t-n)):e.shape===1&&(B=Math.min(Math.abs(A),Math.abs(A-r)),s=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:if(e.shape===0)B=s=Math.min(iA(A,t),iA(A,t-n),iA(A-r,t),iA(A-r,t-n));else if(e.shape===1){var i=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(A),Math.abs(A-r)),a=Bn(r,n,A,t,!0),o=a[0],c=a[1];B=iA(o-A,(c-t)/i),s=i*B}break;case 1:e.shape===0?B=s=Math.max(Math.abs(A),Math.abs(A-r),Math.abs(t),Math.abs(t-n)):e.shape===1&&(B=Math.max(Math.abs(A),Math.abs(A-r)),s=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:if(e.shape===0)B=s=Math.max(iA(A,t),iA(A,t-n),iA(A-r,t),iA(A-r,t-n));else if(e.shape===1){var i=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(A),Math.abs(A-r)),g=Bn(r,n,A,t,!1),o=g[0],c=g[1];B=iA(o-A,(c-t)/i),s=i*B}break}return Array.isArray(e.size)&&(B=x(e.size[0],r),s=e.size.length===2?x(e.size[1],n):B),[B,s]},li=function(e,A){var t=tA(180),r=[];return oA(A).forEach(function(n,B){if(B===0){var s=n[0];if(s.type===20&&s.value==="to"){t=An(n);return}else if($t(s)){t=Je.parse(e,s);return}}var i=Ye(e,n);r.push(i)}),{angle:t,stops:r,type:1}},We=function(e,A){var t=tA(180),r=[];return oA(A).forEach(function(n,B){if(B===0){var s=n[0];if(s.type===20&&["top","left","right","bottom"].indexOf(s.value)!==-1){t=An(n);return}else if($t(s)){t=(Je.parse(e,s)+tA(270))%tA(360);return}}var i=Ye(e,n);r.push(i)}),{angle:t,stops:r,type:1}},ui=function(e,A){var t=tA(180),r=[],n=1,B=0,s=3,i=[];return oA(A).forEach(function(a,o){var c=a[0];if(o===0){if(L(c)&&c.value==="linear"){n=1;return}else if(L(c)&&c.value==="radial"){n=2;return}}if(c.type===18){if(c.name==="from"){var g=pA.parse(e,c.values[0]);r.push({stop:k,color:g})}else if(c.name==="to"){var g=pA.parse(e,c.values[0]);r.push({stop:EA,color:g})}else if(c.name==="color-stop"){var Q=c.values.filter(XA);if(Q.length===2){var g=pA.parse(e,Q[1]),f=Q[0];PA(f)&&r.push({stop:{type:16,number:f.number*100,flags:f.flags},color:g})}}}}),n===1?{angle:(t+tA(180))%tA(360),stops:r,type:n}:{size:s,shape:B,stops:r,position:i,type:n}},sn="closest-side",an="farthest-side",on="closest-corner",cn="farthest-corner",gn="circle",Qn="ellipse",wn="cover",ln="contain",fi=function(e,A){var t=0,r=3,n=[],B=[];return oA(A).forEach(function(s,i){var a=!0;if(i===0){var o=!1;a=s.reduce(function(g,Q){if(o)if(L(Q))switch(Q.value){case"center":return B.push(Gr),g;case"top":case"left":return B.push(k),g;case"right":case"bottom":return B.push(EA),g}else(R(Q)||dA(Q))&&B.push(Q);else if(L(Q))switch(Q.value){case gn:return t=0,!1;case Qn:return t=1,!1;case"at":return o=!0,!1;case sn:return r=0,!1;case wn:case an:return r=1,!1;case ln:case on:return r=2,!1;case cn:return r=3,!1}else if(dA(Q)||R(Q))return Array.isArray(r)||(r=[]),r.push(Q),!1;return g},a)}if(a){var c=Ye(e,s);n.push(c)}}),{size:r,shape:t,stops:n,position:B,type:2}},Ze=function(e,A){var t=0,r=3,n=[],B=[];return oA(A).forEach(function(s,i){var a=!0;if(i===0?a=s.reduce(function(c,g){if(L(g))switch(g.value){case"center":return B.push(Gr),!1;case"top":case"left":return B.push(k),!1;case"right":case"bottom":return B.push(EA),!1}else if(R(g)||dA(g))return B.push(g),!1;return c},a):i===1&&(a=s.reduce(function(c,g){if(L(g))switch(g.value){case gn:return t=0,!1;case Qn:return t=1,!1;case ln:case sn:return r=0,!1;case an:return r=1,!1;case on:return r=2,!1;case wn:case cn:return r=3,!1}else if(dA(g)||R(g))return Array.isArray(r)||(r=[]),r.push(g),!1;return c},a)),a){var o=Ye(e,s);n.push(o)}}),{size:r,shape:t,stops:n,position:B,type:2}},Ci=function(e){return e.type===1},Ui=function(e){return e.type===2},Vr={name:"image",parse:function(e,A){if(A.type===22){var t={url:A.value,type:0};return e.cache.addImage(A.value),t}if(A.type===18){var r=un[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return r(e,A.values)}throw new Error("Unsupported image type "+A.type)}};function Fi(e){return!(e.type===20&&e.value==="none")&&(e.type!==18||!!un[e.name])}var un={"linear-gradient":li,"-moz-linear-gradient":We,"-ms-linear-gradient":We,"-o-linear-gradient":We,"-webkit-linear-gradient":We,"radial-gradient":fi,"-moz-radial-gradient":Ze,"-ms-radial-gradient":Ze,"-o-radial-gradient":Ze,"-webkit-radial-gradient":Ze,"-webkit-gradient":ui},hi={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var t=A[0];return t.type===20&&t.value==="none"?[]:A.filter(function(r){return XA(r)&&Fi(r)}).map(function(r){return Vr.parse(e,r)})}},di={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(t){if(L(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Ei={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(e,A){return oA(A).map(function(t){return t.filter(R)}).map(Wt)}},pi={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(e,A){return oA(A).map(function(t){return t.filter(L).map(function(r){return r.value}).join(" ")}).map(Hi)}},Hi=function(e){switch(e){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},JA;(function(e){e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover"})(JA||(JA={}));var Ii={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(e,A){return oA(A).map(function(t){return t.filter(vi)})}},vi=function(e){return L(e)||R(e)},qe=function(e){return{name:"border-"+e+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},mi=qe("top"),yi=qe("right"),Ki=qe("bottom"),Li=qe("left"),je=function(e){return{name:"border-radius-"+e,initialValue:"0 0",prefix:!1,type:1,parse:function(A,t){return Wt(t.filter(R))}}},bi=je("top-left"),Di=je("top-right"),xi=je("bottom-right"),Ti=je("bottom-left"),ze=function(e){return{name:"border-"+e+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,t){switch(t){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},Si=ze("top"),Oi=ze("right"),Mi=ze("bottom"),Gi=ze("left"),$e=function(e){return{name:"border-"+e+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,t){return Qe(t)?t.number:0}}},Ri=$e("top"),Vi=$e("right"),Ni=$e("bottom"),_i=$e("left"),ki={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Pi={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(e,A){switch(A){case"rtl":return 1;case"ltr":default:return 0}}},Xi={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(e,A){return A.filter(L).reduce(function(t,r){return t|Ji(r.value)},0)}},Ji=function(e){switch(e){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Yi={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Wi={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(e,A){return A.type===20&&A.value==="normal"?0:A.type===17||A.type===15?A.number:0}},Ar;(function(e){e.NORMAL="normal",e.STRICT="strict"})(Ar||(Ar={}));var Zi={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"strict":return Ar.STRICT;case"normal":default:return Ar.NORMAL}}},qi={name:"line-height",initialValue:"normal",prefix:!1,type:4},fn=function(e,A){return L(e)&&e.value==="normal"?1.2*A:e.type===17?A*e.number:R(e)?x(e,A):A},ji={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(e,A){return A.type===20&&A.value==="none"?null:Vr.parse(e,A)}},zi={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(e,A){switch(A){case"inside":return 0;case"outside":default:return 1}}},Nr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},er=function(e){return{name:"margin-"+e,initialValue:"0",prefix:!1,type:4}},$i=er("top"),Aa=er("right"),ea=er("bottom"),ra=er("left"),ta={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(e,A){return A.filter(L).map(function(t){switch(t.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},na={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-word":return"break-word";case"normal":default:return"normal"}}},rr=function(e){return{name:"padding-"+e,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Ba=rr("top"),sa=rr("right"),ia=rr("bottom"),aa=rr("left"),oa={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(e,A){switch(A){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},ca={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(e,A){switch(A){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},ga={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Mr(A[0],"none")?[]:oA(A).map(function(t){for(var r={color:uA.TRANSPARENT,offsetX:k,offsetY:k,blur:k},n=0,B=0;B<t.length;B++){var s=t[B];dA(s)?(n===0?r.offsetX=s:n===1?r.offsetY=s:r.blur=s,n++):r.color=pA.parse(e,s)}return r})}},Qa={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},wa={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(e,A){if(A.type===20&&A.value==="none")return null;if(A.type===18){var t=fa[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return t(A.values)}return null}},la=function(e){var A=e.filter(function(t){return t.type===17}).map(function(t){return t.number});return A.length===6?A:null},ua=function(e){var A=e.filter(function(a){return a.type===17}).map(function(a){return a.number}),t=A[0],r=A[1];A[2],A[3];var n=A[4],B=A[5];A[6],A[7],A[8],A[9],A[10],A[11];var s=A[12],i=A[13];return A[14],A[15],A.length===16?[t,r,n,B,s,i]:null},fa={matrix:la,matrix3d:ua},Cn={type:16,number:50,flags:ie},Ca=[Cn,Cn],Ua={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(e,A){var t=A.filter(R);return t.length!==2?Ca:[t[0],t[1]]}},Fa={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},ue;(function(e){e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all"})(ue||(ue={}));for(var ha={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-all":return ue.BREAK_ALL;case"keep-all":return ue.KEEP_ALL;case"normal":default:return ue.NORMAL}}},da={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(e,A){if(A.type===20)return{auto:!0,order:0};if(PA(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},Un={name:"time",parse:function(e,A){if(A.type===15)switch(A.unit.toLowerCase()){case"s":return 1e3*A.number;case"ms":return A.number}throw new Error("Unsupported time type")}},Ea={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(e,A){return PA(A)?A.number:1}},pa={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Ha={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(e,A){return A.filter(L).map(function(t){switch(t.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(t){return t!==0})}},Ia={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(e,A){var t=[],r=[];return A.forEach(function(n){switch(n.type){case 20:case 0:t.push(n.value);break;case 17:t.push(n.number.toString());break;case 4:r.push(t.join(" ")),t.length=0;break}}),t.length&&r.push(t.join(" ")),r.map(function(n){return n.indexOf(" ")===-1?n:"'"+n+"'"})}},va={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},ma={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(e,A){if(PA(A))return A.number;if(L(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},ya={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.filter(L).map(function(t){return t.value})}},Ka={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},N=function(e,A){return(e&A)!==0},La={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var t=A[0];return t.type===20&&t.value==="none"?[]:A}},ba={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var t=A[0];if(t.type===20&&t.value==="none")return null;for(var r=[],n=A.filter(Yt),B=0;B<n.length;B++){var s=n[B],i=n[B+1];if(s.type===20){var a=i&&PA(i)?i.number:1;r.push({counter:s.value,increment:a})}}return r}},Da={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return[];for(var t=[],r=A.filter(Yt),n=0;n<r.length;n++){var B=r[n],s=r[n+1];if(L(B)&&B.value!=="none"){var i=s&&PA(s)?s.number:0;t.push({counter:B.value,reset:i})}}return t}},xa={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(Qe).map(function(t){return Un.parse(e,t)})}},Ta={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var t=A[0];if(t.type===20&&t.value==="none")return null;var r=[],n=A.filter(si);if(n.length%2!==0)return null;for(var B=0;B<n.length;B+=2){var s=n[B].value,i=n[B+1].value;r.push({open:s,close:i})}return r}},Fn=function(e,A,t){if(!e)return"";var r=e[Math.min(A,e.length-1)];return r?t?r.open:r.close:""},Sa={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Mr(A[0],"none")?[]:oA(A).map(function(t){for(var r={color:255,offsetX:k,offsetY:k,blur:k,spread:k,inset:!1},n=0,B=0;B<t.length;B++){var s=t[B];Mr(s,"inset")?r.inset=!0:dA(s)?(n===0?r.offsetX=s:n===1?r.offsetY=s:n===2?r.blur=s:r.spread=s,n++):r.color=pA.parse(e,s)}return r})}},Oa={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(e,A){var t=[0,1,2],r=[];return A.filter(L).forEach(function(n){switch(n.value){case"stroke":r.push(1);break;case"fill":r.push(0);break;case"markers":r.push(2);break}}),t.forEach(function(n){r.indexOf(n)===-1&&r.push(n)}),r}},Ma={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Ga={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return Qe(A)?A.number:0}},Ra=function(){function e(A,t){var r,n;this.animationDuration=U(A,xa,t.animationDuration),this.backgroundClip=U(A,oi,t.backgroundClip),this.backgroundColor=U(A,ci,t.backgroundColor),this.backgroundImage=U(A,hi,t.backgroundImage),this.backgroundOrigin=U(A,di,t.backgroundOrigin),this.backgroundPosition=U(A,Ei,t.backgroundPosition),this.backgroundRepeat=U(A,pi,t.backgroundRepeat),this.backgroundSize=U(A,Ii,t.backgroundSize),this.borderTopColor=U(A,mi,t.borderTopColor),this.borderRightColor=U(A,yi,t.borderRightColor),this.borderBottomColor=U(A,Ki,t.borderBottomColor),this.borderLeftColor=U(A,Li,t.borderLeftColor),this.borderTopLeftRadius=U(A,bi,t.borderTopLeftRadius),this.borderTopRightRadius=U(A,Di,t.borderTopRightRadius),this.borderBottomRightRadius=U(A,xi,t.borderBottomRightRadius),this.borderBottomLeftRadius=U(A,Ti,t.borderBottomLeftRadius),this.borderTopStyle=U(A,Si,t.borderTopStyle),this.borderRightStyle=U(A,Oi,t.borderRightStyle),this.borderBottomStyle=U(A,Mi,t.borderBottomStyle),this.borderLeftStyle=U(A,Gi,t.borderLeftStyle),this.borderTopWidth=U(A,Ri,t.borderTopWidth),this.borderRightWidth=U(A,Vi,t.borderRightWidth),this.borderBottomWidth=U(A,Ni,t.borderBottomWidth),this.borderLeftWidth=U(A,_i,t.borderLeftWidth),this.boxShadow=U(A,Sa,t.boxShadow),this.color=U(A,ki,t.color),this.direction=U(A,Pi,t.direction),this.display=U(A,Xi,t.display),this.float=U(A,Yi,t.cssFloat),this.fontFamily=U(A,Ia,t.fontFamily),this.fontSize=U(A,va,t.fontSize),this.fontStyle=U(A,Ka,t.fontStyle),this.fontVariant=U(A,ya,t.fontVariant),this.fontWeight=U(A,ma,t.fontWeight),this.letterSpacing=U(A,Wi,t.letterSpacing),this.lineBreak=U(A,Zi,t.lineBreak),this.lineHeight=U(A,qi,t.lineHeight),this.listStyleImage=U(A,ji,t.listStyleImage),this.listStylePosition=U(A,zi,t.listStylePosition),this.listStyleType=U(A,Nr,t.listStyleType),this.marginTop=U(A,$i,t.marginTop),this.marginRight=U(A,Aa,t.marginRight),this.marginBottom=U(A,ea,t.marginBottom),this.marginLeft=U(A,ra,t.marginLeft),this.opacity=U(A,Ea,t.opacity);var B=U(A,ta,t.overflow);this.overflowX=B[0],this.overflowY=B[B.length>1?1:0],this.overflowWrap=U(A,na,t.overflowWrap),this.paddingTop=U(A,Ba,t.paddingTop),this.paddingRight=U(A,sa,t.paddingRight),this.paddingBottom=U(A,ia,t.paddingBottom),this.paddingLeft=U(A,aa,t.paddingLeft),this.paintOrder=U(A,Oa,t.paintOrder),this.position=U(A,ca,t.position),this.textAlign=U(A,oa,t.textAlign),this.textDecorationColor=U(A,pa,(r=t.textDecorationColor)!==null&&r!==void 0?r:t.color),this.textDecorationLine=U(A,Ha,(n=t.textDecorationLine)!==null&&n!==void 0?n:t.textDecoration),this.textShadow=U(A,ga,t.textShadow),this.textTransform=U(A,Qa,t.textTransform),this.transform=U(A,wa,t.transform),this.transformOrigin=U(A,Ua,t.transformOrigin),this.visibility=U(A,Fa,t.visibility),this.webkitTextStrokeColor=U(A,Ma,t.webkitTextStrokeColor),this.webkitTextStrokeWidth=U(A,Ga,t.webkitTextStrokeWidth),this.wordBreak=U(A,ha,t.wordBreak),this.zIndex=U(A,da,t.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},e.prototype.isTransparent=function(){return HA(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==0},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==0},e.prototype.isInlineLevel=function(){return N(this.display,4)||N(this.display,33554432)||N(this.display,268435456)||N(this.display,536870912)||N(this.display,67108864)||N(this.display,134217728)},e}(),Va=function(){function e(A,t){this.content=U(A,La,t.content),this.quotes=U(A,Ta,t.quotes)}return e}(),hn=function(){function e(A,t){this.counterIncrement=U(A,ba,t.counterIncrement),this.counterReset=U(A,Da,t.counterReset)}return e}(),U=function(e,A,t){var r=new Xt,n=t!==null&&typeof t<"u"?t.toString():A.initialValue;r.write(n);var B=new Jt(r.read());switch(A.type){case 2:var s=B.parseComponentValue();return A.parse(e,L(s)?s.value:A.initialValue);case 0:return A.parse(e,B.parseComponentValue());case 1:return A.parse(e,B.parseComponentValues());case 4:return B.parseComponentValue();case 3:switch(A.format){case"angle":return Je.parse(e,B.parseComponentValue());case"color":return pA.parse(e,B.parseComponentValue());case"image":return Vr.parse(e,B.parseComponentValue());case"length":var i=B.parseComponentValue();return dA(i)?i:k;case"length-percentage":var a=B.parseComponentValue();return R(a)?a:k;case"time":return Un.parse(e,B.parseComponentValue())}break}},Na="data-html2canvas-debug",_a=function(e){var A=e.getAttribute(Na);switch(A){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},_r=function(e,A){var t=_a(e);return t===1||A===t},cA=function(){function e(A,t){if(this.context=A,this.textNodes=[],this.elements=[],this.flags=0,_r(t,3))debugger;this.styles=new Ra(A,window.getComputedStyle(t,null)),rt(t)&&(this.styles.animationDuration.some(function(r){return r>0})&&(t.style.animationDuration="0s"),this.styles.transform!==null&&(t.style.transform="none")),this.bounds=ve(this.context,t),_r(t,4)&&(this.flags|=16)}return e}(),ka="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",dn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",fe=typeof Uint8Array>"u"?[]:new Uint8Array(256),tr=0;tr<dn.length;tr++)fe[dn.charCodeAt(tr)]=tr;for(var Pa=function(e){var A=e.length*.75,t=e.length,r,n=0,B,s,i,a;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var o=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),c=Array.isArray(o)?o:new Uint8Array(o);for(r=0;r<t;r+=4)B=fe[e.charCodeAt(r)],s=fe[e.charCodeAt(r+1)],i=fe[e.charCodeAt(r+2)],a=fe[e.charCodeAt(r+3)],c[n++]=B<<2|s>>4,c[n++]=(s&15)<<4|i>>2,c[n++]=(i&3)<<6|a&63;return o},Xa=function(e){for(var A=e.length,t=[],r=0;r<A;r+=2)t.push(e[r+1]<<8|e[r]);return t},Ja=function(e){for(var A=e.length,t=[],r=0;r<A;r+=4)t.push(e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]);return t},xA=5,kr=6+5,Pr=2,Ya=kr-xA,En=65536>>xA,Wa=1<<xA,Xr=Wa-1,Za=1024>>xA,qa=En+Za,ja=qa,za=32,$a=ja+za,Ao=65536>>kr,eo=1<<Ya,ro=eo-1,pn=function(e,A,t){return e.slice?e.slice(A,t):new Uint16Array(Array.prototype.slice.call(e,A,t))},to=function(e,A,t){return e.slice?e.slice(A,t):new Uint32Array(Array.prototype.slice.call(e,A,t))},no=function(e,A){var t=Pa(e),r=Array.isArray(t)?Ja(t):new Uint32Array(t),n=Array.isArray(t)?Xa(t):new Uint16Array(t),B=24,s=pn(n,B/2,r[4]/2),i=r[5]===2?pn(n,(B+r[4])/2):to(r,Math.ceil((B+r[4])/4));return new Bo(r[0],r[1],r[2],r[3],s,i)},Bo=function(){function e(A,t,r,n,B,s){this.initialValue=A,this.errorValue=t,this.highStart=r,this.highValueIndex=n,this.index=B,this.data=s}return e.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>xA],t=(t<<Pr)+(A&Xr),this.data[t];if(A<=65535)return t=this.index[En+(A-55296>>xA)],t=(t<<Pr)+(A&Xr),this.data[t];if(A<this.highStart)return t=$a-Ao+(A>>kr),t=this.index[t],t+=A>>xA&ro,t=this.index[t],t=(t<<Pr)+(A&Xr),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),Hn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",so=typeof Uint8Array>"u"?[]:new Uint8Array(256),nr=0;nr<Hn.length;nr++)so[Hn.charCodeAt(nr)]=nr;var io=1,Jr=2,Yr=3,In=4,vn=5,ao=7,mn=8,Wr=9,Zr=10,yn=11,Kn=12,Ln=13,bn=14,qr=15,oo=function(e){for(var A=[],t=0,r=e.length;t<r;){var n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var B=e.charCodeAt(t++);(B&64512)===56320?A.push(((n&1023)<<10)+(B&1023)+65536):(A.push(n),t--)}else A.push(n)}return A},co=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var t=e.length;if(!t)return"";for(var r=[],n=-1,B="";++n<t;){var s=e[n];s<=65535?r.push(s):(s-=65536,r.push((s>>10)+55296,s%1024+56320)),(n+1===t||r.length>16384)&&(B+=String.fromCharCode.apply(String,r),r.length=0)}return B},go=no(ka),nA="\xD7",jr="\xF7",Qo=function(e){return go.get(e)},wo=function(e,A,t){var r=t-2,n=A[r],B=A[t-1],s=A[t];if(B===Jr&&s===Yr)return nA;if(B===Jr||B===Yr||B===In||s===Jr||s===Yr||s===In)return jr;if(B===mn&&[mn,Wr,yn,Kn].indexOf(s)!==-1||(B===yn||B===Wr)&&(s===Wr||s===Zr)||(B===Kn||B===Zr)&&s===Zr||s===Ln||s===vn||s===ao||B===io)return nA;if(B===Ln&&s===bn){for(;n===vn;)n=A[--r];if(n===bn)return nA}if(B===qr&&s===qr){for(var i=0;n===qr;)i++,n=A[--r];if(i%2===0)return nA}return jr},lo=function(e){var A=oo(e),t=A.length,r=0,n=0,B=A.map(Qo);return{next:function(){if(r>=t)return{done:!0,value:null};for(var s=nA;r<t&&(s=wo(A,B,++r))===nA;);if(s!==nA||r===t){var i=co.apply(null,A.slice(n,r));return n=r,{value:i,done:!1}}return{done:!0,value:null}}}},uo=function(e){for(var A=lo(e),t=[],r;!(r=A.next()).done;)r.value&&t.push(r.value.slice());return t},fo=function(e){var A=123;if(e.createRange){var t=e.createRange();if(t.getBoundingClientRect){var r=e.createElement("boundtest");r.style.height=A+"px",r.style.display="block",e.body.appendChild(r),t.selectNode(r);var n=t.getBoundingClientRect(),B=Math.round(n.height);if(e.body.removeChild(r),B===A)return!0}}return!1},Co=function(e){var A=e.createElement("boundtest");A.style.width="50px",A.style.display="block",A.style.fontSize="12px",A.style.letterSpacing="0px",A.style.wordSpacing="0px",e.body.appendChild(A);var t=e.createRange();A.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var r=A.firstChild,n=me(r.data).map(function(a){return M(a)}),B=0,s={},i=n.every(function(a,o){t.setStart(r,B),t.setEnd(r,B+a.length);var c=t.getBoundingClientRect();B+=a.length;var g=c.x>s.x||c.y>s.y;return s=c,o===0?!0:g});return e.body.removeChild(A),i},Uo=function(){return typeof new Image().crossOrigin<"u"},Fo=function(){return typeof new XMLHttpRequest().responseType=="string"},ho=function(e){var A=new Image,t=e.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;A.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(A,0,0),t.toDataURL()}catch{return!1}return!0},Dn=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},Eo=function(e){var A=e.createElement("canvas"),t=100;A.width=t,A.height=t;var r=A.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,B=A.toDataURL();n.src=B;var s=zr(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),xn(s).then(function(i){r.drawImage(i,0,0);var a=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var o=e.createElement("div");return o.style.backgroundImage="url("+B+")",o.style.height=t+"px",Dn(a)?xn(zr(t,t,0,0,o)):Promise.reject(!1)}).then(function(i){return r.drawImage(i,0,0),Dn(r.getImageData(0,0,t,t).data)}).catch(function(){return!1})},zr=function(e,A,t,r,n){var B="http://www.w3.org/2000/svg",s=document.createElementNS(B,"svg"),i=document.createElementNS(B,"foreignObject");return s.setAttributeNS(null,"width",e.toString()),s.setAttributeNS(null,"height",A.toString()),i.setAttributeNS(null,"width","100%"),i.setAttributeNS(null,"height","100%"),i.setAttributeNS(null,"x",t.toString()),i.setAttributeNS(null,"y",r.toString()),i.setAttributeNS(null,"externalResourcesRequired","true"),s.appendChild(i),i.appendChild(n),s},xn=function(e){return new Promise(function(A,t){var r=new Image;r.onload=function(){return A(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},P={get SUPPORT_RANGE_BOUNDS(){var e=fo(document);return Object.defineProperty(P,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_WORD_BREAKING(){var e=P.SUPPORT_RANGE_BOUNDS&&Co(document);return Object.defineProperty(P,"SUPPORT_WORD_BREAKING",{value:e}),e},get SUPPORT_SVG_DRAWING(){var e=ho(document);return Object.defineProperty(P,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){var e=typeof Array.from=="function"&&typeof window.fetch=="function"?Eo(document):Promise.resolve(!1);return Object.defineProperty(P,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){var e=Uo();return Object.defineProperty(P,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){var e=Fo();return Object.defineProperty(P,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){var e="withCredentials"in new XMLHttpRequest;return Object.defineProperty(P,"SUPPORT_CORS_XHR",{value:e}),e},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var e=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(P,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:e}),e}},Ce=function(){function e(A,t){this.text=A,this.bounds=t}return e}(),po=function(e,A,t,r){var n=vo(A,t),B=[],s=0;return n.forEach(function(i){if(t.textDecorationLine.length||i.trim().length>0)if(P.SUPPORT_RANGE_BOUNDS){var a=Tn(r,s,i.length).getClientRects();if(a.length>1){var o=$r(i),c=0;o.forEach(function(Q){B.push(new Ce(Q,wA.fromDOMRectList(e,Tn(r,c+s,Q.length).getClientRects()))),c+=Q.length})}else B.push(new Ce(i,wA.fromDOMRectList(e,a)))}else{var g=r.splitText(i.length);B.push(new Ce(i,Ho(e,r))),r=g}else P.SUPPORT_RANGE_BOUNDS||(r=r.splitText(i.length));s+=i.length}),B},Ho=function(e,A){var t=A.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(A.cloneNode(!0));var n=A.parentNode;if(n){n.replaceChild(r,A);var B=ve(e,r);return r.firstChild&&n.replaceChild(r.firstChild,r),B}}return wA.EMPTY},Tn=function(e,A,t){var r=e.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(e,A),n.setEnd(e,A+t),n},$r=function(e){if(P.SUPPORT_NATIVE_TEXT_SEGMENTATION){var A=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(A.segment(e)).map(function(t){return t.segment})}return uo(e)},Io=function(e,A){if(P.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(e)).map(function(r){return r.segment})}return yo(e,A)},vo=function(e,A){return A.letterSpacing!==0?$r(e):Io(e,A)},mo=[32,160,4961,65792,65793,4153,4241],yo=function(e,A){for(var t=As(e,{lineBreak:A.lineBreak,wordBreak:A.overflowWrap==="break-word"?"break-word":A.wordBreak}),r=[],n,B=function(){if(n.value){var s=n.value.slice(),i=me(s),a="";i.forEach(function(o){mo.indexOf(o)===-1?a+=M(o):(a.length&&r.push(a),r.push(M(o)),a="")}),a.length&&r.push(a)}};!(n=t.next()).done;)B();return r},Ko=function(){function e(A,t,r){this.text=Lo(t.data,r.textTransform),this.textBounds=po(A,this.text,r,t)}return e}(),Lo=function(e,A){switch(A){case 1:return e.toLowerCase();case 3:return e.replace(bo,Do);case 2:return e.toUpperCase();default:return e}},bo=/(^|\s|:|-|\(|\))([a-z])/g,Do=function(e,A,t){return e.length>0?A+t.toUpperCase():e},Sn=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.src=r.currentSrc||r.src,n.intrinsicWidth=r.naturalWidth,n.intrinsicHeight=r.naturalHeight,n.context.cache.addImage(n.src),n}return A}(cA),On=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.canvas=r,n.intrinsicWidth=r.width,n.intrinsicHeight=r.height,n}return A}(cA),Mn=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this,B=new XMLSerializer,s=ve(t,r);return r.setAttribute("width",s.width+"px"),r.setAttribute("height",s.height+"px"),n.svg="data:image/svg+xml,"+encodeURIComponent(B.serializeToString(r)),n.intrinsicWidth=r.width.baseVal.value,n.intrinsicHeight=r.height.baseVal.value,n.context.cache.addImage(n.svg),n}return A}(cA),Gn=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.value=r.value,n}return A}(cA),At=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.start=r.start,n.reversed=typeof r.reversed=="boolean"&&r.reversed===!0,n}return A}(cA),xo=[{type:15,flags:0,unit:"px",number:3}],To=[{type:16,flags:0,number:50}],So=function(e){return e.width>e.height?new wA(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new wA(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},Oo=function(e){var A=e.type===Mo?new Array(e.value.length+1).join("\u2022"):e.value;return A.length===0?e.placeholder||"":A},Br="checkbox",sr="radio",Mo="password",Rn=707406591,et=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this;switch(n.type=r.type.toLowerCase(),n.checked=r.checked,n.value=Oo(r),(n.type===Br||n.type===sr)&&(n.styles.backgroundColor=3739148031,n.styles.borderTopColor=n.styles.borderRightColor=n.styles.borderBottomColor=n.styles.borderLeftColor=2779096575,n.styles.borderTopWidth=n.styles.borderRightWidth=n.styles.borderBottomWidth=n.styles.borderLeftWidth=1,n.styles.borderTopStyle=n.styles.borderRightStyle=n.styles.borderBottomStyle=n.styles.borderLeftStyle=1,n.styles.backgroundClip=[0],n.styles.backgroundOrigin=[0],n.bounds=So(n.bounds)),n.type){case Br:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=xo;break;case sr:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=To;break}return n}return A}(cA),Vn=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this,B=r.options[r.selectedIndex||0];return n.value=B&&B.text||"",n}return A}(cA),Nn=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.value=r.value,n}return A}(cA),_n=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this;n.src=r.src,n.width=parseInt(r.width,10)||0,n.height=parseInt(r.height,10)||0,n.backgroundColor=n.styles.backgroundColor;try{if(r.contentWindow&&r.contentWindow.document&&r.contentWindow.document.documentElement){n.tree=Pn(t,r.contentWindow.document.documentElement);var B=r.contentWindow.document.documentElement?le(t,getComputedStyle(r.contentWindow.document.documentElement).backgroundColor):uA.TRANSPARENT,s=r.contentWindow.document.body?le(t,getComputedStyle(r.contentWindow.document.body).backgroundColor):uA.TRANSPARENT;n.backgroundColor=HA(B)?HA(s)?n.styles.backgroundColor:s:B}}catch{}return n}return A}(cA),Go=["OL","UL","MENU"],ir=function(e,A,t,r){for(var n=A.firstChild,B=void 0;n;n=B)if(B=n.nextSibling,Xn(n)&&n.data.trim().length>0)t.textNodes.push(new Ko(e,n,t.styles));else if(YA(n))if(jn(n)&&n.assignedNodes)n.assignedNodes().forEach(function(i){return ir(e,i,t,r)});else{var s=kn(e,n);s.styles.isVisible()&&(Ro(n,s,r)?s.flags|=4:Vo(s.styles)&&(s.flags|=2),Go.indexOf(n.tagName)!==-1&&(s.flags|=8),t.elements.push(s),n.slot,n.shadowRoot?ir(e,n.shadowRoot,s,r):!or(n)&&!Jn(n)&&!cr(n)&&ir(e,n,s,r))}},kn=function(e,A){return nt(A)?new Sn(e,A):Yn(A)?new On(e,A):Jn(A)?new Mn(e,A):No(A)?new Gn(e,A):_o(A)?new At(e,A):ko(A)?new et(e,A):cr(A)?new Vn(e,A):or(A)?new Nn(e,A):Zn(A)?new _n(e,A):new cA(e,A)},Pn=function(e,A){var t=kn(e,A);return t.flags|=4,ir(e,A,t,t),t},Ro=function(e,A,t){return A.styles.isPositionedWithZIndex()||A.styles.opacity<1||A.styles.isTransformed()||tt(e)&&t.styles.isTransparent()},Vo=function(e){return e.isPositioned()||e.isFloating()},Xn=function(e){return e.nodeType===Node.TEXT_NODE},YA=function(e){return e.nodeType===Node.ELEMENT_NODE},rt=function(e){return YA(e)&&typeof e.style<"u"&&!ar(e)},ar=function(e){return typeof e.className=="object"},No=function(e){return e.tagName==="LI"},_o=function(e){return e.tagName==="OL"},ko=function(e){return e.tagName==="INPUT"},Po=function(e){return e.tagName==="HTML"},Jn=function(e){return e.tagName==="svg"},tt=function(e){return e.tagName==="BODY"},Yn=function(e){return e.tagName==="CANVAS"},Wn=function(e){return e.tagName==="VIDEO"},nt=function(e){return e.tagName==="IMG"},Zn=function(e){return e.tagName==="IFRAME"},qn=function(e){return e.tagName==="STYLE"},Xo=function(e){return e.tagName==="SCRIPT"},or=function(e){return e.tagName==="TEXTAREA"},cr=function(e){return e.tagName==="SELECT"},jn=function(e){return e.tagName==="SLOT"},zn=function(e){return e.tagName.indexOf("-")>0},Jo=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(A){var t=this.counters[A];return t&&t.length?t[t.length-1]:1},e.prototype.getCounterValues=function(A){var t=this.counters[A];return t||[]},e.prototype.pop=function(A){var t=this;A.forEach(function(r){return t.counters[r].pop()})},e.prototype.parse=function(A){var t=this,r=A.counterIncrement,n=A.counterReset,B=!0;r!==null&&r.forEach(function(i){var a=t.counters[i.counter];a&&i.increment!==0&&(B=!1,a.length||a.push(1),a[Math.max(0,a.length-1)]+=i.increment)});var s=[];return B&&n.forEach(function(i){var a=t.counters[i.counter];s.push(i.counter),a||(a=t.counters[i.counter]=[]),a.push(i.reset)}),s},e}(),$n={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},AB={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u0554","\u0553","\u0552","\u0551","\u0550","\u054F","\u054E","\u054D","\u054C","\u054B","\u054A","\u0549","\u0548","\u0547","\u0546","\u0545","\u0544","\u0543","\u0542","\u0541","\u0540","\u053F","\u053E","\u053D","\u053C","\u053B","\u053A","\u0539","\u0538","\u0537","\u0536","\u0535","\u0534","\u0533","\u0532","\u0531"]},Yo={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["\u05D9\u05F3","\u05D8\u05F3","\u05D7\u05F3","\u05D6\u05F3","\u05D5\u05F3","\u05D4\u05F3","\u05D3\u05F3","\u05D2\u05F3","\u05D1\u05F3","\u05D0\u05F3","\u05EA","\u05E9","\u05E8","\u05E7","\u05E6","\u05E4","\u05E2","\u05E1","\u05E0","\u05DE","\u05DC","\u05DB","\u05D9\u05D8","\u05D9\u05D7","\u05D9\u05D6","\u05D8\u05D6","\u05D8\u05D5","\u05D9","\u05D8","\u05D7","\u05D6","\u05D5","\u05D4","\u05D3","\u05D2","\u05D1","\u05D0"]},Wo={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u10F5","\u10F0","\u10EF","\u10F4","\u10EE","\u10ED","\u10EC","\u10EB","\u10EA","\u10E9","\u10E8","\u10E7","\u10E6","\u10E5","\u10E4","\u10F3","\u10E2","\u10E1","\u10E0","\u10DF","\u10DE","\u10DD","\u10F2","\u10DC","\u10DB","\u10DA","\u10D9","\u10D8","\u10D7","\u10F1","\u10D6","\u10D5","\u10D4","\u10D3","\u10D2","\u10D1","\u10D0"]},WA=function(e,A,t,r,n,B){return e<A||e>t?Fe(e,n,B.length>0):r.integers.reduce(function(s,i,a){for(;e>=i;)e-=i,s+=r.values[a];return s},"")+B},eB=function(e,A,t,r){var n="";do t||e--,n=r(e)+n,e/=A;while(e*A>=A);return n},G=function(e,A,t,r,n){var B=t-A+1;return(e<0?"-":"")+(eB(Math.abs(e),B,r,function(s){return M(Math.floor(s%B)+A)})+n)},TA=function(e,A,t){t===void 0&&(t=". ");var r=A.length;return eB(Math.abs(e),r,!1,function(n){return A[Math.floor(n%r)]})+t},ZA=1<<0,vA=1<<1,mA=1<<2,Ue=1<<3,fA=function(e,A,t,r,n,B){if(e<-9999||e>9999)return Fe(e,4,n.length>0);var s=Math.abs(e),i=n;if(s===0)return A[0]+i;for(var a=0;s>0&&a<=4;a++){var o=s%10;o===0&&N(B,ZA)&&i!==""?i=A[o]+i:o>1||o===1&&a===0||o===1&&a===1&&N(B,vA)||o===1&&a===1&&N(B,mA)&&e>100||o===1&&a>1&&N(B,Ue)?i=A[o]+(a>0?t[a-1]:"")+i:o===1&&a>0&&(i=t[a-1]+i),s=Math.floor(s/10)}return(e<0?r:"")+i},rB="\u5341\u767E\u5343\u842C",tB="\u62FE\u4F70\u4EDF\u842C",nB="\u30DE\u30A4\u30CA\u30B9",Bt="\uB9C8\uC774\uB108\uC2A4",Fe=function(e,A,t){var r=t?". ":"",n=t?"\u3001":"",B=t?", ":"",s=t?" ":"";switch(A){case 0:return"\u2022"+s;case 1:return"\u25E6"+s;case 2:return"\u25FE"+s;case 5:var i=G(e,48,57,!0,r);return i.length<4?"0"+i:i;case 4:return TA(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",n);case 6:return WA(e,1,3999,$n,3,r).toLowerCase();case 7:return WA(e,1,3999,$n,3,r);case 8:return G(e,945,969,!1,r);case 9:return G(e,97,122,!1,r);case 10:return G(e,65,90,!1,r);case 11:return G(e,1632,1641,!0,r);case 12:case 49:return WA(e,1,9999,AB,3,r);case 35:return WA(e,1,9999,AB,3,r).toLowerCase();case 13:return G(e,2534,2543,!0,r);case 14:case 30:return G(e,6112,6121,!0,r);case 15:return TA(e,"\u5B50\u4E11\u5BC5\u536F\u8FB0\u5DF3\u5348\u672A\u7533\u9149\u620C\u4EA5",n);case 16:return TA(e,"\u7532\u4E59\u4E19\u4E01\u620A\u5DF1\u5E9A\u8F9B\u58EC\u7678",n);case 17:case 48:return fA(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",rB,"\u8CA0",n,vA|mA|Ue);case 47:return fA(e,"\u96F6\u58F9\u8CB3\u53C3\u8086\u4F0D\u9678\u67D2\u634C\u7396",tB,"\u8CA0",n,ZA|vA|mA|Ue);case 42:return fA(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",rB,"\u8D1F",n,vA|mA|Ue);case 41:return fA(e,"\u96F6\u58F9\u8D30\u53C1\u8086\u4F0D\u9646\u67D2\u634C\u7396",tB,"\u8D1F",n,ZA|vA|mA|Ue);case 26:return fA(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u4E07",nB,n,0);case 25:return fA(e,"\u96F6\u58F1\u5F10\u53C2\u56DB\u4F0D\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343\u4E07",nB,n,ZA|vA|mA);case 31:return fA(e,"\uC601\uC77C\uC774\uC0BC\uC0AC\uC624\uC721\uCE60\uD314\uAD6C","\uC2ED\uBC31\uCC9C\uB9CC",Bt,B,ZA|vA|mA);case 33:return fA(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u842C",Bt,B,0);case 32:return fA(e,"\u96F6\u58F9\u8CB3\u53C3\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343",Bt,B,ZA|vA|mA);case 18:return G(e,2406,2415,!0,r);case 20:return WA(e,1,19999,Wo,3,r);case 21:return G(e,2790,2799,!0,r);case 22:return G(e,2662,2671,!0,r);case 22:return WA(e,1,10999,Yo,3,r);case 23:return TA(e,"\u3042\u3044\u3046\u3048\u304A\u304B\u304D\u304F\u3051\u3053\u3055\u3057\u3059\u305B\u305D\u305F\u3061\u3064\u3066\u3068\u306A\u306B\u306C\u306D\u306E\u306F\u3072\u3075\u3078\u307B\u307E\u307F\u3080\u3081\u3082\u3084\u3086\u3088\u3089\u308A\u308B\u308C\u308D\u308F\u3090\u3091\u3092\u3093");case 24:return TA(e,"\u3044\u308D\u306F\u306B\u307B\u3078\u3068\u3061\u308A\u306C\u308B\u3092\u308F\u304B\u3088\u305F\u308C\u305D\u3064\u306D\u306A\u3089\u3080\u3046\u3090\u306E\u304A\u304F\u3084\u307E\u3051\u3075\u3053\u3048\u3066\u3042\u3055\u304D\u3086\u3081\u307F\u3057\u3091\u3072\u3082\u305B\u3059");case 27:return G(e,3302,3311,!0,r);case 28:return TA(e,"\u30A2\u30A4\u30A6\u30A8\u30AA\u30AB\u30AD\u30AF\u30B1\u30B3\u30B5\u30B7\u30B9\u30BB\u30BD\u30BF\u30C1\u30C4\u30C6\u30C8\u30CA\u30CB\u30CC\u30CD\u30CE\u30CF\u30D2\u30D5\u30D8\u30DB\u30DE\u30DF\u30E0\u30E1\u30E2\u30E4\u30E6\u30E8\u30E9\u30EA\u30EB\u30EC\u30ED\u30EF\u30F0\u30F1\u30F2\u30F3",n);case 29:return TA(e,"\u30A4\u30ED\u30CF\u30CB\u30DB\u30D8\u30C8\u30C1\u30EA\u30CC\u30EB\u30F2\u30EF\u30AB\u30E8\u30BF\u30EC\u30BD\u30C4\u30CD\u30CA\u30E9\u30E0\u30A6\u30F0\u30CE\u30AA\u30AF\u30E4\u30DE\u30B1\u30D5\u30B3\u30A8\u30C6\u30A2\u30B5\u30AD\u30E6\u30E1\u30DF\u30B7\u30F1\u30D2\u30E2\u30BB\u30B9",n);case 34:return G(e,3792,3801,!0,r);case 37:return G(e,6160,6169,!0,r);case 38:return G(e,4160,4169,!0,r);case 39:return G(e,2918,2927,!0,r);case 40:return G(e,1776,1785,!0,r);case 43:return G(e,3046,3055,!0,r);case 44:return G(e,3174,3183,!0,r);case 45:return G(e,3664,3673,!0,r);case 46:return G(e,3872,3881,!0,r);case 3:default:return G(e,48,57,!0,r)}},BB="data-html2canvas-ignore",sB=function(){function e(A,t,r){if(this.context=A,this.options=r,this.scrolledElements=[],this.referenceElement=t,this.counters=new Jo,this.quoteDepth=0,!t.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(t.ownerDocument.documentElement,!1)}return e.prototype.toIFrame=function(A,t){var r=this,n=Zo(A,t);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var B=A.defaultView.pageXOffset,s=A.defaultView.pageYOffset,i=n.contentWindow,a=i.document,o=zo(n).then(function(){return Z(r,void 0,void 0,function(){var c,g;return J(this,function(Q){switch(Q.label){case 0:return this.scrolledElements.forEach(rc),i&&(i.scrollTo(t.left,t.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(i.scrollY!==t.top||i.scrollX!==t.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(i.scrollX-t.left,i.scrollY-t.top,0,0))),c=this.options.onclone,g=this.clonedReferenceElement,typeof g>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:a.fonts&&a.fonts.ready?[4,a.fonts.ready]:[3,2];case 1:Q.sent(),Q.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,jo(a)]:[3,4];case 3:Q.sent(),Q.label=4;case 4:return typeof c=="function"?[2,Promise.resolve().then(function(){return c(a,g)}).then(function(){return n})]:[2,n]}})})});return a.open(),a.write(Ac(document.doctype)+"<html></html>"),ec(this.referenceElement.ownerDocument,B,s),a.replaceChild(a.adoptNode(this.documentElement),a.documentElement),a.close(),o},e.prototype.createElementClone=function(A){if(_r(A,2))debugger;if(Yn(A))return this.createCanvasClone(A);if(Wn(A))return this.createVideoClone(A);if(qn(A))return this.createStyleClone(A);var t=A.cloneNode(!1);return nt(t)&&(nt(A)&&A.currentSrc&&A.currentSrc!==A.src&&(t.src=A.currentSrc,t.srcset=""),t.loading==="lazy"&&(t.loading="eager")),zn(t)?this.createCustomElementClone(t):t},e.prototype.createCustomElementClone=function(A){var t=document.createElement("html2canvascustomelement");return st(A.style,t),t},e.prototype.createStyleClone=function(A){try{var t=A.sheet;if(t&&t.cssRules){var r=[].slice.call(t.cssRules,0).reduce(function(B,s){return s&&typeof s.cssText=="string"?B+s.cssText:B},""),n=A.cloneNode(!1);return n.textContent=r,n}}catch(B){if(this.context.logger.error("Unable to access cssRules property",B),B.name!=="SecurityError")throw B}return A.cloneNode(!1)},e.prototype.createCanvasClone=function(A){var t;if(this.options.inlineImages&&A.ownerDocument){var r=A.ownerDocument.createElement("img");try{return r.src=A.toDataURL(),r}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var n=A.cloneNode(!1);try{n.width=A.width,n.height=A.height;var B=A.getContext("2d"),s=n.getContext("2d");if(s)if(!this.options.allowTaint&&B)s.putImageData(B.getImageData(0,0,A.width,A.height),0,0);else{var i=(t=A.getContext("webgl2"))!==null&&t!==void 0?t:A.getContext("webgl");if(i){var a=i.getContextAttributes();(a==null?void 0:a.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}s.drawImage(A,0,0)}return n}catch{this.context.logger.info("Unable to clone canvas as it is tainted",A)}return n},e.prototype.createVideoClone=function(A){var t=A.ownerDocument.createElement("canvas");t.width=A.offsetWidth,t.height=A.offsetHeight;var r=t.getContext("2d");try{return r&&(r.drawImage(A,0,0,t.width,t.height),this.options.allowTaint||r.getImageData(0,0,t.width,t.height)),t}catch{this.context.logger.info("Unable to clone video as it is tainted",A)}var n=A.ownerDocument.createElement("canvas");return n.width=A.offsetWidth,n.height=A.offsetHeight,n},e.prototype.appendChildNode=function(A,t,r){(!YA(t)||!Xo(t)&&!t.hasAttribute(BB)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(t)))&&(!this.options.copyStyles||!YA(t)||!qn(t))&&A.appendChild(this.cloneNode(t,r))},e.prototype.cloneChildNodes=function(A,t,r){for(var n=this,B=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;B;B=B.nextSibling)if(YA(B)&&jn(B)&&typeof B.assignedNodes=="function"){var s=B.assignedNodes();s.length&&s.forEach(function(i){return n.appendChildNode(t,i,r)})}else this.appendChildNode(t,B,r)},e.prototype.cloneNode=function(A,t){if(Xn(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var r=A.ownerDocument.defaultView;if(r&&YA(A)&&(rt(A)||ar(A))){var n=this.createElementClone(A);n.style.transitionProperty="none";var B=r.getComputedStyle(A),s=r.getComputedStyle(A,":before"),i=r.getComputedStyle(A,":after");this.referenceElement===A&&rt(n)&&(this.clonedReferenceElement=n),tt(n)&&Bc(n);var a=this.counters.parse(new hn(this.context,B)),o=this.resolvePseudoContent(A,n,s,he.BEFORE);zn(A)&&(t=!0),Wn(A)||this.cloneChildNodes(A,n,t),o&&n.insertBefore(o,n.firstChild);var c=this.resolvePseudoContent(A,n,i,he.AFTER);return c&&n.appendChild(c),this.counters.pop(a),(B&&(this.options.copyStyles||ar(A))&&!Zn(A)||t)&&st(B,n),(A.scrollTop!==0||A.scrollLeft!==0)&&this.scrolledElements.push([n,A.scrollLeft,A.scrollTop]),(or(A)||cr(A))&&(or(n)||cr(n))&&(n.value=A.value),n}return A.cloneNode(!1)},e.prototype.resolvePseudoContent=function(A,t,r,n){var B=this;if(!!r){var s=r.content,i=t.ownerDocument;if(!(!i||!s||s==="none"||s==="-moz-alt-content"||r.display==="none")){this.counters.parse(new hn(this.context,r));var a=new Va(this.context,r),o=i.createElement("html2canvaspseudoelement");st(r,o),a.content.forEach(function(g){if(g.type===0)o.appendChild(i.createTextNode(g.value));else if(g.type===22){var Q=i.createElement("img");Q.src=g.value,Q.style.opacity="1",o.appendChild(Q)}else if(g.type===18){if(g.name==="attr"){var f=g.values.filter(L);f.length&&o.appendChild(i.createTextNode(A.getAttribute(f[0].value)||""))}else if(g.name==="counter"){var w=g.values.filter(XA),l=w[0],p=w[1];if(l&&L(l)){var h=B.counters.getCounterValue(l.value),F=p&&L(p)?Nr.parse(B.context,p.value):3;o.appendChild(i.createTextNode(Fe(h,F,!1)))}}else if(g.name==="counters"){var y=g.values.filter(XA),l=y[0],H=y[1],p=y[2];if(l&&L(l)){var d=B.counters.getCounterValues(l.value),C=p&&L(p)?Nr.parse(B.context,p.value):3,v=H&&H.type===0?H.value:"",m=d.map(function(j){return Fe(j,C,!1)}).join(v);o.appendChild(i.createTextNode(m))}}}else if(g.type===20)switch(g.value){case"open-quote":o.appendChild(i.createTextNode(Fn(a.quotes,B.quoteDepth++,!0)));break;case"close-quote":o.appendChild(i.createTextNode(Fn(a.quotes,--B.quoteDepth,!1)));break;default:o.appendChild(i.createTextNode(g.value))}}),o.className=it+" "+at;var c=n===he.BEFORE?" "+it:" "+at;return ar(t)?t.className.baseValue+=c:t.className+=c,o}}},e.destroy=function(A){return A.parentNode?(A.parentNode.removeChild(A),!0):!1},e}(),he;(function(e){e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER"})(he||(he={}));var Zo=function(e,A){var t=e.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=A.width.toString(),t.height=A.height.toString(),t.scrolling="no",t.setAttribute(BB,"true"),e.body.appendChild(t),t},qo=function(e){return new Promise(function(A){if(e.complete){A();return}if(!e.src){A();return}e.onload=A,e.onerror=A})},jo=function(e){return Promise.all([].slice.call(e.images,0).map(qo))},zo=function(e){return new Promise(function(A,t){var r=e.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=e.onload=function(){r.onload=e.onload=null;var B=setInterval(function(){n.body.childNodes.length>0&&n.readyState==="complete"&&(clearInterval(B),A(e))},50)}})},$o=["all","d","content"],st=function(e,A){for(var t=e.length-1;t>=0;t--){var r=e.item(t);$o.indexOf(r)===-1&&A.style.setProperty(r,e.getPropertyValue(r))}return A},Ac=function(e){var A="";return e&&(A+="<!DOCTYPE ",e.name&&(A+=e.name),e.internalSubset&&(A+=e.internalSubset),e.publicId&&(A+='"'+e.publicId+'"'),e.systemId&&(A+='"'+e.systemId+'"'),A+=">"),A},ec=function(e,A,t){e&&e.defaultView&&(A!==e.defaultView.pageXOffset||t!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(A,t)},rc=function(e){var A=e[0],t=e[1],r=e[2];A.scrollLeft=t,A.scrollTop=r},tc=":before",nc=":after",it="___html2canvas___pseudoelement_before",at="___html2canvas___pseudoelement_after",iB=`{
    content: "" !important;
    display: none !important;
}`,Bc=function(e){sc(e,"."+it+tc+iB+`
         .`+at+nc+iB)},sc=function(e,A){var t=e.ownerDocument;if(t){var r=t.createElement("style");r.textContent=A,e.appendChild(r)}},aB=function(){function e(){}return e.getOrigin=function(A){var t=e._link;return t?(t.href=A,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},e.isSameOrigin=function(A){return e.getOrigin(A)===e._origin},e.setContext=function(A){e._link=A.document.createElement("a"),e._origin=e.getOrigin(A.location.href)},e._origin="about:blank",e}(),ic=function(){function e(A,t){this.context=A,this._options=t,this._cache={}}return e.prototype.addImage=function(A){var t=Promise.resolve();return this.has(A)||(ct(A)||gc(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),t},e.prototype.match=function(A){return this._cache[A]},e.prototype.loadImage=function(A){return Z(this,void 0,void 0,function(){var t,r,n,B,s=this;return J(this,function(i){switch(i.label){case 0:return t=aB.isSameOrigin(A),r=!ot(A)&&this._options.useCORS===!0&&P.SUPPORT_CORS_IMAGES&&!t,n=!ot(A)&&!t&&!ct(A)&&typeof this._options.proxy=="string"&&P.SUPPORT_CORS_XHR&&!r,!t&&this._options.allowTaint===!1&&!ot(A)&&!ct(A)&&!n&&!r?[2]:(B=A,n?[4,this.proxy(B)]:[3,2]);case 1:B=i.sent(),i.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise(function(a,o){var c=new Image;c.onload=function(){return a(c)},c.onerror=o,(Qc(B)||r)&&(c.crossOrigin="anonymous"),c.src=B,c.complete===!0&&setTimeout(function(){return a(c)},500),s._options.imageTimeout>0&&setTimeout(function(){return o("Timed out ("+s._options.imageTimeout+"ms) loading image")},s._options.imageTimeout)})];case 3:return[2,i.sent()]}})})},e.prototype.has=function(A){return typeof this._cache[A]<"u"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(A){var t=this,r=this._options.proxy;if(!r)throw new Error("No proxy defined");var n=A.substring(0,256);return new Promise(function(B,s){var i=P.SUPPORT_RESPONSE_TYPE?"blob":"text",a=new XMLHttpRequest;a.onload=function(){if(a.status===200)if(i==="text")B(a.response);else{var g=new FileReader;g.addEventListener("load",function(){return B(g.result)},!1),g.addEventListener("error",function(Q){return s(Q)},!1),g.readAsDataURL(a.response)}else s("Failed to proxy resource "+n+" with status code "+a.status)},a.onerror=s;var o=r.indexOf("?")>-1?"&":"?";if(a.open("GET",""+r+o+"url="+encodeURIComponent(A)+"&responseType="+i),i!=="text"&&a instanceof XMLHttpRequest&&(a.responseType=i),t._options.imageTimeout){var c=t._options.imageTimeout;a.timeout=c,a.ontimeout=function(){return s("Timed out ("+c+"ms) proxying "+n)}}a.send()})},e}(),ac=/^data:image\/svg\+xml/i,oc=/^data:image\/.*;base64,/i,cc=/^data:image\/.*/i,gc=function(e){return P.SUPPORT_SVG_DRAWING||!wc(e)},ot=function(e){return cc.test(e)},Qc=function(e){return oc.test(e)},ct=function(e){return e.substr(0,4)==="blob"},wc=function(e){return e.substr(-3).toLowerCase()==="svg"||ac.test(e)},u=function(){function e(A,t){this.type=0,this.x=A,this.y=t}return e.prototype.add=function(A,t){return new e(this.x+A,this.y+t)},e}(),qA=function(e,A,t){return new u(e.x+(A.x-e.x)*t,e.y+(A.y-e.y)*t)},gr=function(){function e(A,t,r,n){this.type=1,this.start=A,this.startControl=t,this.endControl=r,this.end=n}return e.prototype.subdivide=function(A,t){var r=qA(this.start,this.startControl,A),n=qA(this.startControl,this.endControl,A),B=qA(this.endControl,this.end,A),s=qA(r,n,A),i=qA(n,B,A),a=qA(s,i,A);return t?new e(this.start,r,s,a):new e(a,i,B,this.end)},e.prototype.add=function(A,t){return new e(this.start.add(A,t),this.startControl.add(A,t),this.endControl.add(A,t),this.end.add(A,t))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),BA=function(e){return e.type===1},lc=function(){function e(A){var t=A.styles,r=A.bounds,n=we(t.borderTopLeftRadius,r.width,r.height),B=n[0],s=n[1],i=we(t.borderTopRightRadius,r.width,r.height),a=i[0],o=i[1],c=we(t.borderBottomRightRadius,r.width,r.height),g=c[0],Q=c[1],f=we(t.borderBottomLeftRadius,r.width,r.height),w=f[0],l=f[1],p=[];p.push((B+a)/r.width),p.push((w+g)/r.width),p.push((s+l)/r.height),p.push((o+Q)/r.height);var h=Math.max.apply(Math,p);h>1&&(B/=h,s/=h,a/=h,o/=h,g/=h,Q/=h,w/=h,l/=h);var F=r.width-a,y=r.height-Q,H=r.width-g,d=r.height-l,C=t.borderTopWidth,v=t.borderRightWidth,m=t.borderBottomWidth,E=t.borderLeftWidth,V=x(t.paddingTop,A.bounds.width),j=x(t.paddingRight,A.bounds.width),rA=x(t.paddingBottom,A.bounds.width),b=x(t.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=B>0||s>0?T(r.left+E/3,r.top+C/3,B-E/3,s-C/3,K.TOP_LEFT):new u(r.left+E/3,r.top+C/3),this.topRightBorderDoubleOuterBox=B>0||s>0?T(r.left+F,r.top+C/3,a-v/3,o-C/3,K.TOP_RIGHT):new u(r.left+r.width-v/3,r.top+C/3),this.bottomRightBorderDoubleOuterBox=g>0||Q>0?T(r.left+H,r.top+y,g-v/3,Q-m/3,K.BOTTOM_RIGHT):new u(r.left+r.width-v/3,r.top+r.height-m/3),this.bottomLeftBorderDoubleOuterBox=w>0||l>0?T(r.left+E/3,r.top+d,w-E/3,l-m/3,K.BOTTOM_LEFT):new u(r.left+E/3,r.top+r.height-m/3),this.topLeftBorderDoubleInnerBox=B>0||s>0?T(r.left+E*2/3,r.top+C*2/3,B-E*2/3,s-C*2/3,K.TOP_LEFT):new u(r.left+E*2/3,r.top+C*2/3),this.topRightBorderDoubleInnerBox=B>0||s>0?T(r.left+F,r.top+C*2/3,a-v*2/3,o-C*2/3,K.TOP_RIGHT):new u(r.left+r.width-v*2/3,r.top+C*2/3),this.bottomRightBorderDoubleInnerBox=g>0||Q>0?T(r.left+H,r.top+y,g-v*2/3,Q-m*2/3,K.BOTTOM_RIGHT):new u(r.left+r.width-v*2/3,r.top+r.height-m*2/3),this.bottomLeftBorderDoubleInnerBox=w>0||l>0?T(r.left+E*2/3,r.top+d,w-E*2/3,l-m*2/3,K.BOTTOM_LEFT):new u(r.left+E*2/3,r.top+r.height-m*2/3),this.topLeftBorderStroke=B>0||s>0?T(r.left+E/2,r.top+C/2,B-E/2,s-C/2,K.TOP_LEFT):new u(r.left+E/2,r.top+C/2),this.topRightBorderStroke=B>0||s>0?T(r.left+F,r.top+C/2,a-v/2,o-C/2,K.TOP_RIGHT):new u(r.left+r.width-v/2,r.top+C/2),this.bottomRightBorderStroke=g>0||Q>0?T(r.left+H,r.top+y,g-v/2,Q-m/2,K.BOTTOM_RIGHT):new u(r.left+r.width-v/2,r.top+r.height-m/2),this.bottomLeftBorderStroke=w>0||l>0?T(r.left+E/2,r.top+d,w-E/2,l-m/2,K.BOTTOM_LEFT):new u(r.left+E/2,r.top+r.height-m/2),this.topLeftBorderBox=B>0||s>0?T(r.left,r.top,B,s,K.TOP_LEFT):new u(r.left,r.top),this.topRightBorderBox=a>0||o>0?T(r.left+F,r.top,a,o,K.TOP_RIGHT):new u(r.left+r.width,r.top),this.bottomRightBorderBox=g>0||Q>0?T(r.left+H,r.top+y,g,Q,K.BOTTOM_RIGHT):new u(r.left+r.width,r.top+r.height),this.bottomLeftBorderBox=w>0||l>0?T(r.left,r.top+d,w,l,K.BOTTOM_LEFT):new u(r.left,r.top+r.height),this.topLeftPaddingBox=B>0||s>0?T(r.left+E,r.top+C,Math.max(0,B-E),Math.max(0,s-C),K.TOP_LEFT):new u(r.left+E,r.top+C),this.topRightPaddingBox=a>0||o>0?T(r.left+Math.min(F,r.width-v),r.top+C,F>r.width+v?0:Math.max(0,a-v),Math.max(0,o-C),K.TOP_RIGHT):new u(r.left+r.width-v,r.top+C),this.bottomRightPaddingBox=g>0||Q>0?T(r.left+Math.min(H,r.width-E),r.top+Math.min(y,r.height-m),Math.max(0,g-v),Math.max(0,Q-m),K.BOTTOM_RIGHT):new u(r.left+r.width-v,r.top+r.height-m),this.bottomLeftPaddingBox=w>0||l>0?T(r.left+E,r.top+Math.min(d,r.height-m),Math.max(0,w-E),Math.max(0,l-m),K.BOTTOM_LEFT):new u(r.left+E,r.top+r.height-m),this.topLeftContentBox=B>0||s>0?T(r.left+E+b,r.top+C+V,Math.max(0,B-(E+b)),Math.max(0,s-(C+V)),K.TOP_LEFT):new u(r.left+E+b,r.top+C+V),this.topRightContentBox=a>0||o>0?T(r.left+Math.min(F,r.width+E+b),r.top+C+V,F>r.width+E+b?0:a-E+b,o-(C+V),K.TOP_RIGHT):new u(r.left+r.width-(v+j),r.top+C+V),this.bottomRightContentBox=g>0||Q>0?T(r.left+Math.min(H,r.width-(E+b)),r.top+Math.min(y,r.height+C+V),Math.max(0,g-(v+j)),Q-(m+rA),K.BOTTOM_RIGHT):new u(r.left+r.width-(v+j),r.top+r.height-(m+rA)),this.bottomLeftContentBox=w>0||l>0?T(r.left+E+b,r.top+d,Math.max(0,w-(E+b)),l-(m+rA),K.BOTTOM_LEFT):new u(r.left+E+b,r.top+r.height-(m+rA))}return e}(),K;(function(e){e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(K||(K={}));var T=function(e,A,t,r,n){var B=4*((Math.sqrt(2)-1)/3),s=t*B,i=r*B,a=e+t,o=A+r;switch(n){case K.TOP_LEFT:return new gr(new u(e,o),new u(e,o-i),new u(a-s,A),new u(a,A));case K.TOP_RIGHT:return new gr(new u(e,A),new u(e+s,A),new u(a,o-i),new u(a,o));case K.BOTTOM_RIGHT:return new gr(new u(a,A),new u(a,A+i),new u(e+s,o),new u(e,o));case K.BOTTOM_LEFT:default:return new gr(new u(a,o),new u(a-s,o),new u(e,A+i),new u(e,A))}},Qr=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},uc=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},wr=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},fc=function(){function e(A,t,r){this.offsetX=A,this.offsetY=t,this.matrix=r,this.type=0,this.target=6}return e}(),lr=function(){function e(A,t){this.path=A,this.target=t,this.type=1}return e}(),Cc=function(){function e(A){this.opacity=A,this.type=2,this.target=6}return e}(),Uc=function(e){return e.type===0},oB=function(e){return e.type===1},Fc=function(e){return e.type===2},cB=function(e,A){return e.length===A.length?e.some(function(t,r){return t===A[r]}):!1},hc=function(e,A,t,r,n){return e.map(function(B,s){switch(s){case 0:return B.add(A,t);case 1:return B.add(A+r,t);case 2:return B.add(A+r,t+n);case 3:return B.add(A,t+n)}return B})},gB=function(){function e(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),QB=function(){function e(A,t){if(this.container=A,this.parent=t,this.effects=[],this.curves=new lc(this.container),this.container.styles.opacity<1&&this.effects.push(new Cc(this.container.styles.opacity)),this.container.styles.transform!==null){var r=this.container.bounds.left+this.container.styles.transformOrigin[0].number,n=this.container.bounds.top+this.container.styles.transformOrigin[1].number,B=this.container.styles.transform;this.effects.push(new fc(r,n,B))}if(this.container.styles.overflowX!==0){var s=Qr(this.curves),i=wr(this.curves);cB(s,i)?this.effects.push(new lr(s,6)):(this.effects.push(new lr(s,2)),this.effects.push(new lr(i,4)))}}return e.prototype.getEffects=function(A){for(var t=[2,3].indexOf(this.container.styles.position)===-1,r=this.parent,n=this.effects.slice(0);r;){var B=r.effects.filter(function(a){return!oB(a)});if(t||r.container.styles.position!==0||!r.parent){if(n.unshift.apply(n,B),t=[2,3].indexOf(r.container.styles.position)===-1,r.container.styles.overflowX!==0){var s=Qr(r.curves),i=wr(r.curves);cB(s,i)||n.unshift(new lr(i,6))}}else n.unshift.apply(n,B);r=r.parent}return n.filter(function(a){return N(a.target,A)})},e}(),gt=function(e,A,t,r){e.container.elements.forEach(function(n){var B=N(n.flags,4),s=N(n.flags,2),i=new QB(n,e);N(n.styles.display,2048)&&r.push(i);var a=N(n.flags,8)?[]:r;if(B||s){var o=B||n.styles.isPositioned()?t:A,c=new gB(i);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var g=n.styles.zIndex.order;if(g<0){var Q=0;o.negativeZIndex.some(function(w,l){return g>w.element.container.styles.zIndex.order?(Q=l,!1):Q>0}),o.negativeZIndex.splice(Q,0,c)}else if(g>0){var f=0;o.positiveZIndex.some(function(w,l){return g>=w.element.container.styles.zIndex.order?(f=l+1,!1):f>0}),o.positiveZIndex.splice(f,0,c)}else o.zeroOrAutoZIndexOrTransformedOrOpacity.push(c)}else n.styles.isFloating()?o.nonPositionedFloats.push(c):o.nonPositionedInlineLevel.push(c);gt(i,c,B?c:t,a)}else n.styles.isInlineLevel()?A.inlineLevel.push(i):A.nonInlineLevel.push(i),gt(i,A,t,a);N(n.flags,8)&&wB(n,a)})},wB=function(e,A){for(var t=e instanceof At?e.start:1,r=e instanceof At?e.reversed:!1,n=0;n<A.length;n++){var B=A[n];B.container instanceof Gn&&typeof B.container.value=="number"&&B.container.value!==0&&(t=B.container.value),B.listValue=Fe(t,B.container.styles.listStyleType,!0),t+=r?-1:1}},dc=function(e){var A=new QB(e,null),t=new gB(A),r=[];return gt(A,t,t,r),wB(A.container,r),t},lB=function(e,A){switch(A){case 0:return sA(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return sA(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return sA(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return sA(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},Ec=function(e,A){switch(A){case 0:return sA(e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox,e.topRightBorderBox,e.topRightBorderDoubleOuterBox);case 1:return sA(e.topRightBorderBox,e.topRightBorderDoubleOuterBox,e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox);case 2:return sA(e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox,e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox);case 3:default:return sA(e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox,e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox)}},pc=function(e,A){switch(A){case 0:return sA(e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox,e.topRightBorderDoubleInnerBox,e.topRightPaddingBox);case 1:return sA(e.topRightBorderDoubleInnerBox,e.topRightPaddingBox,e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox);case 2:return sA(e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox,e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox);case 3:default:return sA(e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox,e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox)}},Hc=function(e,A){switch(A){case 0:return ur(e.topLeftBorderStroke,e.topRightBorderStroke);case 1:return ur(e.topRightBorderStroke,e.bottomRightBorderStroke);case 2:return ur(e.bottomRightBorderStroke,e.bottomLeftBorderStroke);case 3:default:return ur(e.bottomLeftBorderStroke,e.topLeftBorderStroke)}},ur=function(e,A){var t=[];return BA(e)?t.push(e.subdivide(.5,!1)):t.push(e),BA(A)?t.push(A.subdivide(.5,!0)):t.push(A),t},sA=function(e,A,t,r){var n=[];return BA(e)?n.push(e.subdivide(.5,!1)):n.push(e),BA(t)?n.push(t.subdivide(.5,!0)):n.push(t),BA(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),BA(A)?n.push(A.subdivide(.5,!1).reverse()):n.push(A),n},uB=function(e){var A=e.bounds,t=e.styles;return A.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},fr=function(e){var A=e.styles,t=e.bounds,r=x(A.paddingLeft,t.width),n=x(A.paddingRight,t.width),B=x(A.paddingTop,t.width),s=x(A.paddingBottom,t.width);return t.add(r+A.borderLeftWidth,B+A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth+r+n),-(A.borderTopWidth+A.borderBottomWidth+B+s))},Ic=function(e,A){return e===0?A.bounds:e===2?fr(A):uB(A)},vc=function(e,A){return e===0?A.bounds:e===2?fr(A):uB(A)},Qt=function(e,A,t){var r=Ic(zA(e.styles.backgroundOrigin,A),e),n=vc(zA(e.styles.backgroundClip,A),e),B=mc(zA(e.styles.backgroundSize,A),t,r),s=B[0],i=B[1],a=we(zA(e.styles.backgroundPosition,A),r.width-s,r.height-i),o=yc(zA(e.styles.backgroundRepeat,A),a,B,r,n),c=Math.round(r.left+a[0]),g=Math.round(r.top+a[1]);return[o,c,g,s,i]},jA=function(e){return L(e)&&e.value===JA.AUTO},Cr=function(e){return typeof e=="number"},mc=function(e,A,t){var r=A[0],n=A[1],B=A[2],s=e[0],i=e[1];if(!s)return[0,0];if(R(s)&&i&&R(i))return[x(s,t.width),x(i,t.height)];var a=Cr(B);if(L(s)&&(s.value===JA.CONTAIN||s.value===JA.COVER)){if(Cr(B)){var o=t.width/t.height;return o<B!=(s.value===JA.COVER)?[t.width,t.width/B]:[t.height*B,t.height]}return[t.width,t.height]}var c=Cr(r),g=Cr(n),Q=c||g;if(jA(s)&&(!i||jA(i))){if(c&&g)return[r,n];if(!a&&!Q)return[t.width,t.height];if(Q&&a){var f=c?r:n*B,w=g?n:r/B;return[f,w]}var l=c?r:t.width,p=g?n:t.height;return[l,p]}if(a){var h=0,F=0;return R(s)?h=x(s,t.width):R(i)&&(F=x(i,t.height)),jA(s)?h=F*B:(!i||jA(i))&&(F=h/B),[h,F]}var y=null,H=null;if(R(s)?y=x(s,t.width):i&&R(i)&&(H=x(i,t.height)),y!==null&&(!i||jA(i))&&(H=c&&g?y/r*n:t.height),H!==null&&jA(s)&&(y=c&&g?H/n*r:t.width),y!==null&&H!==null)return[y,H];throw new Error("Unable to calculate background-size for element")},zA=function(e,A){var t=e[A];return typeof t>"u"?e[0]:t},yc=function(e,A,t,r,n){var B=A[0],s=A[1],i=t[0],a=t[1];switch(e){case 2:return[new u(Math.round(r.left),Math.round(r.top+s)),new u(Math.round(r.left+r.width),Math.round(r.top+s)),new u(Math.round(r.left+r.width),Math.round(a+r.top+s)),new u(Math.round(r.left),Math.round(a+r.top+s))];case 3:return[new u(Math.round(r.left+B),Math.round(r.top)),new u(Math.round(r.left+B+i),Math.round(r.top)),new u(Math.round(r.left+B+i),Math.round(r.height+r.top)),new u(Math.round(r.left+B),Math.round(r.height+r.top))];case 1:return[new u(Math.round(r.left+B),Math.round(r.top+s)),new u(Math.round(r.left+B+i),Math.round(r.top+s)),new u(Math.round(r.left+B+i),Math.round(r.top+s+a)),new u(Math.round(r.left+B),Math.round(r.top+s+a))];default:return[new u(Math.round(n.left),Math.round(n.top)),new u(Math.round(n.left+n.width),Math.round(n.top)),new u(Math.round(n.left+n.width),Math.round(n.height+n.top)),new u(Math.round(n.left),Math.round(n.height+n.top))]}},Kc="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",fB="Hidden Text",Lc=function(){function e(A){this._data={},this._document=A}return e.prototype.parseMetrics=function(A,t){var r=this._document.createElement("div"),n=this._document.createElement("img"),B=this._document.createElement("span"),s=this._document.body;r.style.visibility="hidden",r.style.fontFamily=A,r.style.fontSize=t,r.style.margin="0",r.style.padding="0",r.style.whiteSpace="nowrap",s.appendChild(r),n.src=Kc,n.width=1,n.height=1,n.style.margin="0",n.style.padding="0",n.style.verticalAlign="baseline",B.style.fontFamily=A,B.style.fontSize=t,B.style.margin="0",B.style.padding="0",B.appendChild(this._document.createTextNode(fB)),r.appendChild(B),r.appendChild(n);var i=n.offsetTop-B.offsetTop+2;r.removeChild(B),r.appendChild(this._document.createTextNode(fB)),r.style.lineHeight="normal",n.style.verticalAlign="super";var a=n.offsetTop-r.offsetTop+2;return s.removeChild(r),{baseline:i,middle:a}},e.prototype.getMetrics=function(A,t){var r=A+" "+t;return typeof this._data[r]>"u"&&(this._data[r]=this.parseMetrics(A,t)),this._data[r]},e}(),CB=function(){function e(A,t){this.context=A,this.options=t}return e}(),bc=1e4,Dc=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n._activeEffects=[],n.canvas=r.canvas?r.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),r.canvas||(n.canvas.width=Math.floor(r.width*r.scale),n.canvas.height=Math.floor(r.height*r.scale),n.canvas.style.width=r.width+"px",n.canvas.style.height=r.height+"px"),n.fontMetrics=new Lc(document),n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-r.x,-r.y),n.ctx.textBaseline="bottom",n._activeEffects=[],n.context.logger.debug("Canvas renderer initialized ("+r.width+"x"+r.height+") with scale "+r.scale),n}return A.prototype.applyEffects=function(t){for(var r=this;this._activeEffects.length;)this.popEffect();t.forEach(function(n){return r.applyEffect(n)})},A.prototype.applyEffect=function(t){this.ctx.save(),Fc(t)&&(this.ctx.globalAlpha=t.opacity),Uc(t)&&(this.ctx.translate(t.offsetX,t.offsetY),this.ctx.transform(t.matrix[0],t.matrix[1],t.matrix[2],t.matrix[3],t.matrix[4],t.matrix[5]),this.ctx.translate(-t.offsetX,-t.offsetY)),oB(t)&&(this.path(t.path),this.ctx.clip()),this._activeEffects.push(t)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(t){return Z(this,void 0,void 0,function(){var r;return J(this,function(n){switch(n.label){case 0:return r=t.element.container.styles,r.isVisible()?[4,this.renderStackContent(t)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(t){return Z(this,void 0,void 0,function(){return J(this,function(r){switch(r.label){case 0:if(N(t.container.flags,16))debugger;return t.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(t)]:[3,3];case 1:return r.sent(),[4,this.renderNodeContent(t)];case 2:r.sent(),r.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(t,r,n){var B=this;if(r===0)this.ctx.fillText(t.text,t.bounds.left,t.bounds.top+n);else{var s=$r(t.text);s.reduce(function(i,a){return B.ctx.fillText(a,i,t.bounds.top+n),i+B.ctx.measureText(a).width},t.bounds.left)}},A.prototype.createFontStyle=function(t){var r=t.fontVariant.filter(function(s){return s==="normal"||s==="small-caps"}).join(""),n=Mc(t.fontFamily).join(", "),B=Qe(t.fontSize)?""+t.fontSize.number+t.fontSize.unit:t.fontSize.number+"px";return[[t.fontStyle,r,t.fontWeight,B,n].join(" "),n,B]},A.prototype.renderTextNode=function(t,r){return Z(this,void 0,void 0,function(){var n,B,s,i,a,o,c,g,Q=this;return J(this,function(f){return n=this.createFontStyle(r),B=n[0],s=n[1],i=n[2],this.ctx.font=B,this.ctx.direction=r.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",a=this.fontMetrics.getMetrics(s,i),o=a.baseline,c=a.middle,g=r.paintOrder,t.textBounds.forEach(function(w){g.forEach(function(l){switch(l){case 0:Q.ctx.fillStyle=_(r.color),Q.renderTextWithLetterSpacing(w,r.letterSpacing,o);var p=r.textShadow;p.length&&w.text.trim().length&&(p.slice(0).reverse().forEach(function(h){Q.ctx.shadowColor=_(h.color),Q.ctx.shadowOffsetX=h.offsetX.number*Q.options.scale,Q.ctx.shadowOffsetY=h.offsetY.number*Q.options.scale,Q.ctx.shadowBlur=h.blur.number,Q.renderTextWithLetterSpacing(w,r.letterSpacing,o)}),Q.ctx.shadowColor="",Q.ctx.shadowOffsetX=0,Q.ctx.shadowOffsetY=0,Q.ctx.shadowBlur=0),r.textDecorationLine.length&&(Q.ctx.fillStyle=_(r.textDecorationColor||r.color),r.textDecorationLine.forEach(function(h){switch(h){case 1:Q.ctx.fillRect(w.bounds.left,Math.round(w.bounds.top+o),w.bounds.width,1);break;case 2:Q.ctx.fillRect(w.bounds.left,Math.round(w.bounds.top),w.bounds.width,1);break;case 3:Q.ctx.fillRect(w.bounds.left,Math.ceil(w.bounds.top+c),w.bounds.width,1);break}}));break;case 1:r.webkitTextStrokeWidth&&w.text.trim().length&&(Q.ctx.strokeStyle=_(r.webkitTextStrokeColor),Q.ctx.lineWidth=r.webkitTextStrokeWidth,Q.ctx.lineJoin=window.chrome?"miter":"round",Q.ctx.strokeText(w.text,w.bounds.left,w.bounds.top+o)),Q.ctx.strokeStyle="",Q.ctx.lineWidth=0,Q.ctx.lineJoin="miter";break}})}),[2]})})},A.prototype.renderReplacedElement=function(t,r,n){if(n&&t.intrinsicWidth>0&&t.intrinsicHeight>0){var B=fr(t),s=wr(r);this.path(s),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(n,0,0,t.intrinsicWidth,t.intrinsicHeight,B.left,B.top,B.width,B.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(t){return Z(this,void 0,void 0,function(){var r,n,B,s,i,a,F,F,o,c,g,Q,H,f,w,d,l,p,h,F,y,H,d;return J(this,function(C){switch(C.label){case 0:this.applyEffects(t.getEffects(4)),r=t.container,n=t.curves,B=r.styles,s=0,i=r.textNodes,C.label=1;case 1:return s<i.length?(a=i[s],[4,this.renderTextNode(a,B)]):[3,4];case 2:C.sent(),C.label=3;case 3:return s++,[3,1];case 4:if(!(r instanceof Sn))return[3,8];C.label=5;case 5:return C.trys.push([5,7,,8]),[4,this.context.cache.match(r.src)];case 6:return F=C.sent(),this.renderReplacedElement(r,n,F),[3,8];case 7:return C.sent(),this.context.logger.error("Error loading image "+r.src),[3,8];case 8:if(r instanceof On&&this.renderReplacedElement(r,n,r.canvas),!(r instanceof Mn))return[3,12];C.label=9;case 9:return C.trys.push([9,11,,12]),[4,this.context.cache.match(r.svg)];case 10:return F=C.sent(),this.renderReplacedElement(r,n,F),[3,12];case 11:return C.sent(),this.context.logger.error("Error loading svg "+r.svg.substring(0,255)),[3,12];case 12:return r instanceof _n&&r.tree?(o=new A(this.context,{scale:this.options.scale,backgroundColor:r.backgroundColor,x:0,y:0,width:r.width,height:r.height}),[4,o.render(r.tree)]):[3,14];case 13:c=C.sent(),r.width&&r.height&&this.ctx.drawImage(c,0,0,r.width,r.height,r.bounds.left,r.bounds.top,r.bounds.width,r.bounds.height),C.label=14;case 14:if(r instanceof et&&(g=Math.min(r.bounds.width,r.bounds.height),r.type===Br?r.checked&&(this.ctx.save(),this.path([new u(r.bounds.left+g*.39363,r.bounds.top+g*.79),new u(r.bounds.left+g*.16,r.bounds.top+g*.5549),new u(r.bounds.left+g*.27347,r.bounds.top+g*.44071),new u(r.bounds.left+g*.39694,r.bounds.top+g*.5649),new u(r.bounds.left+g*.72983,r.bounds.top+g*.23),new u(r.bounds.left+g*.84,r.bounds.top+g*.34085),new u(r.bounds.left+g*.39363,r.bounds.top+g*.79)]),this.ctx.fillStyle=_(Rn),this.ctx.fill(),this.ctx.restore()):r.type===sr&&r.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(r.bounds.left+g/2,r.bounds.top+g/2,g/4,0,Math.PI*2,!0),this.ctx.fillStyle=_(Rn),this.ctx.fill(),this.ctx.restore())),xc(r)&&r.value.length){switch(Q=this.createFontStyle(B),H=Q[0],f=Q[1],w=this.fontMetrics.getMetrics(H,f).baseline,this.ctx.font=H,this.ctx.fillStyle=_(B.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=Sc(r.styles.textAlign),d=fr(r),l=0,r.styles.textAlign){case 1:l+=d.width/2;break;case 2:l+=d.width;break}p=d.add(l,0,0,-d.height/2+1),this.ctx.save(),this.path([new u(d.left,d.top),new u(d.left+d.width,d.top),new u(d.left+d.width,d.top+d.height),new u(d.left,d.top+d.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Ce(r.value,p),B.letterSpacing,w),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!N(r.styles.display,2048))return[3,20];if(r.styles.listStyleImage===null)return[3,19];if(h=r.styles.listStyleImage,h.type!==0)return[3,18];F=void 0,y=h.url,C.label=15;case 15:return C.trys.push([15,17,,18]),[4,this.context.cache.match(y)];case 16:return F=C.sent(),this.ctx.drawImage(F,r.bounds.left-(F.width+10),r.bounds.top),[3,18];case 17:return C.sent(),this.context.logger.error("Error loading list-style-image "+y),[3,18];case 18:return[3,20];case 19:t.listValue&&r.styles.listStyleType!==-1&&(H=this.createFontStyle(B)[0],this.ctx.font=H,this.ctx.fillStyle=_(B.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",d=new wA(r.bounds.left,r.bounds.top+x(r.styles.paddingTop,r.bounds.width),r.bounds.width,fn(B.lineHeight,B.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Ce(t.listValue,d),B.letterSpacing,fn(B.lineHeight,B.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),C.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(t){return Z(this,void 0,void 0,function(){var r,n,h,B,s,h,i,a,h,o,c,h,g,Q,h,f,w,h,l,p,h;return J(this,function(F){switch(F.label){case 0:if(N(t.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(t.element)];case 1:F.sent(),r=0,n=t.negativeZIndex,F.label=2;case 2:return r<n.length?(h=n[r],[4,this.renderStack(h)]):[3,5];case 3:F.sent(),F.label=4;case 4:return r++,[3,2];case 5:return[4,this.renderNodeContent(t.element)];case 6:F.sent(),B=0,s=t.nonInlineLevel,F.label=7;case 7:return B<s.length?(h=s[B],[4,this.renderNode(h)]):[3,10];case 8:F.sent(),F.label=9;case 9:return B++,[3,7];case 10:i=0,a=t.nonPositionedFloats,F.label=11;case 11:return i<a.length?(h=a[i],[4,this.renderStack(h)]):[3,14];case 12:F.sent(),F.label=13;case 13:return i++,[3,11];case 14:o=0,c=t.nonPositionedInlineLevel,F.label=15;case 15:return o<c.length?(h=c[o],[4,this.renderStack(h)]):[3,18];case 16:F.sent(),F.label=17;case 17:return o++,[3,15];case 18:g=0,Q=t.inlineLevel,F.label=19;case 19:return g<Q.length?(h=Q[g],[4,this.renderNode(h)]):[3,22];case 20:F.sent(),F.label=21;case 21:return g++,[3,19];case 22:f=0,w=t.zeroOrAutoZIndexOrTransformedOrOpacity,F.label=23;case 23:return f<w.length?(h=w[f],[4,this.renderStack(h)]):[3,26];case 24:F.sent(),F.label=25;case 25:return f++,[3,23];case 26:l=0,p=t.positiveZIndex,F.label=27;case 27:return l<p.length?(h=p[l],[4,this.renderStack(h)]):[3,30];case 28:F.sent(),F.label=29;case 29:return l++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(t){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(t.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(t){this.ctx.beginPath(),this.formatPath(t),this.ctx.closePath()},A.prototype.formatPath=function(t){var r=this;t.forEach(function(n,B){var s=BA(n)?n.start:n;B===0?r.ctx.moveTo(s.x,s.y):r.ctx.lineTo(s.x,s.y),BA(n)&&r.ctx.bezierCurveTo(n.startControl.x,n.startControl.y,n.endControl.x,n.endControl.y,n.end.x,n.end.y)})},A.prototype.renderRepeat=function(t,r,n,B){this.path(t),this.ctx.fillStyle=r,this.ctx.translate(n,B),this.ctx.fill(),this.ctx.translate(-n,-B)},A.prototype.resizeImage=function(t,r,n){var B;if(t.width===r&&t.height===n)return t;var s=(B=this.canvas.ownerDocument)!==null&&B!==void 0?B:document,i=s.createElement("canvas");i.width=Math.max(1,r),i.height=Math.max(1,n);var a=i.getContext("2d");return a.drawImage(t,0,0,t.width,t.height,0,0,r,n),i},A.prototype.renderBackgroundImage=function(t){return Z(this,void 0,void 0,function(){var r,n,B,s,i,a;return J(this,function(o){switch(o.label){case 0:r=t.styles.backgroundImage.length-1,n=function(c){var g,Q,f,V,z,$,b,X,m,w,V,z,$,b,X,l,p,h,F,y,H,d,C,v,m,E,V,j,rA,b,X,yA,z,$,MA,gA,KA,GA,RA,CA,VA,UA;return J(this,function($A){switch($A.label){case 0:if(c.type!==0)return[3,5];g=void 0,Q=c.url,$A.label=1;case 1:return $A.trys.push([1,3,,4]),[4,B.context.cache.match(Q)];case 2:return g=$A.sent(),[3,4];case 3:return $A.sent(),B.context.logger.error("Error loading background-image "+Q),[3,4];case 4:return g&&(f=Qt(t,r,[g.width,g.height,g.width/g.height]),V=f[0],z=f[1],$=f[2],b=f[3],X=f[4],m=B.ctx.createPattern(B.resizeImage(g,b,X),"repeat"),B.renderRepeat(V,m,z,$)),[3,6];case 5:Ci(c)?(w=Qt(t,r,[null,null,null]),V=w[0],z=w[1],$=w[2],b=w[3],X=w[4],l=Qi(c.angle,b,X),p=l[0],h=l[1],F=l[2],y=l[3],H=l[4],d=document.createElement("canvas"),d.width=b,d.height=X,C=d.getContext("2d"),v=C.createLinearGradient(h,y,F,H),nn(c.stops,p).forEach(function(He){return v.addColorStop(He.stop,_(He.color))}),C.fillStyle=v,C.fillRect(0,0,b,X),b>0&&X>0&&(m=B.ctx.createPattern(d,"repeat"),B.renderRepeat(V,m,z,$))):Ui(c)&&(E=Qt(t,r,[null,null,null]),V=E[0],j=E[1],rA=E[2],b=E[3],X=E[4],yA=c.position.length===0?[Gr]:c.position,z=x(yA[0],b),$=x(yA[yA.length-1],X),MA=wi(c,z,$,b,X),gA=MA[0],KA=MA[1],gA>0&&KA>0&&(GA=B.ctx.createRadialGradient(j+z,rA+$,0,j+z,rA+$,gA),nn(c.stops,gA*2).forEach(function(He){return GA.addColorStop(He.stop,_(He.color))}),B.path(V),B.ctx.fillStyle=GA,gA!==KA?(RA=t.bounds.left+.5*t.bounds.width,CA=t.bounds.top+.5*t.bounds.height,VA=KA/gA,UA=1/VA,B.ctx.save(),B.ctx.translate(RA,CA),B.ctx.transform(1,0,0,VA,0,0),B.ctx.translate(-RA,-CA),B.ctx.fillRect(j,UA*(rA-CA)+CA,b,X*UA),B.ctx.restore()):B.ctx.fill())),$A.label=6;case 6:return r--,[2]}})},B=this,s=0,i=t.styles.backgroundImage.slice(0).reverse(),o.label=1;case 1:return s<i.length?(a=i[s],[5,n(a)]):[3,4];case 2:o.sent(),o.label=3;case 3:return s++,[3,1];case 4:return[2]}})})},A.prototype.renderSolidBorder=function(t,r,n){return Z(this,void 0,void 0,function(){return J(this,function(B){return this.path(lB(n,r)),this.ctx.fillStyle=_(t),this.ctx.fill(),[2]})})},A.prototype.renderDoubleBorder=function(t,r,n,B){return Z(this,void 0,void 0,function(){var s,i;return J(this,function(a){switch(a.label){case 0:return r<3?[4,this.renderSolidBorder(t,n,B)]:[3,2];case 1:return a.sent(),[2];case 2:return s=Ec(B,n),this.path(s),this.ctx.fillStyle=_(t),this.ctx.fill(),i=pc(B,n),this.path(i),this.ctx.fill(),[2]}})})},A.prototype.renderNodeBackgroundAndBorders=function(t){return Z(this,void 0,void 0,function(){var r,n,B,s,i,a,o,c,g=this;return J(this,function(Q){switch(Q.label){case 0:return this.applyEffects(t.getEffects(2)),r=t.container.styles,n=!HA(r.backgroundColor)||r.backgroundImage.length,B=[{style:r.borderTopStyle,color:r.borderTopColor,width:r.borderTopWidth},{style:r.borderRightStyle,color:r.borderRightColor,width:r.borderRightWidth},{style:r.borderBottomStyle,color:r.borderBottomColor,width:r.borderBottomWidth},{style:r.borderLeftStyle,color:r.borderLeftColor,width:r.borderLeftWidth}],s=Tc(zA(r.backgroundClip,0),t.curves),n||r.boxShadow.length?(this.ctx.save(),this.path(s),this.ctx.clip(),HA(r.backgroundColor)||(this.ctx.fillStyle=_(r.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(t.container)]):[3,2];case 1:Q.sent(),this.ctx.restore(),r.boxShadow.slice(0).reverse().forEach(function(f){g.ctx.save();var w=Qr(t.curves),l=f.inset?0:bc,p=hc(w,-l+(f.inset?1:-1)*f.spread.number,(f.inset?1:-1)*f.spread.number,f.spread.number*(f.inset?-2:2),f.spread.number*(f.inset?-2:2));f.inset?(g.path(w),g.ctx.clip(),g.mask(p)):(g.mask(w),g.ctx.clip(),g.path(p)),g.ctx.shadowOffsetX=f.offsetX.number+l,g.ctx.shadowOffsetY=f.offsetY.number,g.ctx.shadowColor=_(f.color),g.ctx.shadowBlur=f.blur.number,g.ctx.fillStyle=f.inset?_(f.color):"rgba(0,0,0,1)",g.ctx.fill(),g.ctx.restore()}),Q.label=2;case 2:i=0,a=0,o=B,Q.label=3;case 3:return a<o.length?(c=o[a],c.style!==0&&!HA(c.color)&&c.width>0?c.style!==2?[3,5]:[4,this.renderDashedDottedBorder(c.color,c.width,i,t.curves,2)]:[3,11]):[3,13];case 4:return Q.sent(),[3,11];case 5:return c.style!==3?[3,7]:[4,this.renderDashedDottedBorder(c.color,c.width,i,t.curves,3)];case 6:return Q.sent(),[3,11];case 7:return c.style!==4?[3,9]:[4,this.renderDoubleBorder(c.color,c.width,i,t.curves)];case 8:return Q.sent(),[3,11];case 9:return[4,this.renderSolidBorder(c.color,i,t.curves)];case 10:Q.sent(),Q.label=11;case 11:i++,Q.label=12;case 12:return a++,[3,3];case 13:return[2]}})})},A.prototype.renderDashedDottedBorder=function(t,r,n,B,s){return Z(this,void 0,void 0,function(){var i,a,o,c,g,Q,f,w,l,p,h,F,y,H,d,C,d,C;return J(this,function(v){return this.ctx.save(),i=Hc(B,n),a=lB(B,n),s===2&&(this.path(a),this.ctx.clip()),BA(a[0])?(o=a[0].start.x,c=a[0].start.y):(o=a[0].x,c=a[0].y),BA(a[1])?(g=a[1].end.x,Q=a[1].end.y):(g=a[1].x,Q=a[1].y),n===0||n===2?f=Math.abs(o-g):f=Math.abs(c-Q),this.ctx.beginPath(),s===3?this.formatPath(i):this.formatPath(a.slice(0,2)),w=r<3?r*3:r*2,l=r<3?r*2:r,s===3&&(w=r,l=r),p=!0,f<=w*2?p=!1:f<=w*2+l?(h=f/(2*w+l),w*=h,l*=h):(F=Math.floor((f+l)/(w+l)),y=(f-F*w)/(F-1),H=(f-(F+1)*w)/F,l=H<=0||Math.abs(l-y)<Math.abs(l-H)?y:H),p&&(s===3?this.ctx.setLineDash([0,w+l]):this.ctx.setLineDash([w,l])),s===3?(this.ctx.lineCap="round",this.ctx.lineWidth=r):this.ctx.lineWidth=r*2+1.1,this.ctx.strokeStyle=_(t),this.ctx.stroke(),this.ctx.setLineDash([]),s===2&&(BA(a[0])&&(d=a[3],C=a[0],this.ctx.beginPath(),this.formatPath([new u(d.end.x,d.end.y),new u(C.start.x,C.start.y)]),this.ctx.stroke()),BA(a[1])&&(d=a[1],C=a[2],this.ctx.beginPath(),this.formatPath([new u(d.end.x,d.end.y),new u(C.start.x,C.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},A.prototype.render=function(t){return Z(this,void 0,void 0,function(){var r;return J(this,function(n){switch(n.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=_(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),r=dc(t),[4,this.renderStack(r)];case 1:return n.sent(),this.applyEffects([]),[2,this.canvas]}})})},A}(CB),xc=function(e){return e instanceof Nn||e instanceof Vn?!0:e instanceof et&&e.type!==sr&&e.type!==Br},Tc=function(e,A){switch(e){case 0:return Qr(A);case 2:return uc(A);case 1:default:return wr(A)}},Sc=function(e){switch(e){case 1:return"center";case 2:return"right";case 0:default:return"left"}},Oc=["-apple-system","system-ui"],Mc=function(e){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?e.filter(function(A){return Oc.indexOf(A)===-1}):e},Gc=function(e){S(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.canvas=r.canvas?r.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),n.options=r,n.canvas.width=Math.floor(r.width*r.scale),n.canvas.height=Math.floor(r.height*r.scale),n.canvas.style.width=r.width+"px",n.canvas.style.height=r.height+"px",n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-r.x,-r.y),n.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+r.width+"x"+r.height+" at "+r.x+","+r.y+") with scale "+r.scale),n}return A.prototype.render=function(t){return Z(this,void 0,void 0,function(){var r,n;return J(this,function(B){switch(B.label){case 0:return r=zr(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,t),[4,Rc(r)];case 1:return n=B.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=_(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(n,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(CB),Rc=function(e){return new Promise(function(A,t){var r=new Image;r.onload=function(){A(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},Vc=function(){function e(A){var t=A.id,r=A.enabled;this.id=t,this.enabled=r,this.start=Date.now()}return e.prototype.debug=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,Ie([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.getTime=function(){return Date.now()-this.start},e.prototype.info=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,Ie([this.id,this.getTime()+"ms"],A))},e.prototype.warn=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,Ie([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.error=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,Ie([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.instances={},e}(),Nc=function(){function e(A,t){var r;this.windowBounds=t,this.instanceName="#"+e.instanceCount++,this.logger=new Vc({id:this.instanceName,enabled:A.logging}),this.cache=(r=A.cache)!==null&&r!==void 0?r:new ic(this,A)}return e.instanceCount=1,e}(),_c=function(e,A){return A===void 0&&(A={}),kc(e,A)};typeof window<"u"&&aB.setContext(window);var kc=function(e,A){return Z(void 0,void 0,void 0,function(){var t,r,n,B,s,i,a,o,c,g,Q,f,w,l,p,h,F,y,H,d,v,C,v,m,E,V,j,rA,b,X,yA,z,$,MA,gA,KA,GA,RA,CA,VA;return J(this,function(UA){switch(UA.label){case 0:if(!e||typeof e!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(t=e.ownerDocument,!t)throw new Error("Element is not attached to a Document");if(r=t.defaultView,!r)throw new Error("Document is not attached to a Window");return n={allowTaint:(m=A.allowTaint)!==null&&m!==void 0?m:!1,imageTimeout:(E=A.imageTimeout)!==null&&E!==void 0?E:15e3,proxy:A.proxy,useCORS:(V=A.useCORS)!==null&&V!==void 0?V:!1},B=QA({logging:(j=A.logging)!==null&&j!==void 0?j:!0,cache:A.cache},n),s={windowWidth:(rA=A.windowWidth)!==null&&rA!==void 0?rA:r.innerWidth,windowHeight:(b=A.windowHeight)!==null&&b!==void 0?b:r.innerHeight,scrollX:(X=A.scrollX)!==null&&X!==void 0?X:r.pageXOffset,scrollY:(yA=A.scrollY)!==null&&yA!==void 0?yA:r.pageYOffset},i=new wA(s.scrollX,s.scrollY,s.windowWidth,s.windowHeight),a=new Nc(B,i),o=(z=A.foreignObjectRendering)!==null&&z!==void 0?z:!1,c={allowTaint:($=A.allowTaint)!==null&&$!==void 0?$:!1,onclone:A.onclone,ignoreElements:A.ignoreElements,inlineImages:o,copyStyles:o},a.logger.debug("Starting document clone with size "+i.width+"x"+i.height+" scrolled to "+-i.left+","+-i.top),g=new sB(a,e,c),Q=g.clonedReferenceElement,Q?[4,g.toIFrame(t,i)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return f=UA.sent(),w=tt(Q)||Po(Q)?FB(Q.ownerDocument):ve(a,Q),l=w.width,p=w.height,h=w.left,F=w.top,y=Pc(a,Q,A.backgroundColor),H={canvas:A.canvas,backgroundColor:y,scale:(gA=(MA=A.scale)!==null&&MA!==void 0?MA:r.devicePixelRatio)!==null&&gA!==void 0?gA:1,x:((KA=A.x)!==null&&KA!==void 0?KA:0)+h,y:((GA=A.y)!==null&&GA!==void 0?GA:0)+F,width:(RA=A.width)!==null&&RA!==void 0?RA:Math.ceil(l),height:(CA=A.height)!==null&&CA!==void 0?CA:Math.ceil(p)},o?(a.logger.debug("Document cloned, using foreign object rendering"),v=new Gc(a,H),[4,v.render(Q)]):[3,3];case 2:return d=UA.sent(),[3,5];case 3:return a.logger.debug("Document cloned, element located at "+h+","+F+" with size "+l+"x"+p+" using computed rendering"),a.logger.debug("Starting DOM parsing"),C=Pn(a,Q),y===C.styles.backgroundColor&&(C.styles.backgroundColor=uA.TRANSPARENT),a.logger.debug("Starting renderer for element at "+H.x+","+H.y+" with size "+H.width+"x"+H.height),v=new Dc(a,H),[4,v.render(C)];case 4:d=UA.sent(),UA.label=5;case 5:return(!((VA=A.removeContainer)!==null&&VA!==void 0)||VA)&&(sB.destroy(f)||a.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),a.logger.debug("Finished rendering"),[2,d]}})})},Pc=function(e,A,t){var r=A.ownerDocument,n=r.documentElement?le(e,getComputedStyle(r.documentElement).backgroundColor):uA.TRANSPARENT,B=r.body?le(e,getComputedStyle(r.body).backgroundColor):uA.TRANSPARENT,s=typeof t=="string"?le(e,t):t===null?uA.TRANSPARENT:4294967295;return A===r.documentElement?HA(n)?HA(B)?s:B:n:s};function Xc(e,A,t){const r=new Date(e).getTime(),n=new Date(A).getTime();return Math.abs(r-n)>t*1e3}function Jc(){return typeof chrome<"u"&&!!chrome.runtime}const wt=window.requestIdleCallback||window.requestAnimationFrame||(e=>setTimeout(e,16)),SA=(()=>(window.__globalCatcher__=window.__globalCatcher__||{},window.__globalCatcher__))();function Yc(e){const A=e.map(t=>t.includes("*")?`^${t.split("*").join("\\d{1}")}$`:`^${t}$`).join("|");return new RegExp(A)}class de{static getItem(A){const t=localStorage.getItem(A);return t?JSON.parse(t):null}static setItem(A,t){localStorage.setItem(A,JSON.stringify(t))}static removeItem(A){localStorage.removeItem(A)}static clear(){localStorage.clear()}}class Wc{constructor(){D(this,"isFinished",!1);D(this,"projectId","");D(this,"groupId","");D(this,"url","");D(this,"PromiseCatcher",!1);D(this,"ResourceCatcher",!1);D(this,"NetworkCatcher",!1);D(this,"JavaScriptCatcher",!0);D(this,"height",200);D(this,"width",200);D(this,"interval",2e3);D(this,"publicKey","");D(this,"debug",de.getItem("PLUGIN_DEBUG")||!1);D(this,"groupRels",[]);D(this,"config",{});D(this,"maxCache",4);D(this,"delayTime",3e3);D(this,"netConfig",{netErrorCodes:["4**","5**","3**","2**"],netCodePattern:/^4\d{2}$|^5\d{2}$|^3\d{2}|^2\d{2}$/,ignoreUrls:["exceptionCatcher","exceptionCatcherGroup","batchSaveExceptionCatcherLogs"],netTimeout:5e3,catcherId:""});D(this,"inChromeExtension",Jc())}init(A){Object.assign(this,A),this.isFinished=!0}updated({rules:A}){this.buildNetConfig(A.netConfig||{}),this.groupRels=A.groupRels}buildNetConfig(A){const{netErrorCodes:t,ignoreUrls:r,netTimeout:n,catcherId:B}=A;if(this.netConfig.catcherId=B,t){const s=t==null?void 0:t.split(",");this.netConfig.netCodePattern=Yc(s)}r&&(this.netConfig.ignoreUrls=[...r.split(","),...this.netConfig.ignoreUrls]),n&&(this.netConfig.netTimeout=n)}}let W;function Zc(e){W=new Wc,SA.options=W}const qc=!!chrome.extension;function jc(e,A,t){let r={method:t,headers:{"content-type":"application/json",referer:qc?"":decodeURIComponent(window.location.href)},mode:"cors"};return(t==="post"||t==="put"||t==="delete")&&(r=Object.assign(r,{body:JSON.stringify(A)})),Promise.race([fetch(e,r).then(n=>n.ok||n.status===401?n:Promise.reject(`${n.status}(${n.statusText})`)).catch(n=>Promise.reject(String(n))),new Promise((n,B)=>{setTimeout(()=>{B("\u670D\u52A1\u8D85\u65F6")},3e4)})]).then(n=>{const B=n.headers.get("content-type");return/application\/octet-stream/.test(B)?n.blob():/application\/json/.test(B)?n.json():/text\/html/.test(B)?n.text():n}).then(async n=>n).catch(n=>n?Promise.reject(n):Promise.reject())}const zc=["get","post","put","delete"],lt={};zc.forEach(e=>{lt[e]=(A,t)=>jc(A,t,e)});const $c=(e,A)=>lt.get(`${e}/autotest-web/exceptionCatcherGroup/getExceptionCatcherGroupWithoutLogin?exceptionCatcherGroupId=${A}`),Ag=(e,A)=>lt.post(`${e}/autotest-web/exceptionCatcherLog/batchSaveExceptionCatcherLogs`,A);class eg{constructor(){D(this,"_locked",!1);this.send()}async send(){if(!W.isFinished)return;const A=O.errors,t=A.length,{projectId:r,url:n,groupId:B}=W;if(!!t)try{if(W.inChromeExtension)await chrome.runtime.sendMessage({functionName:"errorCatchBatchSend",data:{errors:A,groupId:B,post:n,projectId:r}});else try{if(this._locked)return;this._locked=!0,(await Ag(n,{projectId:r,groupId:B,exceptionCatcherLogs:A})).code==="0"&&O.clear(t)}catch{O.clear(t)}finally{this._locked=!1}}catch(s){console.warn(s)}}}let OA;function rg(){OA=new eg}let Ee=!1;class tg{constructor(){D(this,"errors");D(this,"errorsMap");D(this,"timeout");SA.errors=this,this.errors=de.getItem("errors")||[],this.errorsMap=de.getItem("errorsMap")||{},window.addEventListener("beforeunload",()=>{this.save()})}save(){de.setItem("errors",this.errors),de.setItem("errorsMap",this.errorsMap)}add(A,t=!1){this.errors.push(A),this.errors.length>=W.maxCache?(clearTimeout(this.timeout),this.timeout=setTimeout(async()=>{if(!Ee){Ee=!0;try{await(OA==null?void 0:OA.send()),Ee=!1}catch{Ee=!1}finally{Ee=!1}}},W.delayTime)):t&&this.errors.length&&(OA==null||OA.send())}clear(A=0){A?this.errors=this.errors.slice(A):this.errors=[],this.errorsMap={}}checkErrorTime(A){var B;const t=(B=this.errorsMap)==null?void 0:B[A],r=new Date,n=Xc(t,r,W.interval);return t&&!n?!1:(this.errorsMap[A]=r,!0)}async setErrors(A,t){const{width:r,height:n}=W,B=`${A}-${t==null?void 0:t.keywordId}`;if(this.checkErrorTime(B))try{setTimeout(()=>{_c(document.body).then(s=>{const i=document.createElement("img"),a=s.toDataURL("image/png");i.src=a;const o=document.createElement("canvas"),c=o.getContext("2d");o.width=r!=null?r:200,o.height=n!=null?n:200,c.drawImage(i,0,0,o.width,o.height);const g={type:"Catcher",key:B,clientUrl:decodeURIComponent(window.location.href.slice(0,200)),screenShotBase64:a,...t,catcherId:A,clientInfo:window.navigator.userAgent,clientTitle:document.title};this.add(g,!0)})},1e3)}catch{return!1}return!0}collectErrors(A,t={isSend:!0}){const{key:r}=t,n=r||`${A.exceptionDetail}`;if(this.checkErrorTime(n))try{const B={...A,key:n,clientUrl:decodeURIComponent(window.location.href.slice(0,200)),clientInfo:window.navigator.userAgent};this.add(B)}catch(B){console.warn(B)}return!0}}let O;function ng(){O=new tg}var Bg={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"};sg(Bg);function sg(e){var A={};for(var t in e)e.hasOwnProperty(t)&&(A[e[t]]=t);return A}function pe(e,A=document){let t,r;try{r=A.querySelector(e)}catch{}try{if(!r){t=A.evaluate(e,A,null,XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,null);for(let n=0;n<t.snapshotLength;n++){const B=t.snapshotItem(n);if(B!==null){r=B;break}}}}catch{console.warn(`\u901A\u8FC7xpath${e}\u67E5\u627E\u5143\u7D20\u5931\u8D25`)}return r}function ig(e){return e.replace(/<[^>]*>?/gm,"")}class ag{constructor(){D(this,"events",{})}on(A,t){this.events[A]||(this.events[A]=[]),this.events[A].push(t)}off(A,t){if(!this.events[A])return;const r=this.events[A].indexOf(t);r>-1&&this.events[A].splice(r,1)}emit(A,...t){!this.events[A]||this.events[A].forEach(r=>{r(...t)})}}class og{constructor(){D(this,"observeInfo");D(this,"eventBus");D(this,"matchStringError",(A,t,r,n)=>{if(!A)return!1;const B=A.replace(/\s/g,"");if(n&&(n==null?void 0:n.length)>0&&B.indexOf(n.replace(/\s/g,""))>=0)return!1;for(let s=0;s<r.length;s+=1){const{keyword:i,keywordId:a}=r[s];if(i){const o=i.replace(/\s/g,""),c=ig(B);if(c.indexOf(o)>=0){console.warn("\u68C0\u6D4B\u5230\u5173\u952E\u5B57\u9519\u8BEF\u4FE1\u606F:",t),O==null||O.setErrors(t,{keyword:i,keywordId:a,exceptionDetail:c});break}}}return!0});this.eventBus=new ag,this.observeInfo={},this.init()}init(){document.addEventListener("DOMContentLoaded",()=>{this.traverseNode()})}manualTrigger(){this.traverseNode()}traverseNode(){var r;const A=((r=W.groupRels)==null?void 0:r.filter(n=>n.futureElementPath))||[];console.warn("rules",A);let t=[];wt(()=>{for(let n=0;n<A.length;n++){const B=A[n];B.sceneType==="1"&&(this.observeInfo[B.catcherId]=()=>{const s=pe(B.futureElementPath);this.matchStringError(s.outerHTML,B.catcherId,B.keywords)},this._observeExist(B,this.observeInfo[B.catcherId])),B.sceneType==="2"&&t.push(B),B.sceneType==="3"&&(this.observeInfo[B.catcherId]=()=>{const s=pe(B.futureElementPath);s&&this.matchStringError(s.outerHTML,B.catcherId,B.keywords)},this._observeVisible(B,this.observeInfo[B.catcherId]))}for(let n=0;n<t.length;n++){const B=t[n];this.observeInfo[B.catcherId]=()=>{const s=pe(B.futureElementPath);s&&(this.matchStringError(s.outerHTML,B.catcherId,B.keywords),this.eventBus.off(`${B.catchPath}-observer`,this.observeInfo[B.catcherId]),this._observeExist(B,()=>{const i=pe(B.futureElementPath);console.log("\u5378\u8F7D\u76D1\u542Czero2one\u91CD\u65B0\u76D1\u542Celem",B.futureElementPath),this.matchStringError(i.outerHTML,B.catcherId,B.keywords)}))},this._observeVisible(B,this.observeInfo[B.catcherId])}})}ignoreNodes(A){if(["SCRIPT","STYLE"].includes(A==null?void 0:A.nodeName))return!1;const t=A.classList;return!(["html2canvas-container"].some(r=>t.contains(r))||A.outerHTML.includes("Hidden Text"))}_observeExist(A,t){let r=!1,n=!1;const B=()=>new MutationObserver(s=>{for(const i of s){if(n)break;const a=i.addedNodes;for(let o=0;o<a.length&&!r;o++)wt(()=>this.eventBus.emit(`${A.futureElementPath}-observer`)),r=!0;n=!0}r=!1,n=!1});this.eventBus.on(`${A.futureElementPath}-observer`,t),this.observerNodeOnce(A.futureElementPath,B)}_observeVisible(A,t){let r=!1,n=!1;const{catchPath:B}=A,s=i=>new MutationObserver(a=>{for(const o of a){if(n)break;const c=o.addedNodes;for(let g=0;g<c.length&&!r;g++){const Q=c[g];if(!this.ignoreNodes(Q))break;console.log(`${i}-observer`,this.eventBus.events),wt(()=>this.eventBus.emit(`${i}-observer`)),r=!0,n=!0}}r=!1,n=!1});this.eventBus.on(`${B}-observer`,t),this.observerNodeOnce(B,s)}observerNodeOnce(A,t){const r=pe(A),n=(r==null?void 0:r.getAttribute("data-observed"))==="observed";if(r&&n&&this.observeInfo[A]&&this.observeInfo[A]++,!n){const B=t(A);B.observe(r,{childList:!0,subtree:!1}),this.observeInfo[A]=1,this.observeInfo[`${A}-observer`]=B,r.setAttribute("data-observed","observed")}}}const cg=()=>{const e=new og;return SA.catcherIns=e,e},gg=window.XMLHttpRequest,UB=({beginTime:e,endTime:A,url:t,status:r,responseText:n})=>{const{netCodePattern:B,ignoreUrls:s,netTimeout:i}=W.netConfig;if(!(B!=null&&B.test(r))||s.some(c=>t==null?void 0:t.includes(c)))return!1;const a=A-e;if(r===200&&a<i)return!1;const o=JSON.stringify({beginTime:e,endTime:A,url:t,responseText:n});return t&&(O==null||O.collectErrors({type:"Network",requestUrl:t,message:"Network connection lost",exceptionDetail:o,catcherId:W.netConfig.catcherId,errorCode:r,interfaceTime:a},{key:t})),!0},Qg=function(){const e=new gg,A=e.send;let t,r;return e.send=function(n){t=Date.now(),A.call(this,n)},e.addEventListener("load",function(){if(r=Date.now(),e.readyState==4){const{status:n,responseURL:B,responseText:s}=e;UB({beginTime:t,endTime:r,url:B,status:n,responseText:s.length>400?"\u8FD4\u56DE\u5185\u5BB9\u8FC7\u957F\uFF0C\u5DF2\u5FFD\u7565":s})}}),e},wg=()=>{const e=window.fetch;return function(...A){const t=Date.now();return e.apply(window,A).then(async r=>{const n=Date.now(),B=await r.clone().json(),s=await r.clone().text();return UB({beginTime:t,endTime:n,url:r.url,status:r.status,responseText:s.length>400?"\u8FD4\u56DE\u5185\u5BB9\u8FC7\u957F\uFF0C\u5DF2\u5FFD\u7565":B}),r})}},lg=()=>{window.fetch=wg(),window.XMLHttpRequest=Qg};class ug{constructor(){this.init()}init(){window.addEventListener("load",()=>{console.log("readyState",document.readyState),setTimeout(()=>{this.navigatorTiming()},100)})}navigatorTiming(){const A={},{performance:t}=window;let r=t.timing;if(A.fmp=0,t.getEntriesByType){const n=t.getEntriesByType("paint");if(n.length&&(A.fmp=n[n.length-1].startTime),window.PerformanceNavigationTiming){const B=t.getEntriesByType("navigation")[0];B&&(r=B)}}return A.fmp&&window.PerformanceNavigationTiming&&(A.fmp-=r.fetchStart),A.redirect=r.redirectEnd-r.redirectStart,A.dns=r.domainLookupEnd-r.domainLookupStart,A.tcp=r.connectEnd-r.connectStart,A.whiteScreen=r.responseStart-r.startTime,A.requestTime=r.responseStart-r.requestStart,A.responseTime=r.responseEnd-r.responseStart,A.domTree=r.domComplete-r.domInteractive,A.loadDuration=r.loadEventEnd-r.startTime,A.resouces=r.loadEventEnd-r.responseEnd,O.collectErrors({type:"Performance",exceptionDetail:JSON.stringify(A)},{key:Math.random().toString(36)}),A}}function fg(){return new ug}function Cg(){const{JavaScriptCatcher:e,PromiseCatcher:A,ResourceCatcher:t,NetworkCatcher:r}=W;e&&(window.onerror=(n,B,s,i,a)=>n!=null&&n.includes("ResizeObserver")?!1:(O==null||O.collectErrors({type:"JavaScript",exceptionDetail:a?a.toString():n,source:B,line:s,column:i,clientTitle:document.title}),!0)),A&&window.addEventListener("unhandledrejection",n=>{const B=n.reason instanceof Error?n.reason:new Error(n.reason);O==null||O.collectErrors({type:"Promise",message:B.message})}),t&&window.addEventListener("error",n=>{(n.target instanceof HTMLScriptElement||n.target instanceof HTMLImageElement)&&(O==null||O.collectErrors({type:"Resource",message:`Failed to load resource: ${n.target.src}`,source:n.target.src}))},!0),r&&window.addEventListener("offline",()=>{O==null||O.collectErrors({type:"Network",message:"Network connection lost"})})}function Ug(){const e=console.log;return function(...A){return!!W.debug&&e.apply(console,A)}}function Fg(){SA.log=Ug()}return Zc(),Fg(),ng(),rg(),lg(),{init:async e=>{if(SA.__catcher_init__)return;const{url:A,groupId:t}=e;W.init({...e}),Cg(),fg();let r={manualTrigger:()=>{}};try{const[n]=await Promise.all([$c(A,t)]);W.updated({rules:n.data}),r=cg()}catch(n){console.warn("error",n)}return SA.__catcher_init__=!0,r},_globalCatcher:SA}});
