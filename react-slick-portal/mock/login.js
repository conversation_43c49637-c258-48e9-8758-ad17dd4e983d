import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '../src/defaultSettings';

const proxy = {
  'POST /portal/LoginController/login.do': (req, res) => {
    res.send({
      resultCode: '0000',
      userCode: 'admin',
      sessionId: '63ba2e7c-84b5-4afe-84da-e69ef1f8e90d',
      errNum: 0,
      leftNum: 0,
      userInfo: {
        userId: 1,
        userCode: 'admin',
        loginCode: 'admin',
        userOrgId: 10008,
        password: 'XHId4XZ14vYQ82wb1b8ZAA==',
        userName: '管思坤',
        roleId: 92549,
        roleName: '运营管理',
        pwdErrCnt: 0,
        pwdNewtime: '2019-11-06 10:13:00',
        pwdEffectDays: 3600,
        sysCode: '727007',
        pwdSmsTel: 12321324329,
        pwdStatus: '1100',
        statusCd: '1000',
        extParams: { userOrgId: 10008 },
        mobilePhone: '***********',
      },
      orgInfo: {
        orgId: 10008,
        orgName: '四川省',
        orgCode: '10008',
        parentOrgId: -1,
        orgLevel: 1,
        lanId: -1,
      },
      staffInfo: {
        staffId: 3044336,
        staffCode: 'admin',
        staffName: '王兰军',
        partyId: 3,
        staffType: '1000',
        staffTypeName: '自建员工',
        staffAccount: 'wlj',
        statusCd: '1000',
        saleStaffCode: 'Y62010073906',
        regionId: 731,
      },
      depInfos: [
        {
          depId: 142,
          parentDepId: '127',
          departmentName: '成都分公司',
          depLevel: 2,
          pathCode: '142',
          isMainDpt: '0',
        },
      ],
      portalRoles: [
        {
          sysRoleId: 92548,
          sysRoleName: '公司领导',
          sysRoleCode: 'TYMH0081',
          sysRoleType: '1100',
          sysRoleDesc: '公司领导',
          sysCode: 'TYMH',
          statusCd: '1000',
          defaultFlag: false,
        },
        {
          sysRoleId: 92549,
          sysRoleName: '运营管理',
          sysRoleCode: 'TYMH0082',
          sysRoleType: '1100',
          sysRoleDesc: '运营管理',
          sysCode: 'TYMH',
          statusCd: '1000',
          defaultFlag: true,
        },
        {
          sysRoleId: 92550,
          sysRoleName: '一线营销',
          sysRoleCode: 'TYMH0083',
          sysRoleType: '1000',
          sysRoleDesc: '一线营销',
          sysCode: 'TYMH',
          statusCd: '1000',
          defaultFlag: false,
        },
        {
          sysRoleId: 92791,
          sysRoleName: '系统管理员',
          sysRoleCode: 'XTGLY001',
          sysRoleType: '1100',
          sysRoleDesc: '系统管理员',
          sysCode: 'TYMH',
          statusCd: '1000',
          defaultFlag: true,
        },
      ],
      changePwdHintFlag: false,
      pwdLeftDays: 3570,
      loginIp: '*************',
    });
  },
  'POST /orgauth/SystemUserController/updateSysUserPwd.do': (req, res) => {
    res.send({ resultCode: '0000', resultMsg: '修改成功！' });
  },
  'GET /portal/LoginController/status.do': (req, res) => {
    res.status(200).send({
      sessionTimeout: false,
      lockFlag: false,
      popoverMessages: [],
      remindMessageCount: 0,
    });
  },
  'GET /portal/LoginController/getVcodeFlag.do': (req, res) => {
    res.status(200).send(false);
  },
  'GET /orgauth/BulletinRcvObjRelController/getUnReadBulletin.do': (req, res) => {
    res.status(200).send({ resultCode: '0000', resultMsg: '修改成功！' });
  },
  'POST /sso/login.do': (req, res) => {
    res.send({
      resultCode: '0',
      resultObject: {
        userCode: 'admin',
        sessionId: '63ba2e7c-84b5-4afe-84da-e69ef1f8e90d',
        errNum: 0,
        leftNum: 0,
        userInfo: {
          userId: 1,
          userCode: 'admin',
          loginCode: 'admin',
          userOrgId: 10008,
          password: 'XHId4XZ14vYQ82wb1b8ZAA==',
          userName: '管思坤',
          roleId: 92549,
          roleName: '运营管理',
          pwdErrCnt: 0,
          pwdNewtime: '2019-11-06 10:13:00',
          pwdEffectDays: 3600,
          sysCode: '727007',
          pwdSmsTel: 12321324329,
          pwdStatus: '1100',
          statusCd: '1000',
          extParams: { userOrgId: 10008 },
          mobilePhone: '***********',
        },
        orgInfo: {
          orgId: 10008,
          orgName: '四川省',
          orgCode: '10008',
          parentOrgId: -1,
          orgLevel: 1,
          lanId: -1,
        },
        staffInfo: {
          staffId: 3044336,
          staffCode: 'admin',
          staffName: '王兰军',
          partyId: 3,
          staffType: '1000',
          staffTypeName: '自建员工',
          staffAccount: 'wlj',
          statusCd: '1000',
          saleStaffCode: 'Y62010073906',
          regionId: 731,
        },
        portalRoles: [
          {
            sysRoleId: 92548,
            sysRoleName: '公司领导',
            sysRoleCode: 'TYMH0081',
          },
          {
            sysRoleId: 92549,
            sysRoleName: '运营管理',
            sysRoleCode: 'TYMH0082',
          },
          {
            sysRoleId: 92550,
            sysRoleName: '一线营销',
            sysRoleCode: 'TYMH0083',
          },
          {
            sysRoleId: 92791,
            sysRoleName: '系统管理员',
            sysRoleCode: 'XTGLY001',
          },
        ],
      },
    });
  },
};

export default delay(proxy, defaultSettings.delay);
