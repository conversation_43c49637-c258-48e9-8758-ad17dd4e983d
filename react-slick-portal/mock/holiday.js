import mockjs from 'mockjs';
import moment from 'moment';

// mock数据
const holidays = mockjs.mock({
  'list|30': [{
    'id|+1': 1,
    'date': function() {
      const now = moment();
      const year = now.year();
      const month = now.month();
      const day = this.id % 30 + 1;
      return moment(new Date(year, month, day)).format('YYYY-MM-DD');
    },
    'workTime|0-8.1': 0,
    'totalTime': 8.0,
    'isHoliday|1-4': false,
    'remark': function() {
      return mockjs.Random.csentence(5, 20);
    },
  }],
});

function getHolidaysByDateRange(req, res) {
  const { startDate, endDate } = req.query;
  
  const result = holidays.list.filter(item => {
    const itemDate = moment(item.date);
    return itemDate.isSameOrAfter(startDate) && itemDate.isSameOrBefore(endDate);
  });
  
  return res.json({
    success: true,
    resultCode: 'TRUE',
    resultObject: result,
  });
}

function addHoliday(req, res) {
  const body = req.body;
  const newHoliday = {
    id: mockjs.Random.guid(),
    ...body,
  };
  
  holidays.list.push(newHoliday);
  
  return res.json({
    success: true,
    resultCode: 'TRUE',
    resultObject: newHoliday,
  });
}

function batchAddHolidays(req, res) {
  const { dates, ...restProps } = req.body;
  
  if (!dates || !Array.isArray(dates) || dates.length === 0) {
    return res.status(400).json({
      success: false,
      resultCode: 'FALSE',
      resultMsg: '日期数组不能为空',
    });
  }
  
  // 检查是否有重复日期
  const existingDates = new Set(holidays.list.map(h => h.date));
  const newHolidays = [];
  
  dates.forEach(date => {
    if (!existingDates.has(date)) {
      const newHoliday = {
        id: mockjs.Random.guid(),
        date,
        ...restProps,
      };
      
      holidays.list.push(newHoliday);
      newHolidays.push(newHoliday);
      existingDates.add(date);
    }
  });
  
  return res.json({
    success: true,
    resultCode: 'TRUE',
    resultObject: newHolidays,
    totalAdded: newHolidays.length,
    totalRequested: dates.length,
  });
}

function updateHoliday(req, res) {
  const body = req.body;
  const { id } = body;
  
  const index = holidays.list.findIndex(item => item.id === id);
  
  if (index > -1) {
    holidays.list[index] = {
      ...holidays.list[index],
      ...body,
    };
    
    return res.json({
      success: true,
      resultCode: 'TRUE',
      resultObject: holidays.list[index],
    });
  }
  
  return res.status(404).json({
    success: false,
    resultCode: 'FALSE',
    resultMsg: '节假日记录不存在',
  });
}

function deleteHoliday(req, res) {
  const { id } = req.body;
  
  const index = holidays.list.findIndex(item => item.id === id);
  
  if (index > -1) {
    holidays.list.splice(index, 1);
    
    return res.json({
      success: true,
      resultCode: 'TRUE',
    });
  }
  
  return res.status(404).json({
    success: false,
    resultCode: 'FALSE',
    resultMsg: '节假日记录不存在',
  });
}

export default {
  'GET /portal/HolidayController/queryHolidaysByDateRange.do': getHolidaysByDateRange,
  'POST /portal/HolidayController/addHoliday.do': addHoliday,
  'POST /portal/HolidayController/batchAddHolidays.do': batchAddHolidays,
  'POST /portal/HolidayController/updateHoliday.do': updateHoliday,
  'POST /portal/HolidayController/deleteHoliday.do': deleteHoliday,
}; 