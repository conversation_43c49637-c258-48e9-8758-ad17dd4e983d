import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '../src/defaultSettings';

const proxy = {
  'POST /portal/TraceInfoController/traceLog.do': (req, res) => {
    res.send(true);
  },
  'POST /orgauth/SystemActionLogController/collectSystemActionLog.do': (req, res) => {
    res.send({ resultCode: '0000' });
  },
};

export default delay(proxy, defaultSettings.delay);
