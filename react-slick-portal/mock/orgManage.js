import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '../src/defaultSettings';

const proxy = {
  'POST /orgauth/StaffController/selectStaffGridData.do': (req, res) => {
    const { pageSize, pageNum } = req.body;
    res.status(200).send(
      mockjs.mock({
        pageNum: 1,
        pageSize: 5,
        size: 1,
        startRow: 1,
        endRow: 1,
        total: 2,
        pages: 1,
        list: [
          {
            id: 3044336,
            statusDate: '2017-03-02 17:53:17',
            createDate: '2012-07-01 16:16:25',
            createStaff: 3000915,
            updateDate: '2017-08-03 14:37:00',
            updateStaff: 3044336,
            entityAttrs: [],
            staffId: 3044336,
            partyId: 3,
            staffCode: 'admin',
            staffAccount: 'wlj',
            depId: 142,
            staffType: '1000',
            staffName: '王兰军',
            staffDesc: '王兰军',
            statusCd: '1000',
            mobilePhone: '***********',
            salesstaffCode: 'Y62010073906',
            staffTypeName: '自建员工',
            createType: '1100',
            commonRegionName: '',
            departmentName: '成都分公司',
            statusCdName: '有效',
          },
          {
            id: 3044337,
            statusDate: '2017-03-02 17:53:17',
            createDate: '2012-07-01 16:16:25',
            createStaff: 3000916,
            updateDate: '2017-08-03 14:37:00',
            updateStaff: 3044337,
            entityAttrs: [],
            staffId: 3044337,
            partyId: 4,
            staffCode: 'test',
            staffAccount: 'ly',
            depId: 143,
            staffType: '1001',
            staffName: '柯南',
            staffDesc: '柯南',
            mobilePhone: '***********',
            statusCd: '1001',
            salesstaffCode: 'Y62010073907',
            staffTypeName: '自建员工',
            createType: '1200',
            commonRegionName: '',
            departmentName: '重庆分公司',
            statusCdName: '有效',
          },
        ],
        prePage: 0,
        nextPage: 0,
        isFirstPage: true,
        isLastPage: true,
        hasPreviousPage: false,
        hasNextPage: false,
        navigatePages: 8,
        navigatepageNums: [1],
        navigateFirstPage: 1,
        navigateLastPage: 1,
        firstPage: 1,
        lastPage: 1,
      })
    );
  },
  'POST /orgauth/StaffDepRelController/selectStaffDepRelGridData.do': (req, res) => {
    const { pageSize, pageNum } = req.body;
    res.status(200).send(
      mockjs.mock({
        pageNum: 1,
        pageSize: 5,
        size: 1,
        startRow: 1,
        endRow: 1,
        total: 1,
        pages: 1,
        list: [
          {
            id: 1102,
            staffId: 3044336,
            depId: 142,
            isMainDpt: '0',
            statusCd: '1000',
            department: {
              id: 142,
              parentDepId: '127',
              departmentName: '成都分公司',
              areaCode: '2',
              areaName: '成都',
              departmentOrder: '1',
              depLevel: 2,
              pathName: '成都分公司',
              createType: '1100',
              statusCd: '1000',
              // children: [],
              statusCdName: '有效',
              depId: 142,
            },
            statusCdName: '有效',
            staffDepRelId: 1102,
            isMainDptName: '否',
          },
        ],
        prePage: 0,
        nextPage: 0,
        isFirstPage: true,
        isLastPage: true,
        hasPreviousPage: false,
        hasNextPage: false,
        navigatePages: 8,
        navigatepageNums: [1],
        navigateFirstPage: 1,
        navigateLastPage: 1,
        firstPage: 1,
        lastPage: 1,
      })
    );
  },
  'POST /orgauth/SystemUserController/selectGridData.do': (req, res) => {
    const { pageSize, pageNum } = req.body;
    res.status(200).send(
      mockjs.mock({
        pageNum: 1,
        pageSize: 5,
        size: 1,
        startRow: 1,
        endRow: 1,
        total: 1,
        pages: 1,
        list: [
          {
            id: 1,
            statusDate: '2019-12-09 14:05:50',
            createDate: '2018-07-20 15:52:31',
            updateDate: '2019-12-08 10:47:23',
            updateStaff: 1,
            entityAttrs: [],
            sysUserId: 1,
            staffId: 3044336,
            sysUserCode: 'admin',
            loginCode: 'admin',
            password: 'XHId4XZ14vYQ82wb1b8ZAA==',
            pwdErrCnt: 0,
            pwdSmsTel: 12321324329,
            pwdStatus: '1100',
            pwdNewtime: '2019-11-06 10:13:00',
            pwdEffectDays: 60,
            sysCode: '727007',
            loginedNum: 13497,
            sysUserDesc: '管理员',
            effDate: '2018-07-20 15:52:31',
            expDate: '2030-12-31 00:00:00',
            statusCd: '1000',
            userName: '管思坤',
            userOrgId: 10008,
            mobilePhone: '18814127729',
            userType: '1',
            sysCodeName: '统一任务管理',
            pwdStatusName: '',
            statusCdName: '有效',
          },
        ],
        prePage: 0,
        nextPage: 0,
        isFirstPage: true,
        isLastPage: true,
        hasPreviousPage: false,
        hasNextPage: false,
        navigatePages: 8,
        navigatepageNums: [1],
        navigateFirstPage: 1,
        navigateLastPage: 1,
        firstPage: 1,
        lastPage: 1,
      })
    );
  },
  'POST /orgauth/StaffController/updateStaff.do': (req, res) => {
    res.status(200).send(
      mockjs.mock({
        retCode: 'TRUE',
      })
    );
  },
  'POST /orgauth/StaffController/addStaff.do': (req, res) => {
    res.status(200).send(
      mockjs.mock({
        retCode: 'TRUE',
      })
    );
  },

  'POST /orgauth/SystemUserController/checkValidUser.do': (req, res) => {
    res.status(200).send(
      mockjs.mock({
        resultCode: '0000',
      })
    );
  },

  'POST /orgauth/StaffController/invalidateStaffs.do': (req, res) => {
    res.status(200).send(mockjs.mock({ value: 1 }));
  },

  'POST /orgauth/DepartmentController/qryManageDepGridData.do': (req, res) => {
    res.status(200).send(
      mockjs.mock({
        pageNum: 1,
        pageSize: 4,
        size: 4,
        startRow: 1,
        endRow: 4,
        total: 4,
        pages: 72,
        list: [
          {
            id: 142,
            parentDepId: '127',
            departmentName: '成都分公司',
            areaCode: '2',
            areaName: '成都',
            departmentOrder: '1',
            depLevel: 2,
            pathCode: '142',
            pathName: '成都分公司',
            orgId: 2,
            createType: '1100',
            statusCd: '1000',
            // children: [],
            statusCdName: '有效',
            depId: 142,
          },
          {
            id: 14895,
            parentDepId: '5450',
            departmentName: '订单组',
            areaCode: '2',
            areaName: '成都',
            departmentOrder: '1203201200',
            depLevel: 2,
            pathCode: '142.154.5450.14895',
            pathName: '成都分公司/政企客户部/业务管理及风控室/订单组',
            orgId: 2,
            createType: '1100',
            statusCd: '1000',
            // children: [],
            statusCdName: '有效',
            depId: 14895,
          },
          {
            id: 10908,
            parentDepId: '3797',
            departmentName: '高新_综合部',
            areaCode: '119770',
            areaName: '高新',
            departmentOrder: '1218200',
            depLevel: 3,
            pathCode: '142.3797.10908',
            pathName: '成都分公司/高新分公司/高新_综合部',
            orgId: 119770,
            createType: '1100',
            statusCd: '1000',
            // children: [],
            statusCdName: '有效',
            depId: 10908,
          },
          {
            id: 10909,
            parentDepId: '3797',
            departmentName: '高新_市场部',
            areaCode: '119770',
            areaName: '高新',
            departmentOrder: '1218201',
            depLevel: 3,
            pathCode: '142.3797.10909',
            pathName: '成都分公司/高新分公司/高新_市场部',
            orgId: 119770,
            createType: '1100',
            statusCd: '1000',
            // children: [],
            statusCdName: '有效',
            depId: 10909,
          },
        ],
        prePage: 0,
        nextPage: 2,
        isFirstPage: true,
        isLastPage: false,
        hasPreviousPage: false,
        hasNextPage: true,
        navigatePages: 8,
        navigatepageNums: [1, 2, 3, 4, 5, 6, 7, 8],
        navigateFirstPage: 1,
        navigateLastPage: 8,
        firstPage: 1,
        lastPage: 8,
      })
    );
  },
};

export default delay(proxy, defaultSettings.delay);
