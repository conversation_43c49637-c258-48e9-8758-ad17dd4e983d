import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '../src/defaultSettings';

const proxy = {
  'GET /orgauth/SystemInfoController/getSysCodeBySysNbr.do': (req, res) => {
    const { query } = req;
    if (query.sysNbr === 'APP0') {
      res.send('727004');
    } else if (query.sysNbr === 'RWGL') {
      res.send('727007');
    }
  },
  'GET /orgauth/FuncMenuController/selectMenuByLetter.do': (req, res) => {
    res.send([
      [
        {
          title: 'A-B',
          menus: [
            {
              privId: 2592044,
              menuId: 4380504,
              menuName: 'APP快捷菜单模板',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 3610501,
              menuIndex: 9,
              urlAddr: '/customize/appShortcutMenu',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'APPKJCDMB',
              iconUrl: 'icon-gene-man-manager',
              menuCode: 'TYMH_MENU_APPSHORTCUTMENU',
            },
            {
              privId: 992089,
              menuId: 2770294,
              menuName: '编码块与记录配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770292,
              menuIndex: 2,
              urlAddr: 'mrssweb/modules/analysis/eventCfg/views/eventCfgView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'BMKYJLPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_EVENT_CFG',
            },
            {
              privId: 89122,
              menuId: 276910,
              menuName: '拜访任务',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276893,
              menuIndex: 2,
              menuDesc: '驻点拜访',
              urlAddr:
                '[iframe]http://122.192.9.52:20121/operator/dist/index.html#/cust/manage/custVisitTask?viewMode=inner&language=zh-CN',
              systemCode: '727007',
              statusCd: '1000',
              firstLetter: 'BFRW',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'RWGL_MENU_TASK003_1',
            },
            {
              privId: 992175,
              menuId: 2770379,
              menuName: '报文配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770292,
              menuIndex: 4,
              urlAddr: 'mrssweb/modules/infReportGenerate/views/infReportGenerateMainView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'BWPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_infReportGenerate',
            },
          ],
        },
        {
          title: 'C',
          menus: [
            {
              privId: 89172,
              menuId: 276992,
              menuName: '查勘',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 29,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'CK',
              iconUrl: 'reverse_file_icon',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_EXPLORATION',
            },
            {
              privId: 991781,
              menuId: 2769982,
              menuName: '产商品配置',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769981,
              menuIndex: 1,
              menuDesc: '产商品配置',
              urlAddr:
                '[iframe]http://172.16.81.126:9999/cpc-web/iframe.html?pc/modules/offerConfig/views/OfferConfigView&lang=zh&sysUserCode={SYS_USER_CODE}',
              systemCode: '756142',
              statusCd: '1000',
              firstLetter: 'CSPPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'CPC_MENU_0001',
            },
            {
              privId: 3212042,
              menuId: 5190507,
              menuName: '产商品配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190502,
              menuIndex: 2,
              urlAddr:
                '[iframe]http://172.16.81.126:9999/cpc-web/iframe.html?pc/modules/offerConfig/views/OfferConfigView&lang=zh&sysUserCode={SYS_USER_CODE}',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'CSPPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_CPC_002',
            },
            {
              privId: 991821,
              menuId: 2770021,
              menuName: '产商品审批',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769981,
              menuIndex: 5,
              menuDesc: '待办待阅',
              urlAddr:
                '[iframe]http://172.16.81.126:9999/cpc-web/iframe.html?pc/modules/todocenter/todoandread/views/OrderTabView&sysUserCode={SYS_USER_CODE}',
              systemCode: '756142',
              statusCd: '1000',
              firstLetter: 'CSPSP',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'CPC_MENU_0005',
            },
            {
              privId: 992094,
              menuId: 2770299,
              menuName: '插件提取',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770264,
              menuIndex: 6,
              urlAddr: 'mrssweb/modules/factor/factorplugin/views/factorPlugin',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'CJTQ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_FACTOR_PLUGIN',
            },
            {
              privId: 992095,
              menuId: 2770300,
              menuName: '错误码管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770264,
              menuIndex: 7,
              urlAddr: 'mrssweb/modules/factor/errorcode/views/ErrorCodeView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'CWMGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_ERROR_CODE',
            },
            {
              privId: 992100,
              menuId: 2770306,
              menuName: '场景管理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770304,
              menuIndex: 2,
              urlAddr: '[iframe]http://************:9004/#/knowledgebase/sceneTab',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'CJGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XIAOU',
            },
            {
              privId: 2792043,
              menuId: 4580503,
              menuName: '产品行业模板',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769981,
              menuIndex: 8,
              menuDesc: '产品行业模板',
              urlAddr:
                '[iframe]http://172.16.81.126:9999/cpc-web/iframe.html?pc/modules/prodTradeTpl/views/ProdTradeTplView&sysUserCode={SYS_USER_CODE}',
              systemCode: '756142',
              statusCd: '1000',
              firstLetter: 'CPXYMB',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'CPC_MENU_0008',
            },
            {
              privId: 89148,
              menuId: 276958,
              menuName: '产品属性管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276956,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=822',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'CPSXGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DTSXGL_2',
            },
            {
              privId: 992005,
              menuId: 2780366,
              menuName: '菜单管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780365,
              menuIndex: 1,
              urlAddr: '/authManage/menuManage',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'CDGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_CDGL',
            },
            {
              privId: 992187,
              menuId: 2770393,
              menuName: '产品模板管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276988,
              menuIndex: 3,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=823',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'CPMBGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_prodAttrMgr',
            },
            {
              privId: 992181,
              menuId: 2770386,
              menuName: '酬金项目类型管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770301,
              menuIndex: 5,
              urlAddr: 'mrssweb/modules/acctitemtype/views/AcctItemTypeView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'CJXMLXGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_AcctItemTypeView',
            },
            {
              privId: 992183,
              menuId: 2770388,
              menuName: '酬金政策管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770301,
              menuIndex: 7,
              urlAddr: 'mrssweb/modules/rewardpolicy/manage/views/RewardPolicyView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'CJZCGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_RewardPolicyView',
            },
            {
              privId: 992184,
              menuId: 2770389,
              menuName: '酬金规则实例',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770301,
              menuIndex: 8,
              urlAddr:
                'mrssweb/modules/rewardpolicy/instsettleruleissue/views/InstSettleRuleIssueView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'CJGZSL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_ISRuleIssue',
            },
          ],
        },
        {
          title: 'D',
          menus: [
            {
              privId: 89205,
              menuId: 277025,
              menuName: '订单与合同稽核',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769881,
              menuIndex: 5,
              menuDesc: '订单与合同稽核',
              urlAddr: 'audit-web/audit/modules/OrderInfo/views/OrderAndContractInfoMainView.js',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDYHTJH',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_AUDIT_CATALOG',
            },
            {
              privId: 3212056,
              menuId: 5190521,
              menuName: '订单分拣策略管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190505,
              menuIndex: 5,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3282',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDFJCLGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ORDER_005',
            },
            {
              privId: 3212055,
              menuId: 5190520,
              menuName: '订单环节超时配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190505,
              menuIndex: 4,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3124',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDHJCSPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ORDER_004',
            },
            {
              privId: 3212054,
              menuId: 5190519,
              menuName: '订单岗位管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190505,
              menuIndex: 3,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3010',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDGWGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ORDER_003',
            },
            {
              privId: 3212053,
              menuId: 5190518,
              menuName: '订单模版管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190505,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3122',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDMBGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ORDER_002',
            },
            {
              privId: 3212052,
              menuId: 5190517,
              menuName: '订单属性规格管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190505,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=38',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDSXGGGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ORDER_001',
            },
            {
              privId: 992071,
              menuId: 2770274,
              menuName: '订单分拣策略管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770225,
              menuIndex: 5,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3282',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDFJCLGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DDFJCLGL',
            },
            {
              privId: 992067,
              menuId: 2770270,
              menuName: '订单视图',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770161,
              menuIndex: 5,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3343',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDST',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DDST',
            },
            {
              privId: 992066,
              menuId: 2770269,
              menuName: '订单性能监控',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770161,
              menuIndex: 4,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3342',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDXNJK',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DDXNJK',
            },
            {
              privId: 89127,
              menuId: 276915,
              menuName: '订单任务',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276898,
              menuIndex: 7,
              menuDesc: '订单任务',
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3009',
              systemCode: '727007',
              statusCd: '1000',
              firstLetter: 'DDRW',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'RWGL_MENU_TASK008_1',
            },
            {
              privId: 991705,
              menuId: 2769888,
              menuName: '订单模板管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770225,
              menuIndex: 6,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3122',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDMBGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DDMB',
            },
            {
              privId: 86144,
              menuId: 274660,
              menuName: '导出操作日志',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274652,
              parMenuName: '审批管理',
              urlAddr: '[iframe]http://122.192.9.52:20121/operator/cust/exportOperationLog.html',
              systemCode: '727002',
              statusCd: '1000',
              firstLetter: 'DCCZRZ',
              iconUrl: 'icon-gene-man-manager',
              menuCode: 'KGXT_10019',
            },
            {
              privId: 89158,
              menuId: 276974,
              menuName: '订单中心稽核',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769881,
              menuIndex: 1,
              menuDesc: '稽核点管理',
              urlAddr: 'audit-web/audit/modules/OrderInfo/views/OrderInfoMainView.js',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDZXJH',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_AUDIT_POINT',
            },
            {
              privId: 89161,
              menuId: 276980,
              menuName: '订单查询',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769885,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3022',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDCX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DDCX',
            },
            {
              privId: 89164,
              menuId: 276983,
              menuName: '订单任务',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276975,
              menuIndex: 3,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3009',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDRW',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DDRWD',
            },
            {
              privId: 991703,
              menuId: 2769886,
              menuName: '订单超时监控',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770161,
              menuIndex: 3,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3123',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DDCSJK',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DDCSJK',
            },
            {
              privId: 89171,
              menuId: 276991,
              menuName: '订单',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 28,
              urlAddr: 'http://122.192.9.52:20121/dist/#/myOrder',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'DD',
              iconUrl: 'main_money',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_ORDER',
            },
            {
              privId: 992008,
              menuId: 2780369,
              menuName: '登录状态管理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780364,
              menuIndex: 4,
              urlAddr: '/account',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'DLZTGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DLZTGL',
            },
          ],
        },
        {
          title: 'E-F',
          menus: [
            {
              privId: 992090,
              menuId: 2770295,
              menuName: '分拣过滤',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770264,
              menuIndex: 2,
              urlAddr: 'mrssweb/modules/factor/sortfilter/views/SortFilterView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'FJGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_SORT_FILTER',
            },
            {
              privId: 88898,
              menuId: 276637,
              menuName: '发现',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 10,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'FX',
              iconUrl: 'main_discovery',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_DISCOVERY',
            },
            {
              privId: 88896,
              menuId: 276635,
              menuName: '分享',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 8,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'FX',
              iconUrl: 'main_share',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_SHARE',
            },
            {
              privId: 992208,
              menuId: 2770428,
              menuName: '服务管理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770088,
              menuIndex: 10,
              urlAddr:
                '[iframe]http://122.192.9.52:20121/chgc-web/iframe.html?chgc/modules/chargingcenter/servicecontrol/views/ServiceControlView&lang=zh&version=firekylin',
              systemCode: '727011',
              statusCd: '1000',
              firstLetter: 'FWGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'BILLING_MENU_SERVICE',
            },
            {
              privId: 992178,
              menuId: 2770383,
              menuName: '范围分类管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770301,
              menuIndex: 2,
              urlAddr: 'mrssweb/modules/analysis/stscope/views/StScopeCataView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'FWFLGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_StScopeCataView',
            },
          ],
        },
        {
          title: 'G',
          menus: [
            {
              privId: 88959,
              menuId: 276738,
              menuName: '公告',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 6,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'GG',
              iconUrl: 'main_notice',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_NOTICE',
            },
            {
              privId: 992096,
              menuId: 2770302,
              menuName: '规则模板配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770301,
              menuIndex: 1,
              urlAddr: 'mrssweb/modules/rewardpolicy/settlerule/views/SettleRuleView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'GZMBPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_SETTLE_RULE',
            },
            {
              privId: 992043,
              menuId: 2770244,
              menuName: '岗位管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770243,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=356',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'GWGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_GWGL1',
            },
            {
              privId: 3052041,
              menuId: 5000502,
              menuName: '岗位管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5000501,
              menuIndex: 1,
              urlAddr:
                '[iframe]http://122.192.9.52:20121/project/dist/index.html#/portal/postInfo?viewMode=inner&language=zh-CN',
              systemCode: '727015',
              statusCd: '1000',
              firstLetter: 'GWGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'PROJECT_MENU_11',
            },
            {
              privId: 992195,
              menuId: 2770402,
              menuName: '公参管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770395,
              menuIndex: 3,
              urlAddr: 'mrssweb/modules/commonparam/cachedatainfo/views/CacheDataInfoView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'GCGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_CacheDataInfos',
            },
            {
              privId: 992177,
              menuId: 2770382,
              menuName: '告警规则',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770292,
              menuIndex: 6,
              urlAddr: 'mrssweb/modules/warndataproducer/warningRule/views/warningRuleView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'GJGZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_warningRuleView',
            },
            {
              privId: 992188,
              menuId: 2770394,
              menuName: '轨迹查询',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770390,
              menuIndex: 3,
              urlAddr: 'mrssweb/modules/settlerollback/settle/views/SettleDetailView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'GJCX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_SettleDetailView',
            },
            {
              privId: 3272042,
              menuId: 5240503,
              menuName: '工作台1',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5240502,
              menuIndex: 1,
              menuDesc: 'react',
              urlAddr: '/dashboard/index2',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'GZT1',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1001',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_dashboardreact2',
            },
            {
              privId: 992007,
              menuId: 2780368,
              menuName: '公告管理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780364,
              menuIndex: 3,
              urlAddr: '/bulletin/manage',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'GGGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_GGGL',
            },
            {
              privId: 89168,
              menuId: 276987,
              menuName: '岗位管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276848,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=356',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'GWGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_6',
            },
          ],
        },
        {
          title: 'H',
          menus: [
            {
              privId: 991762,
              menuId: 2769963,
              menuName: '划配规则管理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769961,
              menuIndex: 1,
              menuDesc: '划配规则管理',
              urlAddr:
                '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocRuleView',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HPGZGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_RULE_MGR',
            },
            {
              privId: 88966,
              menuId: 276745,
              menuName: '合同',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 13,
              urlAddr: 'http://122.192.9.52:20121/dist/#/contractList',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'HT',
              iconUrl: 'home_toker_list_archiving',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_CONTRACT',
            },
            {
              privId: 89191,
              menuId: 277009,
              menuName: '合同中心稽核',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769882,
              menuIndex: 3,
              menuDesc: '合同中心稽核',
              urlAddr: 'audit-web/audit/modules/ContractInfo/views/ContractInfoMainView.js',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTZXJH',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ENTERPRISE_AUDTI_NEW',
            },
            {
              privId: 89192,
              menuId: 277010,
              menuName: '活动配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770043,
              menuIndex: 1,
              menuDesc:
                '[iframe]http://10.45.47.18:3433/ire/activityConfigManage/marketingActivityList/?sso=1',
              urlAddr:
                '[iframe]http://172.16.81.126:9999/ire/activityConfigManage/marketingActivityList/?sso=1',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HDPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_HUODONG',
            },
            {
              privId: 1602040,
              menuId: 3390500,
              menuName: '合同条款管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276949,
              menuIndex: 5,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=2200',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTTKGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_AGREE_TERM_MANAGER',
            },
            {
              privId: 991741,
              menuId: 2769932,
              menuName: '活动模板管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770043,
              menuIndex: 7,
              menuDesc: '活动模板管理',
              urlAddr: '[iframe]http://122.192.9.52:20121/ire/activityBase/activityTemplate/?sso=1',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HDMBGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_HuoDongMuBan',
            },
            {
              privId: 991742,
              menuId: 2769933,
              menuName: '活动评估',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770044,
              menuIndex: 3,
              menuDesc: '活动评估',
              urlAddr: '[iframe]http://122.192.9.52:20121/ire/activityScheduling/?sso=1',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HDPG',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_HuoDongPingGu',
            },
            {
              privId: 3212051,
              menuId: 5190516,
              menuName: '合同模版管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190504,
              menuIndex: 5,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=2041',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTMBGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_BCM_005',
            },
            {
              privId: 3212050,
              menuId: 5190515,
              menuName: '合同审核岗位管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190504,
              menuIndex: 4,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=356',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTSHGWGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_BCM_004',
            },
            {
              privId: 3212049,
              menuId: 5190514,
              menuName: '合同环节处理配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190504,
              menuIndex: 3,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=396',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTHJCLPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_BCM_003',
            },
            {
              privId: 3212048,
              menuId: 5190513,
              menuName: '合同产品属性管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190504,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=822',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTCPSXGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_BCM_002',
            },
            {
              privId: 3212047,
              menuId: 5190512,
              menuName: '合同属性规格管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190504,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=821',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTSXGGGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_BCM_001',
            },
            {
              privId: 992069,
              menuId: 2770272,
              menuName: '环节岗位管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770225,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3010',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HJGWGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DD_HJGWGL',
            },
            {
              privId: 991561,
              menuId: 2769741,
              menuName: '合同预警及续签',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276954,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=2120',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTYJJXQ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_CONTRACT_REMIND',
            },
            {
              privId: 992044,
              menuId: 2770245,
              menuName: '环节处理配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770243,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=396',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HJCLPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_YYGL2',
            },
            {
              privId: 89050,
              menuId: 276849,
              menuName: '环节处理配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276848,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=396',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'HJCLPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_396',
            },
            {
              privId: 991801,
              menuId: 2770001,
              menuName: '划配规则配置',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769961,
              menuIndex: 2,
              menuDesc: '划配规则配置',
              urlAddr:
                '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocRuleCfgView',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HPGZPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_RULE_CFG',
            },
            {
              privId: 89126,
              menuId: 276914,
              menuName: '合同任务',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276897,
              menuIndex: 5,
              menuDesc: '合同任务',
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=162',
              systemCode: '727007',
              statusCd: '1000',
              firstLetter: 'HTRW',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'RWGL_MENU_TASK007_1',
            },
            {
              privId: 89131,
              menuId: 276930,
              menuName: '合同起草',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276949,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=2023',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTQC',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XYGL_1',
            },
            {
              privId: 89132,
              menuId: 276931,
              menuName: '合同全视图',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276949,
              menuIndex: 3,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=1981',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTQST',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XYGL_3',
            },
            {
              privId: 89143,
              menuId: 276950,
              menuName: '合同草稿',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276949,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=2024',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTCG',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XYGL_2',
            },
            {
              privId: 89144,
              menuId: 276951,
              menuName: '合同模板',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276949,
              menuIndex: 4,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=2041',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTMB',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XYGL_4',
            },
            {
              privId: 991923,
              menuId: 2770124,
              menuName: '合同管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770122,
              parMenuName: '合同信息管理',
              menuIndex: 1,
              urlAddr: 'bpp/modules/contractManagement/views/CPContractQueryView.js',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HTGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_22333',
            },
            {
              privId: 991704,
              menuId: 2769887,
              menuName: '环节超时管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770225,
              menuIndex: 4,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3124',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'HJCSGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_HJCSPZ',
            },
          ],
        },
      ],
      [
        {
          title: 'I-J',
          menus: [
            {
              privId: 89120,
              menuId: 276908,
              menuName: '集团营销',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276892,
              menuIndex: 8,
              menuDesc: '集团营销',
              urlAddr: 'modules/routinetask/views/NoTaskView',
              systemCode: '727007',
              statusCd: '1000',
              firstLetter: 'JTYX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'RWGL_MENU_TASK002_1',
            },
            {
              privId: 1822048,
              menuId: 3610509,
              menuName: '角色模板配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 3610501,
              menuIndex: 8,
              urlAddr: '/customize/role',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'JSMBPZ',
              iconUrl: 'icon-gene-man-manager',
              menuCode: 'TYMH_MENU_JSMBPZ',
            },
            {
              privId: 992185,
              menuId: 2770391,
              menuName: '结算查询',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770390,
              menuIndex: 1,
              urlAddr:
                'mrssweb/modules/ruleland/settledeatilfirekylinquery/views/SettleDeatilFirekylinQueryView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'JSCX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_SettleDeatilFirekylinQuery',
            },
            {
              privId: 992011,
              menuId: 2780373,
              menuName: '角色管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780365,
              menuIndex: 2,
              urlAddr: '/authManage/roleManage',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'JSGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_JSGL',
            },
          ],
        },
        {
          title: 'K',
          menus: [
            {
              privId: 88958,
              menuId: 276737,
              menuName: '客户',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 5,
              menuDesc: '客户',
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'KH',
              iconUrl: 'main_cust',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_CUST',
            },
            {
              privId: 992247,
              menuId: 2770467,
              menuName: '客户认领',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769961,
              menuIndex: 4,
              urlAddr:
                '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocClaimView',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'KHRL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_LOC_CLAIM',
            },
            {
              privId: 992248,
              menuId: 2770468,
              menuName: '客户争议',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769961,
              menuIndex: 5,
              urlAddr:
                '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocDisputeView',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'KHZY',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_LOC_DISPUTE',
            },
            {
              privId: 992022,
              menuId: 2770224,
              menuName: '客户中心稽核',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770222,
              menuIndex: 1,
              menuDesc: '客户中心稽核',
              urlAddr: 'audit-web/audit/modules/custinfo/views/CustInfoMainView.js',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'KHZXJH',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_AUDIT_CUST01',
            },
            {
              privId: 88981,
              menuId: 276761,
              menuName: '客户任务',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274651,
              parMenuName: '客户中心',
              menuIndex: 2,
              urlAddr:
                '[iframe]http://122.192.9.52:20121/operator/dist/index.html#/cust/auditManage/custTask?viewMode=inner&language=zh-CN',
              systemCode: '727002',
              statusCd: '1000',
              firstLetter: 'KHRW',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'KGXT_MENU_115',
            },
            {
              privId: 89200,
              menuId: 277019,
              menuName: '勘察任务',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 277018,
              menuIndex: 4,
              menuDesc: '查勘任务',
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=2881',
              systemCode: '727007',
              statusCd: '1000',
              firstLetter: 'KCRW',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'RWGL_MENU_TASK010_1',
            },
            {
              privId: 992291,
              menuId: 2770509,
              menuName: '客户综合看板',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274651,
              parMenuName: '客户中心',
              menuIndex: 9,
              urlAddr:
                '[iframe]http://122.192.9.52:20121/operator/dist/index.html#/cust/board/index?viewMode=inner&language=zh-CN',
              systemCode: '727002',
              statusCd: '1000',
              firstLetter: 'KHZHKB',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'KGXT_MENU_100101',
            },
            {
              privId: 89124,
              menuId: 276912,
              menuName: '客户任务',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276895,
              menuIndex: 1,
              menuDesc: '客户任务',
              urlAddr:
                '[iframe]http://122.192.9.52:20121/operator/dist/index.html#/cust/auditManage/custTask?viewMode=inner&language=zh-CN',
              systemCode: '727007',
              statusCd: '1000',
              firstLetter: 'KHRW',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'RWGL_MENU_TASK005_1',
            },
          ],
        },
        {
          title: 'L-P',
          menus: [
            {
              privId: 992041,
              menuId: 2770241,
              menuName: '模板任务单',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276952,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=2140',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'MBRWD',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_GDGL_2',
            },
            {
              privId: 2792042,
              menuId: 4580502,
              menuName: '模板属性管理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769981,
              menuIndex: 7,
              menuDesc: '属性规格管理',
              urlAddr:
                '[iframe]http://172.16.81.126:9999/cpc-web/iframe.html?iotcmp/modules/dynamicattrmgr/views/AttrMgrView&sysUserCode={SYS_USER_CODE}',
              systemCode: '756142',
              statusCd: '1000',
              firstLetter: 'MBSXGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'CPC_MENU_0007',
            },
          ],
        },
        {
          title: 'Q',
          menus: [
            {
              privId: 991751,
              menuId: 2769943,
              menuName: '企业分析',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769926,
              menuIndex: 2,
              urlAddr: '[iframe]http://************:8485/analysis/enterprise',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'QYFX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_QYFX',
            },
            {
              privId: 89021,
              menuId: 276813,
              menuName: '潜在客户',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274651,
              parMenuName: '客户中心',
              menuIndex: 1,
              urlAddr:
                '[iframe]http://122.192.9.52:20121/operator/dist/index.html#/cust/manage/potentialcustInfo?viewMode=inner&language=zh-CN',
              systemCode: '727002',
              statusCd: '1000',
              firstLetter: 'QZKH',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'KGXT_MENU_11',
            },
            {
              privId: 1002040,
              menuId: 2790500,
              menuName: '区域管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780371,
              menuIndex: 2,
              urlAddr: '/orgManage/areaManage',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'QYGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_QYGL',
            },
            {
              privId: 992097,
              menuId: 2770303,
              menuName: '清单入库明细配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770262,
              menuIndex: 1,
              urlAddr: 'mrssweb/modules/ruleland/drConfig/views/drConfigDetail',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'QDRKMXPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_DR_CONFIG',
            },
            {
              privId: 992176,
              menuId: 2770381,
              menuName: '前台通知信息',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770292,
              menuIndex: 5,
              urlAddr:
                'mrssweb/modules/warndataproducer/frontNoticeInst/views/frontNoticeInstView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'QTTZXX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_frontNoticeInst',
            },
            {
              privId: 992171,
              menuId: 2770372,
              menuName: '企业首页',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769926,
              menuIndex: 1,
              urlAddr: '[iframe]http://************:8485/businessPortraits/index',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'QYSY',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_QYSY',
            },
            {
              privId: 992172,
              menuId: 2770373,
              menuName: '企业列表',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769926,
              menuIndex: 4,
              urlAddr: '[iframe]http://************:8485/marketOpportunity/enterpriseInfoBase',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'QYLB',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_QYLB',
            },
          ],
        },
        {
          title: 'R',
          menus: [
            {
              privId: 88963,
              menuId: 276742,
              menuName: '任务',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 10,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'RW',
              iconUrl: 'main_task',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_TASKMENU',
            },
            {
              privId: 1992042,
              menuId: 3780502,
              menuName: '任务管理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780364,
              menuIndex: 11,
              urlAddr: '/TaskManage',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'RWGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_TASKMANAGE',
            },
          ],
        },
        {
          title: 'S',
          menus: [
            {
              privId: 992023,
              menuId: 2770226,
              menuName: '属性规格管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770225,
              menuIndex: 3,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=38',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'SXGGGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DD_SXGGGL',
            },
            {
              privId: 3212046,
              menuId: 5190511,
              menuName: '商机审核岗位管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190503,
              menuIndex: 4,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=356',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'SJSHGWGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_SALES_004',
            },
            {
              privId: 3212045,
              menuId: 5190510,
              menuName: '商机环节处理配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190503,
              menuIndex: 3,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=396',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'SJHJCLPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_SALES_003',
            },
            {
              privId: 3212044,
              menuId: 5190509,
              menuName: '商机产品模版管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190503,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=823',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'SJCPMBGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_SALES_002',
            },
            {
              privId: 3212043,
              menuId: 5190508,
              menuName: '商机属性规格管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190503,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=821',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'SJSXGGGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_SALES_001',
            },
            {
              privId: 992087,
              menuId: 2770291,
              menuName: '数据源管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770287,
              menuIndex: 2,
              urlAddr: 'mrssweb/modules/receive/cmpt/views/ReceivePdbView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'SJYGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_PDB',
            },
            {
              privId: 992042,
              menuId: 2770242,
              menuName: '属性规格管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276956,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bcm-web/ssoLogin?menuCode=821',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'SXGGGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DTSXGL_1',
            },
            {
              privId: 3042042,
              menuId: 4990503,
              menuName: '属性管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 4990502,
              menuIndex: 1,
              urlAddr:
                '[iframe]http://122.192.9.52:20121/project/dist/index.html#/attr/attrMain?viewMode=inner&language=zh-CN',
              systemCode: '727015',
              statusCd: '1000',
              firstLetter: 'SXGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'PROJECT_MENU_402',
            },
            {
              privId: 88890,
              menuId: 276629,
              menuName: '商机',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 2,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'SJ',
              iconUrl: 'tool_menu_business',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_MY_BUSINESS',
            },
            {
              privId: 89123,
              menuId: 276911,
              menuName: '生日关怀',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276894,
              menuIndex: 9,
              menuDesc: '生日关怀',
              urlAddr: 'modules/routinetask/views/NoTaskView',
              systemCode: '727007',
              statusCd: '1000',
              firstLetter: 'SRGH',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'RWGL_MENU_TASK004_1',
            },
            {
              privId: 89125,
              menuId: 276913,
              menuName: '商机任务',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276896,
              menuIndex: 3,
              menuDesc: '商机任务',
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=2902',
              systemCode: '727007',
              statusCd: '1000',
              firstLetter: 'SJRW',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'RWGL_MENU_TASK006_1',
            },
            {
              privId: 992170,
              menuId: 2770371,
              menuName: '数据源采集结果展示',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770287,
              menuIndex: 3,
              urlAddr: 'mrssweb/modules/receive/cmpt/views/ReceiveDealLogView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'SJYCJJGZS',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_RECEIVE_DEAL_LOG',
            },
            {
              privId: 86144,
              menuId: 274672,
              menuName: '审批人设置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274652,
              parMenuName: '审批管理',
              urlAddr: '[iframe]http://122.192.9.52:20121/operator/cust/approveSet.html',
              systemCode: '727002',
              statusCd: '1000',
              firstLetter: 'SPRSZ',
              iconUrl: 'icon-gene-man-manager',
              menuCode: 'KGXT_10006',
            },
            {
              privId: 89169,
              menuId: 276989,
              menuName: '属性规格管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276988,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=821',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'SXGGGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_8',
            },
          ],
        },
        {
          title: 'T-V',
          menus: [
            {
              privId: 1612042,
              menuId: 3400504,
              menuName: '团队角色管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 3400503,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=3000',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'TDJSGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_TEAM_ROLE',
            },
            {
              privId: 991737,
              menuId: 2769928,
              menuName: '特殊分群管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770043,
              menuIndex: 5,
              menuDesc: '特殊分群管理',
              urlAddr: '[iframe]http://122.192.9.52:20121/ire/specialGroup/?sso=1',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'TSFQGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_TeShuFenQun',
            },
            {
              privId: 2202044,
              menuId: 3990506,
              menuName: '团队管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 3400503,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=3200',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'TDGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_TEAM',
            },
            {
              privId: 992174,
              menuId: 2770375,
              menuName: '通知信息',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770292,
              menuIndex: 3,
              urlAddr: 'mrssweb/modules/warndataproducer/noticeInfo/views/noticeInfoView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'TZXX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_noticeInfoView',
            },
          ],
        },
        {
          title: 'W',
          menus: [
            {
              privId: 88968,
              menuId: 276747,
              menuName: '网络测速',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 15,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'WLCS',
              iconUrl: 'home_toker_test_speed',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_SPEEDTEST',
            },
            {
              privId: 89209,
              menuId: 277029,
              menuName: '我的勘察',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 277020,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=2901',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'WDKC',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_01',
            },
            {
              privId: 992227,
              menuId: 2770447,
              menuName: '我的工单',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769981,
              menuIndex: 6,
              menuDesc: '工单查询和处理',
              urlAddr:
                '[iframe]http://172.16.81.126:9999/cpc-web/iframe.html?pc/modules/orderwork/views/OrderInfo&sysUserCode={SYS_USER_CODE}',
              systemCode: '756142',
              statusCd: '1000',
              firstLetter: 'WDGD',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'CPC_MENU_0006',
            },
            {
              privId: 992088,
              menuId: 2770293,
              menuName: '网元配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770292,
              menuIndex: 1,
              urlAddr: 'mrssweb/modules/analysis/decodeCfg/views/decodeCfgView.js',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'WYPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_DECODE_CFG',
            },
            {
              privId: 991802,
              menuId: 2770002,
              menuName: '网格管理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769961,
              menuIndex: 3,
              menuDesc: '网格管理',
              urlAddr:
                '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/grid-management/views/gridManageView',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'WGGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_GRID',
            },
            {
              privId: 992123,
              menuId: 2770321,
              menuName: '我的预受理单',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276816,
              menuIndex: 141,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=2970',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'WDYSLD',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_YSLD',
            },
            {
              privId: 86152,
              menuId: 274659,
              menuName: '我的客户',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274651,
              parMenuName: '客户中心',
              menuIndex: 1,
              urlAddr:
                '[iframe]http://122.192.9.52:20121/operator/dist/index.html#/cust/manage/custInfo?viewMode=inner&language=zh-CN',
              systemCode: '727002',
              statusCd: '1000',
              firstLetter: 'WDKH',
              iconUrl: 'icon-gene-man-manager',
              menuCode: 'KGXT_MENU_1005',
            },
            {
              privId: 992179,
              menuId: 2770384,
              menuName: '网内规则配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770301,
              menuIndex: 3,
              urlAddr: 'mrssweb/modules/rewardpolicy/netsettlerule/views/SettleRuleView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'WNGZPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_SettleRuleView',
            },
            {
              privId: 992180,
              menuId: 2770385,
              menuName: '网内酬金规则实例',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770301,
              menuIndex: 4,
              urlAddr:
                'mrssweb/modules/rewardpolicy/netinstsettleruleissue/views/InstSettleRuleIssueView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'WNCJGZSL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_ISRuleIssueView',
            },
            {
              privId: 89165,
              menuId: 276984,
              menuName: '我的线索',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276789,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=3100',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'WDXS',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_2',
            },
            {
              privId: 89166,
              menuId: 276985,
              menuName: '我的商机',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276789,
              menuIndex: 3,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=3040',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'WDSJ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_3',
            },
          ],
        },
        {
          title: 'X',
          menus: [
            {
              privId: 88965,
              menuId: 276744,
              menuName: '消息',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 12,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'XX',
              iconUrl: 'ic_home_message',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_MESSAGE',
            },
            {
              privId: 1812042,
              menuId: 3600502,
              menuName: '系统信息配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 3590501,
              menuIndex: 2,
              urlAddr: '/configManage/infoConfig',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'XTXXPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XTINFOCONFIG',
            },
            {
              privId: 1812041,
              menuId: 3600501,
              menuName: '系统操作日志',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780364,
              menuIndex: 9,
              urlAddr: '/systemOperLog',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'XTCZRZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XTCZRZ',
            },
            {
              privId: 1802041,
              menuId: 3590502,
              menuName: '系统参数配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 3590501,
              menuIndex: 1,
              urlAddr: '/configManage/paramsConfig',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'XTCSPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_PARAMSCONFIG',
            },
            {
              privId: 992025,
              menuId: 2780485,
              menuName: '下载中心',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780364,
              menuIndex: 7,
              urlAddr: '/download',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'XZZX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XTGL_XZZX',
            },
            {
              privId: 88897,
              menuId: 276636,
              menuName: '线索',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 9,
              menuDesc: '\n',
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'XS',
              iconUrl: 'order_trace_icon',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_CLUE',
            },
            {
              privId: 992006,
              menuId: 2780367,
              menuName: '系统公告',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780364,
              menuIndex: 2,
              urlAddr: '/bulletin',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'XTGG',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XTGG',
            },
            {
              privId: 992009,
              menuId: 2780370,
              menuName: '统一消息',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780364,
              menuIndex: 5,
              urlAddr: '/notice',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'XXZX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_XXZX',
            },
          ],
        },
      ],
      [
        {
          title: 'Y',
          menus: [
            {
              privId: 88969,
              menuId: 276748,
              menuName: '业务办理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 16,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'YWBL',
              iconUrl: 'quick_order_btn',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_QUICKORDER',
            },
            {
              privId: 89193,
              menuId: 277011,
              menuName: '业务办理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 276975,
              menuIndex: 4,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=3322',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'YWBL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_YWBL',
            },
            {
              privId: 3212041,
              menuId: 5190506,
              menuName: '引入产品配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 5190502,
              menuIndex: 1,
              urlAddr:
                '[iframe]http://172.16.81.126:9999/cpc-web/iframe.html?pc/modules/offerConfig/views/ProductLeadInMgr&sysUserCode={SYS_USER_CODE}',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'YRCPPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_CPC_001',
            },
            {
              privId: 992093,
              menuId: 2770298,
              menuName: '要素提取',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770264,
              menuIndex: 5,
              urlAddr: 'mrssweb/modules/factor/factorEnhance/views/factorEnhance',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'YSTQ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_FACTOR_ENHANCE',
            },
            {
              privId: 992091,
              menuId: 2770296,
              menuName: '业务小类',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770264,
              menuIndex: 3,
              urlAddr: 'mrssweb/modules/factor/eventtype/views/EventTypeView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'YWXL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_EVENT_TYPE',
            },
            {
              privId: 992092,
              menuId: 2770297,
              menuName: '业务类型',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770264,
              menuIndex: 4,
              urlAddr: 'mrssweb/modules/factor/servicelist/views/ServiceListView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'YWLX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_SERVICE_LIST',
            },
            {
              privId: 992085,
              menuId: 2770289,
              menuName: '要素管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770264,
              menuIndex: 1,
              urlAddr: 'mrssweb/modules/factor/eventattr/views/EventAttrView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'YSGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_EVENT_ATTR',
            },
            {
              privId: 88895,
              menuId: 276634,
              menuName: '营销沙盘',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 7,
              urlAddr: 'none',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'YXSP',
              iconUrl: 'main_indicator',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_INDICATOR',
            },
            {
              privId: 992121,
              menuId: 2770309,
              menuName: '预受理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 33,
              menuDesc: '预受理',
              urlAddr: 'modules/menu/views/MenuManagementView',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'YSL',
              iconUrl: 'menu_pre_accept',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_ACCEPT',
            },
            {
              privId: 1822041,
              menuId: 3610502,
              menuName: '用户模板管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 3610501,
              menuIndex: 1,
              urlAddr: '/customize/user',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'YHMBGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_YHMBPZ',
            },
            {
              privId: 992128,
              menuId: 2770328,
              menuName: '引入产品配置',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769981,
              menuIndex: 1,
              menuDesc: '引入产品配置',
              urlAddr:
                '[iframe]http://172.16.81.126:9999/cpc-web/iframe.html?pc/modules/offerConfig/views/ProductLeadInMgr&sysUserCode={SYS_USER_CODE}',
              systemCode: '756142',
              statusCd: '1000',
              firstLetter: 'YRCPPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'CPC_MENU_0003',
            },
            {
              privId: 1492040,
              menuId: 3290500,
              menuName: '用户管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780371,
              menuIndex: 4,
              urlAddr: '/orgManage/userManage',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'YHGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_YHGL',
            },
            {
              privId: 991681,
              menuId: 2769861,
              menuName: '业务变更',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769885,
              menuIndex: 2,
              urlAddr: '[iframe]http://122.192.9.52:20121/order-web/ssoLogin?menuCode=43',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'YWBG',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_DDLR',
            },
            {
              privId: 992182,
              menuId: 2770387,
              menuName: '因子管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770301,
              menuIndex: 6,
              urlAddr: 'mrssweb/modules/factor/factordefinition/views/FactorDefinitionView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'YZGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_FactorDefinitionV',
            },
            {
              privId: 992010,
              menuId: 2780372,
              menuName: '员工管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780371,
              menuIndex: 1,
              urlAddr: '/orgManage/memberManage',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'YGGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_YGGL',
            },
          ],
        },
        {
          title: 'Z',
          menus: [
            {
              privId: 992249,
              menuId: 2770469,
              menuName: '争议工单处理',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769961,
              menuIndex: 6,
              urlAddr:
                '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocTaskView',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZYGDCL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_LOC_TASK',
            },
            {
              privId: 991753,
              menuId: 2769945,
              menuName: '资产分析',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769926,
              menuIndex: 3,
              urlAddr: '[iframe]http://************:8485/analysis/assets',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZCFX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ZCFX',
            },
            {
              privId: 891502,
              menuId: 2769682,
              menuName: '主题管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769925,
              menuIndex: 3,
              urlAddr: '[iframe]http://************:8485/basic/themeManage/list',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZTGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_SPIDERCONF',
            },
            {
              privId: 891503,
              menuId: 2769683,
              menuName: '主题执行详情',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769925,
              menuIndex: 4,
              urlAddr: '[iframe]http://************:8485/basic/runDetail/list',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZTZXXQ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_TIMINGTASK',
            },
            {
              privId: 891504,
              menuId: 2769684,
              menuName: '站点模板',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2769925,
              menuIndex: 5,
              urlAddr: '[iframe]http://************:8485/basic/site',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZDMB',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_RULE',
            },
            {
              privId: 1202040,
              menuId: 2990500,
              menuName: '组织管理',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2780371,
              menuIndex: 3,
              urlAddr: '/orgManage/orgnzManage',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZZGL',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ZZGL2',
            },
            {
              privId: 992152,
              menuId: 2770354,
              menuName: '知识库',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 274744,
              parMenuName: 'APP菜单',
              menuIndex: 35,
              menuDesc: '知识库',
              urlAddr: 'modules/menu/views/MenuManagementView',
              systemCode: '727004',
              statusCd: '1000',
              firstLetter: 'ZSK',
              iconUrl: 'menu_knowledge',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'APP0_MENU_KNOWLEDGE',
            },
            {
              privId: 992153,
              menuId: 2770356,
              menuName: '资讯订阅',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770355,
              menuIndex: 3,
              urlAddr: '[iframe]http://************:8485/marketOpportunity/subscriptionManage',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZXDY',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_QYSHY',
            },
            {
              privId: 992154,
              menuId: 2770357,
              menuName: '资讯首页',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770355,
              menuIndex: 1,
              urlAddr: '[iframe]http://************:8485/businessPortraits/tenderAnalysis',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZXSY',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ZTBFX',
            },
            {
              privId: 992099,
              menuId: 2770305,
              menuName: '知识库',
              menuLevel: 1,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770304,
              menuIndex: 1,
              urlAddr: '[iframe]http://************:9004/#/knowledgebase/info',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZSK',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_KDB',
            },
            {
              privId: 992124,
              menuId: 2770323,
              menuName: '知识库',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770322,
              menuIndex: 1,
              urlAddr: '[iframe]http://122.192.9.52:20121/bsc-web/ssoLogin?menuCode=2940',
              systemCode: '727006',
              statusCd: '1000',
              firstLetter: 'ZSK',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SALES_MENU_KNOWLEDGE_BASE',
            },
            {
              privId: 89151,
              menuId: 276965,
              menuName: '资讯详情',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770355,
              menuIndex: 2,
              urlAddr: '[iframe]http://************:8485/marketOpportunity/BiddingMsg',
              systemCode: '727001',
              statusCd: '1000',
              firstLetter: 'ZXXQ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'TYMH_MENU_ANALYSIS_ZB',
            },
            {
              privId: 992173,
              menuId: 2770374,
              menuName: '账单入库明细配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770262,
              menuIndex: 2,
              urlAddr: 'mrssweb/modules/ruleland/sdColConfig/views/sdConfig',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'ZDRKMXPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_sdConfig',
            },
            {
              privId: 992191,
              menuId: 2770398,
              menuName: '字典配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770395,
              menuIndex: 2,
              urlAddr: 'mrssweb/modules/dict/sysdict/views/sysdict',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'ZDPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_sysdict',
            },
            {
              privId: 992192,
              menuId: 2770399,
              menuName: '字典sql配置',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770395,
              menuIndex: 2,
              urlAddr: 'mrssweb/modules/dict/syssql/views/syssql',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'ZDSQLPZ',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_syssql',
            },
            {
              privId: 992190,
              menuId: 2770397,
              menuName: 'zcache查询',
              menuLevel: 2,
              menuType: '1100',
              menuTypeName: '叶子菜单',
              parMenuId: 2770395,
              menuIndex: 1,
              urlAddr: 'mrssweb/modules/zcache/views/ZcacheView',
              systemCode: '727012',
              statusCd: '1000',
              firstLetter: 'ZCACHECX',
              iconUrl: 'icon-gene-man-manager',
              paramEncryptType: '1000',
              menuOpenMode: '1',
              menuCode: 'SETTLE_MENU_ZcacheView',
            },
          ],
        },
      ],
    ]);
  },
  'GET /orgauth/SysuserCollectionController/getCollectionMenu.do': (req, res) => {
    res.send([
      {
        id: 866662,
        createDate: '2020-03-25 11:15:35',
        createStaff: 5279893,
        updateDate: '2020-03-25 11:15:35',
        collectId: 866662,
        sysUserId: 5279893,
        collectStaff: 5279893,
        contentType: '1001',
        contentId: 2780368,
        collectSort: 1,
        statusCd: '1000',
        menuName: '公告管理',
        urlAddr: '/bulletin/manage',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        parMenuId: 2780364,
        menuCode: 'TYMH_MENU_GGGL',
      },
      {
        id: 866663,
        createDate: '2020-03-25 11:15:35',
        createStaff: 5279893,
        updateDate: '2020-03-25 11:15:35',
        collectId: 866663,
        sysUserId: 5279893,
        collectStaff: 5279893,
        contentType: '1001',
        contentId: 4380504,
        collectSort: 2,
        statusCd: '1000',
        menuName: 'APP快捷菜单模板',
        urlAddr: '/customize/appShortcutMenu',
        iconUrl: 'icon-gene-man-manager',
        parMenuId: 3610501,
        menuCode: 'TYMH_MENU_APPSHORTCUTMENU',
      },
      {
        id: 866664,
        createDate: '2020-03-25 11:15:35',
        createStaff: 5279893,
        updateDate: '2020-03-25 11:15:35',
        collectId: 866664,
        sysUserId: 5279893,
        collectStaff: 5279893,
        contentType: '1001',
        contentId: 3610509,
        collectSort: 3,
        statusCd: '1000',
        menuName: '角色模板配置',
        urlAddr: '/customize/role',
        iconUrl: 'icon-gene-man-manager',
        parMenuId: 3610501,
        menuCode: 'TYMH_MENU_JSMBPZ',
      },
      {
        id: 866665,
        createDate: '2020-03-25 11:15:35',
        createStaff: 5279893,
        updateDate: '2020-03-25 11:15:35',
        collectId: 866665,
        sysUserId: 5279893,
        collectStaff: 5279893,
        contentType: '1001',
        contentId: 3610502,
        collectSort: 4,
        statusCd: '1000',
        menuName: '用户模板管理',
        urlAddr: '/customize/user',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        parMenuId: 3610501,
        menuCode: 'TYMH_MENU_YHMBPZ',
      },
    ]);
  },
  'GET /orgauth/SysuserRecentMenuController/getRecentMenuInfo.do': (req, res) => {
    res.send([
      {
        id: 2727371,
        sysUserId: 1,
        postId: 92550,
        recentContent: '276104',
        recentType: '1',
        accessDate: '2019-12-08 11:44:48',
        iconUrl: 'icon-gene-view-module',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '角色模板配置',
        urlAddr: 'portal/modules/templet/views/TempletConfigView?confType=1',
        menuCode: 'TYMH_MENU_025',
        recentMenuId: 2727371,
      },
      {
        id: 2727123,
        sysUserId: 1,
        postId: 92550,
        recentContent: '276105',
        recentType: '1',
        accessDate: '2019-11-03 19:04:24',
        iconUrl: 'icon-gene-person-border-',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '用户模板配置',
        urlAddr: 'portal/modules/templet/views/UserTempletConfigView?confType=1',
        menuCode: 'TYMH_MENU_901',
        recentMenuId: 2727123,
      },
      {
        id: 2727097,
        sysUserId: 1,
        postId: 92550,
        recentContent: '276980',
        recentType: '1',
        accessDate: '2019-11-01 16:13:13',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '订单查询',
        urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3022',
        menuCode: 'TYMH_MENU_DDCX',
        recentMenuId: 2727097,
      },
      {
        id: 2727096,
        sysUserId: 1,
        postId: 92550,
        recentContent: '2769888',
        recentType: '1',
        accessDate: '2019-11-01 16:08:23',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '订单模板管理',
        urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3122',
        menuCode: 'TYMH_MENU_DDMB',
        recentMenuId: 2727096,
      },
      {
        id: 2727095,
        sysUserId: 1,
        postId: 92550,
        recentContent: '2769886',
        recentType: '1',
        accessDate: '2019-11-01 16:07:46',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '订单超时监控',
        urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3123',
        menuCode: 'TYMH_MENU_DDCSJK',
        recentMenuId: 2727095,
      },
      {
        id: 2727075,
        sysUserId: 1,
        postId: 92550,
        recentContent: '2769682',
        recentType: '1',
        accessDate: '2019-11-01 13:56:38',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '主题管理',
        urlAddr: '[iframe]http://10.45.47.16:8491/basic/themeManage/list',
        menuCode: 'TYMH_MENU_SPIDERCONF',
        recentMenuId: 2727075,
      },
      {
        id: 2726974,
        sysUserId: 1,
        postId: 92550,
        recentContent: '276116',
        recentType: '1',
        accessDate: '2019-11-01 12:24:01',
        iconUrl: 'icon-gene-radio-button-un',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '菜单管理',
        urlAddr: 'modules/menu/views/MenuManagementView',
        menuCode: 'TYMH_MENU_022',
        recentMenuId: 2726974,
      },
      {
        id: 2727006,
        sysUserId: 1,
        postId: 92550,
        recentContent: '2769936',
        recentType: '1',
        accessDate: '2019-11-01 10:25:59',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '沙盘地图',
        urlAddr:
          '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/operations/views/OperationsView',
        menuCode: 'MKT0_MENU_ZZZX',
        recentMenuId: 2727006,
      },
      {
        id: 2727007,
        sysUserId: 1,
        postId: 92550,
        recentContent: '2769946',
        recentType: '1',
        accessDate: '2019-10-31 21:27:14',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '行客视图',
        urlAddr:
          '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/gov-geer-region-portrait-day/views/IndexView',
        menuCode: 'MKT0_MENU_HANGKE',
        recentMenuId: 2727007,
      },
      {
        id: 2726996,
        sysUserId: 1,
        postId: 92550,
        recentContent: '2769963',
        recentType: '1',
        accessDate: '2019-10-31 17:53:42',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuName: '划配规则管理',
        urlAddr:
          '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocRuleView',
        menuCode: 'TYMH_MENU_RULE_MGR',
        recentMenuId: 2726996,
      },
    ]);
  },
  'GET /orgauth/FuncMenuController/selectMenuFromSession.do': (req, res) => {
    res.status(200).send([
      {
        menuId: 5240502,
        menuName: '页面样例-react',
        menuLevel: 0,
        // 菜单类型, 1000：目录菜单，1100：叶子菜单
        menuType: '1000',
        menuTypeName: '目录菜单',
        // 父节点menuid。0表示根节点
        parMenuId: 0,
        // 菜单排序
        menuIndex: 0,
        menuDesc: 'REACT',
        urlAddr: 'Views',
        statusCd: '1000',
        firstLetter: 'YMYLREACT',
        iconUrl: 'icon-gene-man-manager',
        menuCode: 'TYMH_MENU_YMYLREACT',
        // 打开方式 1：工作台标签页；2:弹窗窗口; 3:新开浏览器; 4: 浏览器新标签
        menuOpenMode: '1',
        // 加密方式 1000:不加密  1001：MD5加密
        paramEncryptType: '1000',
      },
      {
        "privId": 1740207447079321214,
        "menuId": 1740207447079322131,
        "menuName": "节假日",
        "menuLevel": 2,
        "menuType": "1100",
        "menuTypeName": "叶子菜单",
        "parMenuId": 1740207334079318,
        "menuIndex": 1,
        "urlAddr": "/Holiday",
        "systemCode": "727001",
        "statusCd": "1000",
        "firstLetter": "JTKHGL",
        "menuCode": "TYMH_MENU_XTGL_Holidaye",
        "ifAsync": "1100",
        "showFlag": "1000"
      },
      {
        menuId: 2780364,
        menuName: '系统管理-react',
        menuLevel: 0,
        menuType: '1000',
        menuTypeName: '目录菜单',
        parMenuId: 0,
        menuIndex: 1,
        menuDesc: '系统管理-react',
        statusCd: '1000',
        firstLetter: 'XTGLREACT',
        iconUrl: 'icon-gene-man-manager',
        menuCode: 'TYMH_MENU_XTGL_REACT',
        ifMain: false,
      },
      {
        "menuId": 1740207334079318,
        "menuName": "集团客户",
        "menuLevel": 1,
        "menuType": "1000",
        "menuTypeName": "目录菜单",
        "parMenuId": 2780364,
        "menuIndex": 34,
        "systemCode": "727001",
        "statusCd": "1000",
        "firstLetter": "JTKH",
        "menuCode": "TYMH_MENU_XTGL_GroupCust",
        "ifMain": false
      },
      {
        "privId": 174020744707932123,
        "menuId": 174020744707932212,
        "menuName": "话费收取",
        "menuLevel": 2,
        "menuType": "1100",
        "menuTypeName": "叶子菜单",
        "parMenuId": 1740207334079318,
        "menuIndex": 1,
        "urlAddr": "/BillCollection",
        "systemCode": "727001",
        "statusCd": "1000",
        "firstLetter": "JTKHGL",
        "menuCode": "TYMH_MENU_XTGL_GroupCustManage",
        "ifAsync": "1100",
        "showFlag": "1000"
      },
      {
        "privId": 174020744707932124,
        "menuId": 174020744707932213,
        "menuName": "账户信息查询",
        "menuLevel": 2,
        "menuType": "1100",
        "menuTypeName": "叶子菜单",
        "parMenuId": 1740207334079318,
        "menuIndex": 1,
        "urlAddr": "/AcctInfoView",
        "systemCode": "727001",
        "statusCd": "1000",
        "firstLetter": "JTKHGL",
        "menuCode": "TYMH_MENU_XTGL_GroupCustManage",
        "ifAsync": "1100",
        "showFlag": "1000"
      },
      {
        "privId": 17402074470793212311,
        "menuId": 17402074470793221211,
        "menuName": "集团证件管理",
        "menuLevel": 2,
        "menuType": "1100",
        "menuTypeName": "叶子菜单",
        "parMenuId": 1740207334079318,
        "menuIndex": 1,
        "urlAddr": "/GroupCertManage",
        "systemCode": "727001",
        "statusCd": "1000",
        "firstLetter": "JTKHGL",
        "menuCode": "TYMH_MENU_XTGL_GroupCustManage",
        "ifAsync": "1100",
        "showFlag": "1000"
      },
      {
        "privId": 1740207447079323,
        "menuId": 1740207447079322,
        "menuName": "新建客户",
        "menuLevel": 2,
        "menuType": "1100",
        "menuTypeName": "叶子菜单",
        "parMenuId": 1740207334079318,
        "menuIndex": 1,
        "urlAddr": "/GroupCust/GroupCustManage/add",
        "systemCode": "727001",
        "statusCd": "1000",
        "firstLetter": "JTKHGL",
        "menuCode": "TYMH_MENU_XTGL_GroupCustManage",
        "ifAsync": "1100",
        "showFlag": "1000"
      },
      {
        "privId": 1740207492079328,
        "menuId": 1740207492079327,
        "menuName": "集团客户视图",
        "menuLevel": 2,
        "menuType": "1100",
        "menuTypeName": "叶子菜单",
        "parMenuId": 1740207334079318,
        "menuIndex": 2,
        "urlAddr": "/GroupCust/GroupCustView",
        "systemCode": "727001",
        "statusCd": "1000",
        "firstLetter": "JTKHST",
        "menuCode": "TYMH_MENU_XTGL_GroupCustView",
        "ifAsync": "1100",
        "showFlag": "1100"
      },
      {
        menuId: 12780365,
        menuName: '工作台配置',
        menuLevel: 1,
        menuType: '1000',
        menuTypeName: '目录菜单',
        parMenuId: 2780364,
        menuIndex: 1,
        menuDesc: '工作台管理',
        statusCd: '1000',
        firstLetter: 'GZTPZ',
        iconUrl: 'icon-gene-man-manager',
        menuCode: 'TYMH_MENU_GZTPZ',
        ifMain: false,
      },
      {
        privId: 992005,
        menuId: 22780366,
        menuName: '用户模板配置',
        menuLevel: 2,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 12780365,
        menuIndex: 1,
        urlAddr: '/customize/user',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'YHMBPZ',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_YHMBPZ',
      },
      {
        privId: 9920053,
        menuId: 232780366,
        menuName: '角色模板配置',
        menuLevel: 2,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 12780365,
        menuIndex: 1,
        urlAddr: '/customize/role',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'JSMBPZ',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_YHMBPZ',
      },
      {
        menuId: 2780365,
        menuName: '权限管理',
        menuLevel: 1,
        menuType: '1000',
        menuTypeName: '目录菜单',
        parMenuId: 2780364,
        menuIndex: 1,
        urlAddr: 'modules/menu/views/MenuManagementView',
        statusCd: '1000',
        firstLetter: 'QXGL',
        iconUrl: 'icon-gene-man-manager',
        menuCode: 'TYMH_MENU_QXGL',
        ifMain: false,
      },
      {
        privId: 992005,
        menuId: 2780366,
        menuName: '菜单管理',
        menuLevel: 2,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780365,
        menuIndex: 1,
        urlAddr: '/authManage/menuManage',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'CDGL',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_CDGL',
      },
      {
        privId: 992011,
        menuId: 2780373,
        menuName: '角色管理',
        menuLevel: 2,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780365,
        menuIndex: 2,
        urlAddr: '/authManage/roleManage',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'JSGL',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_JSGL',
      },
      {
        privId: 992017,
        menuId: 2780379,
        menuName: '租户管理',
        menuLevel: 2,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780365,
        menuIndex: 2,
        urlAddr: '/authManage/tenantManage',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'JSGL',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_JSGL',
      },
      {
        privId: 992006,
        menuId: 2780367,
        menuName: '系统公告',
        menuLevel: 1,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780364,
        menuIndex: 2,
        urlAddr: '/bulletin',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'XTGG',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_XTGG',
      },
      {
        privId: 992009,
        menuId: 2780370,
        menuName: '统一消息',
        menuLevel: 1,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780364,
        menuIndex: 5,
        urlAddr: '/notice',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'XXZX',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_XXZX',
      },
      {
        privId: 992007,
        menuId: 2780368,
        menuName: '公告管理',
        menuLevel: 1,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780364,
        menuIndex: 3,
        urlAddr: '/bulletin/manage',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'GGGL',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_GGGL',
      },
      {
        privId: 992008,
        menuId: 2780369,
        menuName: '登录状态管理',
        menuLevel: 1,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780364,
        menuIndex: 4,
        urlAddr: '/account',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'DLZTGL',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_DLZTGL',
      },
      {
        privId: 992025,
        menuId: 2780485,
        menuName: '下载中心',
        menuLevel: 1,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780364,
        menuIndex: 7,
        urlAddr: '/download',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'XZZX',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_XTGL_XZZX',
      },
      {
        menuId: 2780371,
        menuName: '组织管理',
        menuLevel: 1,
        menuType: '1000',
        menuTypeName: '目录菜单',
        parMenuId: 2780364,
        menuIndex: 6,
        urlAddr: 'modules/menu/views/MenuManagementView',
        statusCd: '1000',
        firstLetter: 'ZZGL',
        iconUrl: 'icon-gene-man-manager',
        menuCode: 'TYMH_MENU_ZZGL',
        ifMain: false,
      },
      {
        privId: 992010,
        menuId: 2780372,
        menuName: '员工管理',
        menuLevel: 2,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780371,
        menuIndex: 1,
        urlAddr: '/orgManage/memberManage',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'YGGL',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_YGGL',
      },
      {
        privId: 1202040,
        menuId: 2990500,
        menuName: '组织管理',
        menuLevel: 2,
        menuType: '1100',
        menuTypeName: '叶子菜单',
        parMenuId: 2780371,
        menuIndex: 3,
        urlAddr: '/orgManage/orgnzManage',
        systemCode: '727001',
        statusCd: '1000',
        firstLetter: 'ZZGL',
        iconUrl: 'icon-gene-man-manager',
        paramEncryptType: '1000',
        menuOpenMode: '1',
        menuCode: 'TYMH_MENU_ZZGL2',
      },
    ]);
  },
  'post /orgauth/SysuserRecentMenuController/updateCollectionMenu.do': (req, res) => {
    res.status(200).send({
      resultCode: '0',
      resultObject: {},
    });
  },
};

export default delay(proxy, defaultSettings.delay);
